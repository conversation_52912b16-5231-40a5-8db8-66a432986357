<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * 性能分析工具 - ThinkPHP6企业级应用
 * 功能：分析代码性能、数据库查询、缓存使用、前端资源
 */

require_once dirname(__DIR__) . '/vendor/autoload.php';

use think\facade\Db;
use think\facade\Cache;

class PerformanceAnalyzer {
    
    private $projectRoot;
    private $issues = [];
    private $suggestions = [];
    
    public function __construct() {
        $this->projectRoot = dirname(__DIR__);
    }
    
    /**
     * 全面性能分析
     */
    public function fullAnalysis() {
        echo "⚡ 性能全面分析\n";
        echo str_repeat("=", 60) . "\n";
        
        $this->analyzeDatabasePerformance();
        $this->analyzeCacheUsage();
        $this->analyzeCodePerformance();
        $this->analyzeFrontendResources();
        
        $this->generatePerformanceReport();
    }
    
    /**
     * 数据库性能分析
     */
    public function analyzeDatabasePerformance() {
        echo "\n🗄️ 数据库性能分析\n";
        
        try {
            // 检查慢查询
            $slowQueries = $this->findSlowQueries();
            if (!empty($slowQueries)) {
                $this->addIssue('high', "发现 " . count($slowQueries) . " 个潜在慢查询");
                echo "⚠️ 潜在慢查询:\n";
                foreach ($slowQueries as $query) {
                    echo "  - {$query['file']}:{$query['line']} - {$query['query']}\n";
                }
            } else {
                echo "✅ 未发现明显的慢查询\n";
            }
            
            // 检查N+1查询问题
            $nPlusOneIssues = $this->findNPlusOneQueries();
            if (!empty($nPlusOneIssues)) {
                $this->addIssue('medium', "发现 " . count($nPlusOneIssues) . " 个N+1查询问题");
                echo "⚠️ N+1查询问题:\n";
                foreach ($nPlusOneIssues as $issue) {
                    echo "  - {$issue['file']}:{$issue['line']} - {$issue['description']}\n";
                }
            } else {
                echo "✅ 未发现N+1查询问题\n";
            }
            
            // 检查索引使用
            $indexIssues = $this->checkIndexUsage();
            if (!empty($indexIssues)) {
                $this->addIssue('medium', "发现 " . count($indexIssues) . " 个索引优化建议");
                echo "💡 索引优化建议:\n";
                foreach ($indexIssues as $issue) {
                    echo "  - {$issue}\n";
                }
            }
            
        } catch (\Exception $e) {
            echo "❌ 数据库分析失败: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * 缓存使用分析
     */
    public function analyzeCacheUsage() {
        echo "\n🚀 缓存使用分析\n";
        
        // 检查缓存配置
        $cacheConfig = $this->checkCacheConfig();
        if ($cacheConfig['redis_enabled']) {
            echo "✅ Redis缓存已启用\n";
        } else {
            $this->addIssue('medium', "Redis缓存未启用，建议配置Redis提升性能");
            echo "⚠️ Redis缓存未启用\n";
        }
        
        // 检查缓存使用情况
        $cacheUsage = $this->analyzeCacheUsageInCode();
        echo "📊 缓存使用统计:\n";
        echo "  - Cache::get() 调用: {$cacheUsage['get_calls']} 次\n";
        echo "  - Cache::set() 调用: {$cacheUsage['set_calls']} 次\n";
        echo "  - 缓存覆盖率: " . $this->calculateCacheCoverage($cacheUsage) . "%\n";
        
        if ($cacheUsage['get_calls'] < 5) {
            $this->addIssue('medium', "缓存使用较少，建议增加缓存策略");
        }
    }
    
    /**
     * 代码性能分析
     */
    public function analyzeCodePerformance() {
        echo "\n💻 代码性能分析\n";
        
        $performanceIssues = [];
        
        // 检查循环性能
        $loopIssues = $this->findPerformanceLoops();
        $performanceIssues = array_merge($performanceIssues, $loopIssues);
        
        // 检查内存使用
        $memoryIssues = $this->findMemoryIssues();
        $performanceIssues = array_merge($performanceIssues, $memoryIssues);
        
        // 检查文件操作
        $fileIssues = $this->findFileOperationIssues();
        $performanceIssues = array_merge($performanceIssues, $fileIssues);
        
        if (!empty($performanceIssues)) {
            echo "⚠️ 发现 " . count($performanceIssues) . " 个性能问题:\n";
            foreach ($performanceIssues as $issue) {
                echo "  - {$issue['file']}:{$issue['line']} - {$issue['description']}\n";
                $this->addIssue($issue['severity'], $issue['description']);
            }
        } else {
            echo "✅ 代码性能良好\n";
        }
    }
    
    /**
     * 前端资源分析
     */
    public function analyzeFrontendResources() {
        echo "\n🎨 前端资源分析\n";
        
        $resourcePath = $this->projectRoot . '/public';
        
        // 分析CSS文件
        $cssFiles = glob($resourcePath . '/assets/css/*.css');
        $totalCssSize = 0;
        foreach ($cssFiles as $file) {
            $totalCssSize += filesize($file);
        }
        
        // 分析JS文件
        $jsFiles = glob($resourcePath . '/assets/js/*.js');
        $totalJsSize = 0;
        foreach ($jsFiles as $file) {
            $totalJsSize += filesize($file);
        }
        
        // 分析图片文件
        $imageFiles = glob($resourcePath . '/uploads/*.{jpg,jpeg,png,gif,webp}', GLOB_BRACE);
        $totalImageSize = 0;
        $largeImages = [];
        foreach ($imageFiles as $file) {
            $size = filesize($file);
            $totalImageSize += $size;
            if ($size > 500 * 1024) { // 大于500KB
                $largeImages[] = [
                    'file' => basename($file),
                    'size' => $this->formatFileSize($size)
                ];
            }
        }
        
        echo "📊 资源统计:\n";
        echo "  - CSS文件: " . count($cssFiles) . " 个，总大小: " . $this->formatFileSize($totalCssSize) . "\n";
        echo "  - JS文件: " . count($jsFiles) . " 个，总大小: " . $this->formatFileSize($totalJsSize) . "\n";
        echo "  - 图片文件: " . count($imageFiles) . " 个，总大小: " . $this->formatFileSize($totalImageSize) . "\n";
        
        // 性能建议
        if ($totalCssSize > 200 * 1024) {
            $this->addIssue('medium', "CSS文件过大，建议压缩和合并");
        }
        
        if ($totalJsSize > 500 * 1024) {
            $this->addIssue('medium', "JS文件过大，建议压缩和按需加载");
        }
        
        if (!empty($largeImages)) {
            $this->addIssue('medium', "发现 " . count($largeImages) . " 个大图片文件，建议压缩");
            echo "⚠️ 大图片文件:\n";
            foreach ($largeImages as $img) {
                echo "  - {$img['file']}: {$img['size']}\n";
            }
        }
    }
    
    // 私有方法
    private function findSlowQueries() {
        $slowQueries = [];
        $phpFiles = $this->findPhpFiles();
        
        foreach ($phpFiles as $file) {
            $content = file_get_contents($file);
            $lines = file($file);
            
            // 检查复杂查询
            $patterns = [
                '/Db::table\([^)]+\)->join\([^)]+\)->join\([^)]+\)/',  // 多表连接
                '/Db::table\([^)]+\)->where\([^)]+\)->where\([^)]+\)->where\([^)]+\)/',  // 多条件查询
                '/->select\(\)->toArray\(\)/',  // 全表查询
            ];
            
            foreach ($patterns as $pattern) {
                if (preg_match_all($pattern, $content, $matches, PREG_OFFSET_CAPTURE)) {
                    foreach ($matches[0] as $match) {
                        $lineNum = substr_count(substr($content, 0, $match[1]), "\n") + 1;
                        $slowQueries[] = [
                            'file' => str_replace($this->projectRoot . DIRECTORY_SEPARATOR, '', $file),
                            'line' => $lineNum,
                            'query' => trim($match[0])
                        ];
                    }
                }
            }
        }
        
        return $slowQueries;
    }
    
    private function findNPlusOneQueries() {
        $issues = [];
        $phpFiles = $this->findPhpFiles();
        
        foreach ($phpFiles as $file) {
            $content = file_get_contents($file);
            
            // 检查循环中的数据库查询
            if (preg_match('/foreach\s*\([^)]+\)\s*{[^}]*Db::/s', $content)) {
                $issues[] = [
                    'file' => str_replace($this->projectRoot . DIRECTORY_SEPARATOR, '', $file),
                    'line' => 'multiple',
                    'description' => '循环中存在数据库查询，可能导致N+1问题'
                ];
            }
        }
        
        return $issues;
    }
    
    private function checkIndexUsage() {
        $suggestions = [];
        
        try {
            // 获取所有表
            $tables = Db::query('SHOW TABLES');
            
            foreach ($tables as $table) {
                $tableName = array_values($table)[0];
                
                // 检查表的索引
                $indexes = Db::query("SHOW INDEX FROM {$tableName}");
                $hasNonPrimaryIndex = false;
                
                foreach ($indexes as $index) {
                    if ($index['Key_name'] !== 'PRIMARY') {
                        $hasNonPrimaryIndex = true;
                        break;
                    }
                }
                
                if (!$hasNonPrimaryIndex) {
                    $suggestions[] = "表 {$tableName} 缺少索引，建议为常用查询字段添加索引";
                }
            }
        } catch (\Exception $e) {
            // 忽略数据库连接错误
        }
        
        return $suggestions;
    }
    
    private function checkCacheConfig() {
        $config = [
            'redis_enabled' => false
        ];
        
        $cacheConfigFile = $this->projectRoot . '/config/cache.php';
        if (file_exists($cacheConfigFile)) {
            $content = file_get_contents($cacheConfigFile);
            if (strpos($content, 'redis') !== false) {
                $config['redis_enabled'] = true;
            }
        }
        
        return $config;
    }
    
    private function analyzeCacheUsageInCode() {
        $usage = [
            'get_calls' => 0,
            'set_calls' => 0
        ];
        
        $phpFiles = $this->findPhpFiles();
        
        foreach ($phpFiles as $file) {
            $content = file_get_contents($file);
            $usage['get_calls'] += substr_count($content, 'Cache::get');
            $usage['set_calls'] += substr_count($content, 'Cache::set');
        }
        
        return $usage;
    }
    
    private function calculateCacheCoverage($usage) {
        $totalCalls = $usage['get_calls'] + $usage['set_calls'];
        if ($totalCalls === 0) return 0;
        
        return min(100, ($totalCalls / 10) * 100); // 假设10次调用为100%覆盖
    }
    
    private function findPerformanceLoops() {
        $issues = [];
        $phpFiles = $this->findPhpFiles();
        
        foreach ($phpFiles as $file) {
            $content = file_get_contents($file);
            
            // 检查嵌套循环
            if (preg_match('/foreach\s*\([^)]+\)\s*{[^}]*foreach\s*\([^)]+\)/s', $content)) {
                $issues[] = [
                    'file' => str_replace($this->projectRoot . DIRECTORY_SEPARATOR, '', $file),
                    'line' => 'multiple',
                    'description' => '嵌套循环可能影响性能',
                    'severity' => 'medium'
                ];
            }
        }
        
        return $issues;
    }
    
    private function findMemoryIssues() {
        $issues = [];
        $phpFiles = $this->findPhpFiles();
        
        foreach ($phpFiles as $file) {
            $content = file_get_contents($file);
            
            // 检查大数组操作
            if (preg_match('/array_merge\s*\([^)]*\$[^)]*\)/', $content)) {
                $issues[] = [
                    'file' => str_replace($this->projectRoot . DIRECTORY_SEPARATOR, '', $file),
                    'line' => 'multiple',
                    'description' => '大数组合并可能消耗大量内存',
                    'severity' => 'low'
                ];
            }
        }
        
        return $issues;
    }
    
    private function findFileOperationIssues() {
        $issues = [];
        $phpFiles = $this->findPhpFiles();
        
        foreach ($phpFiles as $file) {
            $content = file_get_contents($file);
            
            // 检查频繁文件操作
            if (substr_count($content, 'file_get_contents') > 3) {
                $issues[] = [
                    'file' => str_replace($this->projectRoot . DIRECTORY_SEPARATOR, '', $file),
                    'line' => 'multiple',
                    'description' => '频繁文件读取，建议使用缓存',
                    'severity' => 'medium'
                ];
            }
        }
        
        return $issues;
    }
    
    private function findPhpFiles() {
        $files = [];
        $directories = ['app/controller', 'app/model', 'app/service'];
        
        foreach ($directories as $dir) {
            $fullDir = $this->projectRoot . '/' . $dir;
            if (is_dir($fullDir)) {
                $iterator = new RecursiveIteratorIterator(
                    new RecursiveDirectoryIterator($fullDir)
                );
                
                foreach ($iterator as $file) {
                    if ($file->isFile() && $file->getExtension() === 'php') {
                        $files[] = $file->getPathname();
                    }
                }
            }
        }
        
        return $files;
    }
    
    private function addIssue($severity, $message) {
        $this->issues[] = [
            'severity' => $severity,
            'message' => $message
        ];
    }
    
    private function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    private function generatePerformanceReport() {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📊 性能分析报告\n";
        echo str_repeat("=", 60) . "\n";
        
        if (empty($this->issues)) {
            echo "✅ 性能表现良好，未发现明显问题\n";
            return;
        }
        
        $criticalIssues = array_filter($this->issues, function($issue) {
            return $issue['severity'] === 'high';
        });
        
        $mediumIssues = array_filter($this->issues, function($issue) {
            return $issue['severity'] === 'medium';
        });
        
        echo "📈 问题统计:\n";
        echo "  • 高优先级: " . count($criticalIssues) . " 个\n";
        echo "  • 中优先级: " . count($mediumIssues) . " 个\n";
        echo "  • 总计: " . count($this->issues) . " 个\n\n";
        
        echo "🔧 优化建议:\n";
        foreach ($this->issues as $issue) {
            $icon = $issue['severity'] === 'high' ? '🔴' : ($issue['severity'] === 'medium' ? '🟠' : '🟡');
            echo "  {$icon} {$issue['message']}\n";
        }
        
        echo "\n💡 性能优化要点:\n";
        echo "  • 优化数据库查询，添加必要索引\n";
        echo "  • 合理使用缓存，减少重复计算\n";
        echo "  • 压缩前端资源，启用Gzip\n";
        echo "  • 避免N+1查询，使用预加载\n";
    }

    /**
     * 批量性能分析
     */
    public function batchPerformanceAnalysis($directory) {
        $phpFiles = $this->findPhpFilesInDirectory($directory);

        if (empty($phpFiles)) {
            echo "❌ 未找到PHP文件\n";
            return;
        }

        echo "📁 发现 " . count($phpFiles) . " 个PHP文件\n\n";

        $results = [];
        $totalIssues = 0;

        foreach ($phpFiles as $file) {
            $relativePath = str_replace($this->projectRoot . DIRECTORY_SEPARATOR, '', $file);
            echo "🔍 分析文件: $relativePath\n";
            echo str_repeat("-", 40) . "\n";

            $fileResult = $this->analyzeFilePerformance($file);
            $results[] = [
                'file' => $relativePath,
                'result' => $fileResult
            ];

            $issueCount = count($fileResult['issues']);
            $totalIssues += $issueCount;

            if ($issueCount > 0) {
                echo "⚠️ 发现 $issueCount 个性能问题:\n";
                foreach ($fileResult['issues'] as $issue) {
                    $icon = $issue['severity'] === 'high' ? '🔴' : ($issue['severity'] === 'medium' ? '🟠' : '🟡');
                    echo "  $icon {$issue['description']}\n";
                }
            } else {
                echo "✅ 性能良好\n";
            }

            echo "📊 性能评分: {$fileResult['score']}/100\n\n";
        }

        // 生成批量分析报告
        $this->generateBatchPerformanceReport($results, $totalIssues);
    }

    /**
     * 性能对比分析
     */
    public function comparePerformance($dir1, $dir2) {
        echo "🔄 对比目录: $dir1 vs $dir2\n\n";

        $files1 = $this->findPhpFilesInDirectory($dir1);
        $files2 = $this->findPhpFilesInDirectory($dir2);

        if (empty($files1) || empty($files2)) {
            echo "❌ 其中一个目录没有PHP文件\n";
            return;
        }

        echo "📊 目录统计:\n";
        echo "  • $dir1: " . count($files1) . " 个文件\n";
        echo "  • $dir2: " . count($files2) . " 个文件\n\n";

        // 分析第一个目录
        $results1 = $this->analyzeDirectoryPerformance($files1, $dir1);

        // 分析第二个目录
        $results2 = $this->analyzeDirectoryPerformance($files2, $dir2);

        // 生成对比报告
        $this->generateComparisonReport($results1, $results2, $dir1, $dir2);
    }

    /**
     * 生成详细性能报告
     */
    public function generateDetailedReport($directory) {
        $phpFiles = $this->findPhpFilesInDirectory($directory);

        if (empty($phpFiles)) {
            echo "❌ 未找到PHP文件\n";
            return;
        }

        echo "📊 生成详细性能报告...\n";
        echo "📁 扫描目录: $directory\n";
        echo "📄 文件数量: " . count($phpFiles) . "\n\n";

        $allResults = [];
        $performanceStats = [
            'total_files' => count($phpFiles),
            'total_issues' => 0,
            'high_issues' => 0,
            'medium_issues' => 0,
            'low_issues' => 0,
            'avg_score' => 0
        ];

        foreach ($phpFiles as $file) {
            $relativePath = str_replace($this->projectRoot . DIRECTORY_SEPARATOR, '', $file);
            $result = $this->analyzeFilePerformance($file);
            $allResults[$relativePath] = $result;

            $performanceStats['total_issues'] += count($result['issues']);
            $performanceStats['avg_score'] += $result['score'];

            foreach ($result['issues'] as $issue) {
                switch ($issue['severity']) {
                    case 'high':
                        $performanceStats['high_issues']++;
                        break;
                    case 'medium':
                        $performanceStats['medium_issues']++;
                        break;
                    case 'low':
                        $performanceStats['low_issues']++;
                        break;
                }
            }
        }

        $performanceStats['avg_score'] = round($performanceStats['avg_score'] / $performanceStats['total_files'], 1);

        // 生成详细报告
        $this->saveDetailedPerformanceReport($allResults, $performanceStats, $directory);
        $this->displayPerformanceSummary($performanceStats);
    }

    /**
     * 在指定目录查找PHP文件
     */
    private function findPhpFilesInDirectory($directory) {
        $files = [];
        $fullDir = $this->projectRoot . '/' . $directory;

        if (!is_dir($fullDir)) {
            echo "❌ 目录不存在: $directory\n";
            return $files;
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($fullDir, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $files[] = $file->getPathname();
            }
        }

        return $files;
    }

    /**
     * 分析单个文件性能
     */
    private function analyzeFilePerformance($file) {
        $issues = [];
        $score = 100;

        $content = file_get_contents($file);
        $lines = file($file);

        // 检查数据库查询
        $dbQueries = substr_count($content, 'Db::');
        if ($dbQueries > 5) {
            $issues[] = [
                'severity' => 'medium',
                'description' => "数据库查询较多 ({$dbQueries}次)"
            ];
            $score -= 10;
        }

        // 检查循环中的数据库查询
        if (preg_match('/foreach\s*\([^)]+\)\s*{[^}]*Db::/s', $content)) {
            $issues[] = [
                'severity' => 'high',
                'description' => '循环中存在数据库查询，可能导致N+1问题'
            ];
            $score -= 20;
        }

        // 检查缓存使用
        $cacheUsage = substr_count($content, 'Cache::');
        if ($dbQueries > 3 && $cacheUsage === 0) {
            $issues[] = [
                'severity' => 'medium',
                'description' => '有数据库查询但未使用缓存'
            ];
            $score -= 15;
        }

        // 检查文件操作
        $fileOps = substr_count($content, 'file_get_contents') + substr_count($content, 'file_put_contents');
        if ($fileOps > 3) {
            $issues[] = [
                'severity' => 'medium',
                'description' => "频繁文件操作 ({$fileOps}次)"
            ];
            $score -= 10;
        }

        // 检查嵌套循环
        if (preg_match('/foreach\s*\([^)]+\)\s*{[^}]*foreach\s*\([^)]+\)/s', $content)) {
            $issues[] = [
                'severity' => 'medium',
                'description' => '嵌套循环可能影响性能'
            ];
            $score -= 10;
        }

        // 检查大数组操作
        if (preg_match('/array_merge\s*\([^)]*\$[^)]*\)/', $content)) {
            $issues[] = [
                'severity' => 'low',
                'description' => '大数组合并操作'
            ];
            $score -= 5;
        }

        return [
            'score' => max(0, $score),
            'issues' => $issues,
            'db_queries' => $dbQueries,
            'cache_usage' => $cacheUsage,
            'file_operations' => $fileOps,
            'lines' => count($lines)
        ];
    }

    /**
     * 分析目录性能
     */
    private function analyzeDirectoryPerformance($files, $dirName) {
        $totalIssues = 0;
        $totalScore = 0;
        $totalDbQueries = 0;
        $totalCacheUsage = 0;
        $fileCount = count($files);

        foreach ($files as $file) {
            $result = $this->analyzeFilePerformance($file);
            $totalIssues += count($result['issues']);
            $totalScore += $result['score'];
            $totalDbQueries += $result['db_queries'];
            $totalCacheUsage += $result['cache_usage'];
        }

        return [
            'directory' => $dirName,
            'file_count' => $fileCount,
            'avg_score' => $fileCount > 0 ? round($totalScore / $fileCount, 1) : 0,
            'total_issues' => $totalIssues,
            'avg_issues' => $fileCount > 0 ? round($totalIssues / $fileCount, 1) : 0,
            'total_db_queries' => $totalDbQueries,
            'total_cache_usage' => $totalCacheUsage,
            'cache_ratio' => $totalDbQueries > 0 ? round(($totalCacheUsage / $totalDbQueries) * 100, 1) : 0
        ];
    }

    /**
     * 生成批量分析报告
     */
    private function generateBatchPerformanceReport($results, $totalIssues) {
        echo str_repeat("=", 60) . "\n";
        echo "📊 批量性能分析报告\n";
        echo str_repeat("=", 60) . "\n";

        $totalFiles = count($results);
        $totalScore = 0;
        $problemFiles = 0;

        foreach ($results as $result) {
            $totalScore += $result['result']['score'];
            if (!empty($result['result']['issues'])) {
                $problemFiles++;
            }
        }

        $avgScore = $totalFiles > 0 ? round($totalScore / $totalFiles, 1) : 0;

        echo "📈 总体统计:\n";
        echo "  • 分析文件数: $totalFiles\n";
        echo "  • 平均评分: $avgScore/100\n";
        echo "  • 有问题文件: $problemFiles\n";
        echo "  • 总问题数: $totalIssues\n\n";

        // 文件评分排行
        usort($results, function($a, $b) {
            return $b['result']['score'] - $a['result']['score'];
        });

        echo "📋 文件性能排行:\n";
        foreach ($results as $result) {
            $score = $result['result']['score'];
            $issueCount = count($result['result']['issues']);
            $status = $score >= 90 ? '✅' : ($score >= 70 ? '⚠️' : '❌');
            echo "  $status " . str_pad($result['file'], 35) . " 评分: $score 问题: $issueCount\n";
        }

        echo "\n🎯 优化建议:\n";
        if ($problemFiles > 0) {
            echo "  • $problemFiles 个文件存在性能问题，需要优化\n";
        }
        if ($avgScore < 80) {
            echo "  • 平均评分较低，建议重点优化性能\n";
        }
        echo "  • 定期运行性能检查，保持代码质量\n";
    }

    /**
     * 生成对比报告
     */
    private function generateComparisonReport($results1, $results2, $dir1, $dir2) {
        echo str_repeat("=", 60) . "\n";
        echo "📊 性能对比分析报告\n";
        echo str_repeat("=", 60) . "\n";

        echo "📈 对比统计:\n";
        echo "  目录1 ($dir1):\n";
        echo "    • 文件数: {$results1['file_count']}\n";
        echo "    • 平均评分: {$results1['avg_score']}/100\n";
        echo "    • 总问题数: {$results1['total_issues']}\n";
        echo "    • 数据库查询: {$results1['total_db_queries']} 次\n";
        echo "    • 缓存使用: {$results1['total_cache_usage']} 次\n";
        echo "    • 缓存比率: {$results1['cache_ratio']}%\n\n";

        echo "  目录2 ($dir2):\n";
        echo "    • 文件数: {$results2['file_count']}\n";
        echo "    • 平均评分: {$results2['avg_score']}/100\n";
        echo "    • 总问题数: {$results2['total_issues']}\n";
        echo "    • 数据库查询: {$results2['total_db_queries']} 次\n";
        echo "    • 缓存使用: {$results2['total_cache_usage']} 次\n";
        echo "    • 缓存比率: {$results2['cache_ratio']}%\n\n";

        echo "🔄 对比结果:\n";

        // 评分对比
        $scoreDiff = $results1['avg_score'] - $results2['avg_score'];
        if ($scoreDiff > 0) {
            echo "  ✅ $dir1 性能更好 (评分高 $scoreDiff 分)\n";
        } elseif ($scoreDiff < 0) {
            echo "  ✅ $dir2 性能更好 (评分高 " . abs($scoreDiff) . " 分)\n";
        } else {
            echo "  ⚖️ 两个目录性能相当\n";
        }

        // 问题数对比
        $issueDiff = $results1['total_issues'] - $results2['total_issues'];
        if ($issueDiff > 0) {
            echo "  ⚠️ $dir1 问题更多 (多 $issueDiff 个问题)\n";
        } elseif ($issueDiff < 0) {
            echo "  ⚠️ $dir2 问题更多 (多 " . abs($issueDiff) . " 个问题)\n";
        } else {
            echo "  ✅ 两个目录问题数量相同\n";
        }

        // 缓存使用对比
        $cacheDiff = $results1['cache_ratio'] - $results2['cache_ratio'];
        if ($cacheDiff > 0) {
            echo "  🚀 $dir1 缓存使用率更高 (高 $cacheDiff%)\n";
        } elseif ($cacheDiff < 0) {
            echo "  🚀 $dir2 缓存使用率更高 (高 " . abs($cacheDiff) . "%)\n";
        } else {
            echo "  ⚖️ 两个目录缓存使用率相同\n";
        }

        echo "\n💡 优化建议:\n";
        if ($results1['avg_score'] < $results2['avg_score']) {
            echo "  • 重点优化 $dir1 的性能问题\n";
        } elseif ($results2['avg_score'] < $results1['avg_score']) {
            echo "  • 重点优化 $dir2 的性能问题\n";
        }

        if ($results1['cache_ratio'] < 50 || $results2['cache_ratio'] < 50) {
            echo "  • 增加缓存使用，提升性能\n";
        }

        echo "  • 学习性能更好目录的优化经验\n";
    }

    /**
     * 保存详细性能报告
     */
    private function saveDetailedPerformanceReport($allResults, $stats, $directory) {
        $reportPath = $this->projectRoot . '/tools/performance_report.txt';
        $content = "# 性能分析报告 - " . date('Y-m-d H:i:s') . "\n\n";
        $content .= "## 扫描目录: $directory\n\n";
        $content .= "## 统计信息\n";
        $content .= "- 总文件数: {$stats['total_files']}\n";
        $content .= "- 平均评分: {$stats['avg_score']}/100\n";
        $content .= "- 总问题数: {$stats['total_issues']}\n";
        $content .= "- 高优先级问题: {$stats['high_issues']}\n";
        $content .= "- 中优先级问题: {$stats['medium_issues']}\n";
        $content .= "- 低优先级问题: {$stats['low_issues']}\n\n";

        $content .= "## 详细文件信息\n";
        foreach ($allResults as $file => $result) {
            $content .= "### $file\n";
            $content .= "- 性能评分: {$result['score']}/100\n";
            $content .= "- 问题数量: " . count($result['issues']) . "\n";
            $content .= "- 数据库查询: {$result['db_queries']} 次\n";
            $content .= "- 缓存使用: {$result['cache_usage']} 次\n";
            $content .= "- 文件操作: {$result['file_operations']} 次\n";
            $content .= "- 代码行数: {$result['lines']} 行\n";

            if (!empty($result['issues'])) {
                $content .= "- 问题列表:\n";
                foreach ($result['issues'] as $issue) {
                    $content .= "  * [{$issue['severity']}] {$issue['description']}\n";
                }
            }
            $content .= "\n";
        }

        file_put_contents($reportPath, $content);
        echo "\n📝 详细报告已保存: tools/performance_report.txt\n";
    }

    /**
     * 显示性能摘要
     */
    private function displayPerformanceSummary($stats) {
        echo "📊 性能分析摘要:\n";
        echo "  • 总文件数: {$stats['total_files']}\n";
        echo "  • 平均评分: {$stats['avg_score']}/100\n";
        echo "  • 总问题数: {$stats['total_issues']}\n";
        echo "  • 高优先级: {$stats['high_issues']} 个\n";
        echo "  • 中优先级: {$stats['medium_issues']} 个\n";
        echo "  • 低优先级: {$stats['low_issues']} 个\n\n";

        $status = $stats['avg_score'] >= 90 ? '✅ 优秀' :
                 ($stats['avg_score'] >= 80 ? '⚠️ 良好' :
                 ($stats['avg_score'] >= 70 ? '⚠️ 一般' : '❌ 需要优化'));

        echo "🎯 总体评价: $status\n";

        if ($stats['high_issues'] > 0) {
            echo "🔴 发现 {$stats['high_issues']} 个高优先级问题，需要立即处理\n";
        }

        if ($stats['avg_score'] < 80) {
            echo "💡 建议重点关注性能优化，提升代码质量\n";
        }
    }
}

// 命令行使用
if (php_sapi_name() === 'cli') {
    $analyzer = new PerformanceAnalyzer();
    
    $command = $argv[1] ?? 'help';
    
    switch ($command) {
        case 'full':
            $analyzer->fullAnalysis();
            break;
            
        case 'db':
            $analyzer->analyzeDatabasePerformance();
            break;
            
        case 'cache':
            $analyzer->analyzeCacheUsage();
            break;
            
        case 'code':
            $analyzer->analyzeCodePerformance();
            break;
            
        case 'frontend':
            $analyzer->analyzeFrontendResources();
            break;
            
        case 'batch':
            $directory = $argv[2] ?? 'app';
            echo "📁 批量性能分析: $directory\n";
            echo str_repeat("=", 50) . "\n";
            $analyzer->batchPerformanceAnalysis($directory);
            break;

        case 'compare':
            $dir1 = $argv[2] ?? 'app/controller';
            $dir2 = $argv[3] ?? 'app/model';
            echo "🔄 性能对比分析\n";
            echo str_repeat("=", 50) . "\n";
            $analyzer->comparePerformance($dir1, $dir2);
            break;

        case 'report':
            $directory = $argv[2] ?? 'app';
            echo "📊 生成性能报告: $directory\n";
            echo str_repeat("=", 50) . "\n";
            $analyzer->generateDetailedReport($directory);
            break;

        case 'help':
        default:
            echo "⚡ 性能分析工具 - 三只鱼网络科技\n\n";
            echo "📋 可用命令:\n";
            echo "  full       - 全面性能分析(推荐)\n";
            echo "  db         - 数据库性能分析\n";
            echo "  cache      - 缓存使用分析\n";
            echo "  code       - 代码性能分析\n";
            echo "  frontend   - 前端资源分析\n";
            echo "  batch      - 批量性能分析\n";
            echo "  compare    - 性能对比分析\n";
            echo "  report     - 生成详细性能报告\n";
            echo "  help       - 显示此帮助信息\n\n";
            echo "💡 示例:\n";
            echo "  php tools/performance_analyzer.php full\n";
            echo "  php tools/performance_analyzer.php batch app/controller\n";
            echo "  php tools/performance_analyzer.php compare app/controller app/model\n";
            echo "  php tools/performance_analyzer.php report app\n\n";
            echo "🎯 功能特性:\n";
            echo "  • 全面性能分析和优化建议\n";
            echo "  • 数据库查询性能检测\n";
            echo "  • 缓存使用率分析\n";
            echo "  • 代码性能问题识别\n";
            echo "  • 前端资源优化建议\n";
            echo "  • 批量文件性能分析\n";
            echo "  • 性能对比和趋势分析\n";
    }
}
