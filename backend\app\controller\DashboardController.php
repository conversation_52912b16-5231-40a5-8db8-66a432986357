<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 仪表盘控制器
 */

namespace app\controller;

use app\BaseController;
use app\model\User;
use app\model\DiyPage;
use app\model\Log;
use think\Response;

class DashboardController extends BaseController
{
    /**
     * 获取仪表盘统计数据
     */
    public function stats(): Response
    {
        try {
            // 用户统计
            $totalUsers = User::count();
            $newUsersToday = User::whereTime('created_at', 'today')->count();
            $activeUsersToday = User::whereTime('last_login_at', 'today')->count();
            $lastWeekUsers = User::whereTime('created_at', 'last week')->count();
            $userTrend = $lastWeekUsers > 0 ? round((($newUsersToday - $lastWeekUsers) / $lastWeekUsers) * 100, 1) : 0;

            // 页面统计
            $totalPages = DiyPage::count();
            $publishedPages = DiyPage::where('status', 'published')->count();
            $draftPages = DiyPage::where('status', 'draft')->count();
            $lastWeekPages = DiyPage::whereTime('created_at', 'last week')->count();
            $pageTrend = $lastWeekPages > 0 ? round((($totalPages - $lastWeekPages) / $lastWeekPages) * 100, 1) : 0;

            // 访问统计（模拟数据）
            $visitsToday = rand(800, 1200);
            $totalVisits = rand(15000, 25000);
            $uniqueVisits = rand(8000, 12000);
            $visitTrend = rand(-10, 15);

            // 消息统计
            $unreadMessages = Log::where('action', 'like', '%error%')->count();
            $totalMessages = Log::count();
            $messageTrend = rand(0, 20);

            $data = [
                'users' => [
                    'total' => $totalUsers,
                    'trend' => $userTrend,
                    'new_today' => $newUsersToday,
                    'active_today' => $activeUsersToday
                ],
                'pages' => [
                    'total' => $totalPages,
                    'trend' => $pageTrend,
                    'published' => $publishedPages,
                    'draft' => $draftPages
                ],
                'visits' => [
                    'today' => $visitsToday,
                    'trend' => $visitTrend,
                    'total' => $totalVisits,
                    'unique' => $uniqueVisits
                ],
                'messages' => [
                    'unread' => $unreadMessages,
                    'trend' => $messageTrend,
                    'total' => $totalMessages
                ]
            ];

            return $this->success($data, '获取统计数据成功');

        } catch (\Exception $e) {
            return $this->error('获取统计数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取最近活动
     */
    public function activities(): Response
    {
        try {
            $limit = $this->request->param('limit', 10);
            
            $activities = Log::with(['user'])
                ->order('created_at', 'desc')
                ->limit($limit)
                ->select()
                ->toArray();

            $result = [];
            foreach ($activities as $activity) {
                $result[] = [
                    'id' => $activity['id'],
                    'user' => $activity['user']['real_name'] ?? $activity['user']['username'] ?? '未知用户',
                    'action' => $activity['description'] ?? $activity['action'],
                    'time' => date('Y-m-d H:i', strtotime($activity['created_at'])),
                    'avatar' => $activity['user']['avatar_url'] ?? '',
                    'type' => $this->getActivityType($activity['action'])
                ];
            }

            return $this->success($result, '获取活动记录成功');

        } catch (\Exception $e) {
            return $this->error('获取活动记录失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取访问趋势
     */
    public function visitTrend(): Response
    {
        try {
            $period = $this->request->param('period', '7d');
            
            // 根据周期生成模拟数据
            $days = $period === '7d' ? 7 : ($period === '30d' ? 30 : 90);
            $data = [];
            
            for ($i = $days - 1; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-{$i} days"));
                $baseVisits = rand(100, 300);
                $data[] = [
                    'date' => $date,
                    'visits' => $baseVisits + rand(0, 100),
                    'unique_visits' => intval($baseVisits * 0.7) + rand(0, 50),
                    'page_views' => $baseVisits * rand(2, 5)
                ];
            }

            return $this->success($data, '获取访问趋势成功');

        } catch (\Exception $e) {
            return $this->error('获取访问趋势失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取快速统计
     */
    public function quickStats(): Response
    {
        try {
            $data = [
                'online_users' => rand(15, 45),
                'today_orders' => rand(20, 80),
                'today_revenue' => rand(5000, 15000),
                'system_load' => rand(20, 80)
            ];

            return $this->success($data, '获取快速统计成功');

        } catch (\Exception $e) {
            return $this->error('获取快速统计失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取活动类型
     */
    private function getActivityType(string $action): string
    {
        if (strpos($action, '创建') !== false || strpos($action, 'create') !== false) {
            return 'create';
        }
        if (strpos($action, '更新') !== false || strpos($action, 'update') !== false) {
            return 'update';
        }
        if (strpos($action, '删除') !== false || strpos($action, 'delete') !== false) {
            return 'delete';
        }
        if (strpos($action, '登录') !== false || strpos($action, 'login') !== false) {
            return 'login';
        }
        if (strpos($action, '登出') !== false || strpos($action, 'logout') !== false) {
            return 'logout';
        }
        
        return 'update';
    }
}
