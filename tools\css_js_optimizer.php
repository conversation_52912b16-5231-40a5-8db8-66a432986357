<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * CSS/JS优化工具 - ThinkPHP6企业级应用
 * 功能：检测重复CSS、未使用样式、JS冗余、安全清理建议
 */

class CssJsOptimizer {

    private $projectRoot;
    private $cssFiles = [];
    private $jsFiles = [];
    private $htmlFiles = [];
    private $usedClasses = [];
    private $usedIds = [];
    private $duplicateRules = [];
    private $unusedRules = [];
    private $scanDirs = [];

    public function __construct($customDirs = []) {
        $this->projectRoot = dirname(__DIR__);

        // 设置扫描目录
        if (!empty($customDirs)) {
            $this->scanDirs = $customDirs;
        } else {
            // 默认扫描目录
            $this->scanDirs = [
                'css' => ['public/assets/css', 'public/diy/css'],
                'js' => ['public/assets/js', 'public/diy/js'],
                'html' => ['app/view', 'public/diy']
            ];
        }

        $this->loadFiles();
    }
    
    /**
     * 全面CSS/JS分析
     */
    public function fullAnalysis() {
        echo "🎨 CSS/JS全面优化分析\n";
        echo str_repeat("=", 60) . "\n";
        
        $this->analyzeCSSRedundancy();
        $this->analyzeJSRedundancy();
        $this->analyzeUnusedCSS();
        $this->generateOptimizationReport();
    }
    
    /**
     * CSS冗余分析
     */
    public function analyzeCSSRedundancy() {
        echo "\n📐 CSS冗余分析\n";
        
        $allRules = [];
        $duplicates = [];
        
        foreach ($this->cssFiles as $cssFile) {
            $content = file_get_contents($cssFile['path']);
            $rules = $this->extractCSSRules($content);
            
            foreach ($rules as $rule) {
                $selector = $rule['selector'];
                $properties = $rule['properties'];
                $ruleKey = $selector . '|' . $properties;
                
                if (isset($allRules[$ruleKey])) {
                    // 发现重复规则
                    if (!isset($duplicates[$ruleKey])) {
                        $duplicates[$ruleKey] = [
                            'selector' => $selector,
                            'properties' => $properties,
                            'files' => [$allRules[$ruleKey]]
                        ];
                    }
                    $duplicates[$ruleKey]['files'][] = $cssFile['name'];
                } else {
                    $allRules[$ruleKey] = $cssFile['name'];
                }
            }
        }
        
        $this->duplicateRules = $duplicates;
        
        if (!empty($duplicates)) {
            echo "⚠️ 发现 " . count($duplicates) . " 个重复CSS规则:\n";
            $count = 0;
            foreach ($duplicates as $rule) {
                if ($count >= 10) {
                    echo "  ... 还有 " . (count($duplicates) - 10) . " 个重复规则\n";
                    break;
                }
                echo "  - {$rule['selector']} (出现在: " . implode(', ', $rule['files']) . ")\n";
                $count++;
            }
        } else {
            echo "✅ 未发现重复CSS规则\n";
        }
        
        // 分析CSS文件大小
        $this->analyzeCSSFileSize();
    }
    
    /**
     * 分析未使用的CSS
     */
    public function analyzeUnusedCSS() {
        echo "\n🔍 未使用CSS分析\n";
        
        // 收集HTML中使用的类名和ID
        $this->collectUsedSelectors();
        
        $unusedRules = [];
        $totalRules = 0;
        
        foreach ($this->cssFiles as $cssFile) {
            $content = file_get_contents($cssFile['path']);
            $rules = $this->extractCSSRules($content);
            
            foreach ($rules as $rule) {
                $totalRules++;
                $selector = trim($rule['selector']);
                
                // 跳过伪类、媒体查询等复杂选择器
                if ($this->isComplexSelector($selector)) {
                    continue;
                }
                
                if (!$this->isSelectorUsed($selector)) {
                    $unusedRules[] = [
                        'file' => $cssFile['name'],
                        'selector' => $selector,
                        'properties' => $rule['properties']
                    ];
                }
            }
        }
        
        $this->unusedRules = $unusedRules;
        $unusedCount = count($unusedRules);
        $usageRate = $totalRules > 0 ? round((($totalRules - $unusedCount) / $totalRules) * 100, 1) : 0;
        
        echo "📊 CSS使用率统计:\n";
        echo "  - 总CSS规则: {$totalRules} 个\n";
        echo "  - 未使用规则: {$unusedCount} 个\n";
        echo "  - 使用率: {$usageRate}%\n";
        
        if ($unusedCount > 0) {
            echo "\n⚠️ 可能未使用的CSS规则 (前10个):\n";
            $count = 0;
            foreach ($unusedRules as $rule) {
                if ($count >= 10) break;
                echo "  - {$rule['file']}: {$rule['selector']}\n";
                $count++;
            }
            
            if ($unusedCount > 10) {
                echo "  ... 还有 " . ($unusedCount - 10) . " 个未使用规则\n";
            }
        }
    }
    
    /**
     * JS冗余分析
     */
    public function analyzeJSRedundancy() {
        echo "\n⚡ JS冗余分析\n";
        
        $duplicateFunctions = [];
        $allFunctions = [];
        
        foreach ($this->jsFiles as $jsFile) {
            $content = file_get_contents($jsFile['path']);
            $functions = $this->extractJSFunctions($content);
            
            foreach ($functions as $func) {
                $funcName = $func['name'];
                $funcBody = $func['body'];
                
                if (isset($allFunctions[$funcName])) {
                    // 检查函数体是否相似
                    if ($this->isSimilarFunction($funcBody, $allFunctions[$funcName]['body'])) {
                        if (!isset($duplicateFunctions[$funcName])) {
                            $duplicateFunctions[$funcName] = [
                                'files' => [$allFunctions[$funcName]['file']]
                            ];
                        }
                        $duplicateFunctions[$funcName]['files'][] = $jsFile['name'];
                    }
                } else {
                    $allFunctions[$funcName] = [
                        'file' => $jsFile['name'],
                        'body' => $funcBody
                    ];
                }
            }
        }
        
        if (!empty($duplicateFunctions)) {
            echo "⚠️ 发现 " . count($duplicateFunctions) . " 个重复JS函数:\n";
            foreach ($duplicateFunctions as $funcName => $info) {
                echo "  🔴 {$funcName}() 重复出现在:\n";
                foreach ($info['files'] as $file) {
                    echo "    - $file\n";
                }
            }
        } else {
            echo "✅ 未发现重复JS函数\n";
        }
        
        // 分析JS文件大小
        $this->analyzeJSFileSize();
    }
    
    /**
     * 生成安全清理建议
     */
    public function generateSafeCleanupSuggestions() {
        echo "\n🛡️ 安全清理建议\n";
        
        $suggestions = [];
        
        // CSS清理建议
        if (!empty($this->duplicateRules)) {
            $suggestions[] = [
                'type' => 'CSS重复规则',
                'count' => count($this->duplicateRules),
                'action' => '保留一个，删除其他重复项',
                'risk' => 'low'
            ];
        }
        
        if (!empty($this->unusedRules)) {
            $safeToRemove = array_filter($this->unusedRules, function($rule) {
                return !$this->isSystemClass($rule['selector']);
            });
            
            $suggestions[] = [
                'type' => '未使用CSS规则',
                'count' => count($safeToRemove),
                'action' => '可安全删除(排除系统类)',
                'risk' => 'medium'
            ];
        }
        
        // 生成清理脚本
        $this->generateCleanupScript($suggestions);
        
        foreach ($suggestions as $suggestion) {
            $riskIcon = $suggestion['risk'] === 'low' ? '🟢' : ($suggestion['risk'] === 'medium' ? '🟡' : '🔴');
            echo "  {$riskIcon} {$suggestion['type']}: {$suggestion['count']} 个 - {$suggestion['action']}\n";
        }
    }
    
    /**
     * 生成优化报告
     */
    private function generateOptimizationReport() {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📊 CSS/JS优化报告\n";
        echo str_repeat("=", 60) . "\n";
        
        $totalIssues = count($this->duplicateRules) + count($this->unusedRules);
        
        echo "📈 问题统计:\n";
        echo "  • CSS重复规则: " . count($this->duplicateRules) . " 个\n";
        echo "  • 未使用CSS规则: " . count($this->unusedRules) . " 个\n";
        echo "  • 总计问题: {$totalIssues} 个\n\n";
        
        if ($totalIssues > 0) {
            echo "🔧 优化建议:\n";
            echo "  • 合并重复CSS规则，减少文件大小\n";
            echo "  • 安全删除未使用样式，提升加载速度\n";
            echo "  • 建立CSS规范，避免重复编写\n";
            echo "  • 定期运行检测，保持代码整洁\n\n";
            
            $this->generateSafeCleanupSuggestions();
        } else {
            echo "✅ CSS/JS代码质量良好，无需优化\n";
        }
        
        echo "\n💡 最佳实践:\n";
        echo "  • 使用CSS预处理器避免重复\n";
        echo "  • 建立组件化开发模式\n";
        echo "  • 定期清理无用代码\n";
        echo "  • 使用构建工具自动优化\n";
    }
    
    // 私有方法
    private function loadFiles() {
        // 加载CSS文件
        if (isset($this->scanDirs['css'])) {
            foreach ($this->scanDirs['css'] as $cssDir) {
                $fullPath = $this->projectRoot . '/' . $cssDir;
                if (is_dir($fullPath)) {
                    $this->loadCSSFilesRecursive($fullPath);
                }
            }
        }

        // 加载JS文件
        if (isset($this->scanDirs['js'])) {
            foreach ($this->scanDirs['js'] as $jsDir) {
                $fullPath = $this->projectRoot . '/' . $jsDir;
                if (is_dir($fullPath)) {
                    $this->loadJSFilesRecursive($fullPath);
                }
            }
        }

        // 加载HTML文件
        if (isset($this->scanDirs['html'])) {
            foreach ($this->scanDirs['html'] as $htmlDir) {
                $fullPath = $this->projectRoot . '/' . $htmlDir;
                $this->loadHTMLFiles($fullPath);
            }
        }
    }

    /**
     * 递归加载CSS文件
     */
    private function loadCSSFilesRecursive($dir) {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->getExtension() === 'css') {
                $relativePath = str_replace($this->projectRoot . '/', '', $file->getPathname());
                $this->cssFiles[] = [
                    'path' => $file->getPathname(),
                    'name' => $file->getFilename(),
                    'relative' => $relativePath
                ];
            }
        }
    }

    /**
     * 递归加载JS文件
     */
    private function loadJSFilesRecursive($dir) {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->getExtension() === 'js') {
                $relativePath = str_replace($this->projectRoot . '/', '', $file->getPathname());
                $this->jsFiles[] = [
                    'path' => $file->getPathname(),
                    'name' => $file->getFilename(),
                    'relative' => $relativePath
                ];
            }
        }
    }
    
    private function loadHTMLFiles($dir) {
        if (!is_dir($dir)) return;
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'html') {
                $this->htmlFiles[] = $file->getPathname();
            }
        }
    }
    
    private function extractCSSRules($content) {
        $rules = [];
        
        // 移除注释
        $content = preg_replace('/\/\*.*?\*\//s', '', $content);
        
        // 匹配CSS规则
        preg_match_all('/([^{}]+)\{([^{}]*)\}/s', $content, $matches);
        
        for ($i = 0; $i < count($matches[0]); $i++) {
            $selector = trim($matches[1][$i]);
            $properties = trim($matches[2][$i]);
            
            if (!empty($selector) && !empty($properties)) {
                $rules[] = [
                    'selector' => $selector,
                    'properties' => $properties
                ];
            }
        }
        
        return $rules;
    }
    
    private function extractJSFunctions($content) {
        $functions = [];
        
        // 匹配函数定义
        preg_match_all('/function\s+(\w+)\s*\([^)]*\)\s*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}/s', $content, $matches);
        
        for ($i = 0; $i < count($matches[0]); $i++) {
            $functions[] = [
                'name' => $matches[1][$i],
                'body' => trim($matches[2][$i])
            ];
        }
        
        return $functions;
    }
    
    private function collectUsedSelectors() {
        foreach ($this->htmlFiles as $htmlFile) {
            $content = file_get_contents($htmlFile);
            
            // 收集class
            preg_match_all('/class=["\']([^"\']*)["\']/', $content, $classMatches);
            foreach ($classMatches[1] as $classList) {
                $classes = explode(' ', $classList);
                foreach ($classes as $class) {
                    $class = trim($class);
                    if (!empty($class)) {
                        $this->usedClasses[$class] = true;
                    }
                }
            }
            
            // 收集id
            preg_match_all('/id=["\']([^"\']*)["\']/', $content, $idMatches);
            foreach ($idMatches[1] as $id) {
                $id = trim($id);
                if (!empty($id)) {
                    $this->usedIds[$id] = true;
                }
            }
        }
    }
    
    private function isSelectorUsed($selector) {
        // 简化选择器检查
        $selector = trim($selector);
        
        // 检查类选择器
        if (strpos($selector, '.') === 0) {
            $className = substr($selector, 1);
            return isset($this->usedClasses[$className]);
        }
        
        // 检查ID选择器
        if (strpos($selector, '#') === 0) {
            $idName = substr($selector, 1);
            return isset($this->usedIds[$idName]);
        }
        
        // 元素选择器默认认为被使用
        if (preg_match('/^[a-zA-Z]+$/', $selector)) {
            return true;
        }
        
        return false;
    }
    
    private function isComplexSelector($selector) {
        // 跳过复杂选择器
        return strpos($selector, ':') !== false || 
               strpos($selector, '@') !== false ||
               strpos($selector, '>') !== false ||
               strpos($selector, '+') !== false ||
               strpos($selector, '~') !== false;
    }
    
    private function isSystemClass($selector) {
        $systemClasses = ['container', 'row', 'col-', 'btn', 'form-', 'nav', 'card', 'alert', 'modal'];
        
        foreach ($systemClasses as $systemClass) {
            if (strpos($selector, $systemClass) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    private function isSimilarFunction($body1, $body2) {
        // 简单的相似度检查
        $similarity = 0;
        similar_text($body1, $body2, $similarity);
        return $similarity > 80; // 80%相似度
    }
    
    private function analyzeCSSFileSize() {
        $totalSize = 0;
        $largeFiles = [];
        
        foreach ($this->cssFiles as $cssFile) {
            $size = filesize($cssFile['path']);
            $totalSize += $size;
            
            if ($size > 50 * 1024) { // 大于50KB
                $largeFiles[] = [
                    'name' => $cssFile['name'],
                    'size' => $this->formatFileSize($size)
                ];
            }
        }
        
        echo "\n📊 CSS文件大小分析:\n";
        echo "  - 总大小: " . $this->formatFileSize($totalSize) . "\n";
        
        if (!empty($largeFiles)) {
            echo "  - 大文件 (>50KB):\n";
            foreach ($largeFiles as $file) {
                echo "    * {$file['name']}: {$file['size']}\n";
            }
        }
    }
    
    private function analyzeJSFileSize() {
        $totalSize = 0;
        $largeFiles = [];
        
        foreach ($this->jsFiles as $jsFile) {
            $size = filesize($jsFile['path']);
            $totalSize += $size;
            
            if ($size > 100 * 1024) { // 大于100KB
                $largeFiles[] = [
                    'name' => $jsFile['name'],
                    'size' => $this->formatFileSize($size)
                ];
            }
        }
        
        echo "\n📊 JS文件大小分析:\n";
        echo "  - 总大小: " . $this->formatFileSize($totalSize) . "\n";
        
        if (!empty($largeFiles)) {
            echo "  - 大文件 (>100KB):\n";
            foreach ($largeFiles as $file) {
                echo "    * {$file['name']}: {$file['size']}\n";
            }
        }
    }
    
    private function generateCleanupScript($suggestions) {
        $scriptPath = $this->projectRoot . '/tools/css_cleanup_suggestions.txt';
        $content = "# CSS/JS清理建议 - " . date('Y-m-d H:i:s') . "\n\n";
        
        if (!empty($this->duplicateRules)) {
            $content .= "## 重复CSS规则清理\n";
            foreach ($this->duplicateRules as $rule) {
                $content .= "- 选择器: {$rule['selector']}\n";
                $content .= "  文件: " . implode(', ', $rule['files']) . "\n";
                $content .= "  建议: 保留一个文件中的规则，删除其他文件中的重复项\n\n";
            }
        }
        
        if (!empty($this->unusedRules)) {
            $content .= "## 未使用CSS规则清理\n";
            foreach (array_slice($this->unusedRules, 0, 20) as $rule) {
                if (!$this->isSystemClass($rule['selector'])) {
                    $content .= "- 文件: {$rule['file']}\n";
                    $content .= "  选择器: {$rule['selector']}\n";
                    $content .= "  建议: 可安全删除(请先确认)\n\n";
                }
            }
        }
        
        file_put_contents($scriptPath, $content);
        echo "\n📝 详细清理建议已保存到: tools/css_cleanup_suggestions.txt\n";
    }
    
    private function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}

// 命令行使用
if (php_sapi_name() === 'cli') {
    $command = $argv[1] ?? 'help';
    $customDirs = [];

    // 解析自定义目录参数
    if (isset($argv[2]) && strpos($argv[2], '--dir=') === 0) {
        $dirParam = substr($argv[2], 6);
        $dirs = explode(',', $dirParam);

        // 根据文件类型分类目录
        foreach ($dirs as $dir) {
            $dir = trim($dir);
            if (strpos($dir, '.css') !== false || strpos($dir, '/css') !== false) {
                $customDirs['css'][] = $dir;
            } elseif (strpos($dir, '.js') !== false || strpos($dir, '/js') !== false) {
                $customDirs['js'][] = $dir;
            } elseif (strpos($dir, '.html') !== false || strpos($dir, '/view') !== false || strpos($dir, '/diy') !== false) {
                $customDirs['html'][] = $dir;
            } else {
                // 自动检测目录类型
                if (is_dir($dir)) {
                    if (glob($dir . '/*.css')) $customDirs['css'][] = $dir;
                    if (glob($dir . '/*.js')) $customDirs['js'][] = $dir;
                    if (glob($dir . '/*.html')) $customDirs['html'][] = $dir;
                }
            }
        }
    }

    $optimizer = new CssJsOptimizer($customDirs);

    switch ($command) {
        case 'full':
            $optimizer->fullAnalysis();
            break;

        case 'css':
            $optimizer->analyzeCSSRedundancy();
            break;

        case 'unused':
            $optimizer->analyzeUnusedCSS();
            break;

        case 'js':
            $optimizer->analyzeJSRedundancy();
            break;

        case 'cleanup':
            $optimizer->generateSafeCleanupSuggestions();
            break;

        case 'scan':
            // 新增：扫描指定目录
            if (isset($argv[2])) {
                $targetDir = $argv[2];
                echo "🔍 扫描目录: $targetDir\n";
                $scanDirs = [
                    'css' => [$targetDir],
                    'js' => [$targetDir],
                    'html' => [$targetDir]
                ];
                $scanner = new CssJsOptimizer($scanDirs);
                $scanner->fullAnalysis();
            } else {
                echo "❌ 请指定要扫描的目录\n";
                echo "💡 示例: php tools/css_js_optimizer.php scan public/diy\n";
            }
            break;

        default:
            echo "🎨 CSS/JS优化工具 - 三只鱼网络科技\n\n";
            echo "📋 可用命令:\n";
            echo "  full     - 全面CSS/JS分析(默认目录)\n";
            echo "  css      - CSS冗余分析\n";
            echo "  unused   - 未使用CSS分析\n";
            echo "  js       - JS冗余分析\n";
            echo "  cleanup  - 生成安全清理建议\n";
            echo "  scan     - 扫描指定目录\n\n";
            echo "🎯 自定义目录扫描:\n";
            echo "  --dir=目录1,目录2  - 指定扫描目录\n\n";
            echo "💡 示例:\n";
            echo "  php tools/css_js_optimizer.php full\n";
            echo "  php tools/css_js_optimizer.php scan public/diy\n";
            echo "  php tools/css_js_optimizer.php js --dir=public/diy/js\n";
            echo "  php tools/css_js_optimizer.php css --dir=public/assets/css,public/diy/css\n";
    }
}
