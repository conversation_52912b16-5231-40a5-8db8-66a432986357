<?php
/**
 * 三只鱼网络科技 | 韩总 | 2025-06-08
 * 文件分析工具 - 批量文件查看和分析
 */

class FileAnalyzer
{
    private $projectRoot;
    private $excludeDirs = ['vendor', 'node_modules', 'runtime', '.git'];
    private $includeExts = ['php', 'js', 'css', 'html', 'vue', 'md'];

    public function __construct($projectRoot = null)
    {
        $this->projectRoot = $projectRoot ?: dirname(__DIR__);
    }

    /**
     * 批量查询文件信息
     */
    public function batchFileInfo($pattern = '*')
    {
        echo "=== 批量文件信息查询 ===\n";
        echo "项目根目录: {$this->projectRoot}\n";
        echo "查询模式: {$pattern}\n\n";

        $files = $this->findFiles($pattern);
        
        foreach ($files as $file) {
            $this->showFileInfo($file);
        }
        
        echo "\n总计文件数: " . count($files) . "\n";
    }

    /**
     * 大文件分段查看
     */
    public function viewLargeFile($filePath, $startLine = 1, $lineCount = 50)
    {
        $fullPath = $this->getFullPath($filePath);
        
        if (!file_exists($fullPath)) {
            echo "文件不存在: {$filePath}\n";
            return;
        }

        $fileSize = filesize($fullPath);
        $totalLines = count(file($fullPath));
        
        echo "=== 大文件分段查看 ===\n";
        echo "文件: {$filePath}\n";
        echo "大小: " . $this->formatFileSize($fileSize) . "\n";
        echo "总行数: {$totalLines}\n";
        echo "查看范围: {$startLine} - " . ($startLine + $lineCount - 1) . "\n";
        echo str_repeat("-", 50) . "\n";

        $lines = file($fullPath);
        $endLine = min($startLine + $lineCount - 1, $totalLines);
        
        for ($i = $startLine - 1; $i < $endLine; $i++) {
            printf("%4d: %s", $i + 1, $lines[$i]);
        }
        
        echo str_repeat("-", 50) . "\n";
        echo "显示完成。使用 viewLargeFile('{$filePath}', " . ($endLine + 1) . ") 查看下一段\n";
    }

    /**
     * 查找特定内容
     */
    public function searchContent($searchText, $filePattern = '*.php')
    {
        echo "=== 内容搜索 ===\n";
        echo "搜索内容: {$searchText}\n";
        echo "文件模式: {$filePattern}\n\n";

        $files = $this->findFiles($filePattern);
        $results = [];

        foreach ($files as $file) {
            $content = file_get_contents($file);
            if (stripos($content, $searchText) !== false) {
                $lines = file($file);
                foreach ($lines as $lineNum => $line) {
                    if (stripos($line, $searchText) !== false) {
                        $results[] = [
                            'file' => str_replace($this->projectRoot . DIRECTORY_SEPARATOR, '', $file),
                            'line' => $lineNum + 1,
                            'content' => trim($line)
                        ];
                    }
                }
            }
        }

        foreach ($results as $result) {
            echo "{$result['file']}:{$result['line']} - {$result['content']}\n";
        }
        
        echo "\n找到 " . count($results) . " 个匹配项\n";
    }

    /**
     * 文件结构分析
     */
    public function analyzeStructure($directory = '')
    {
        $targetDir = $directory ? $this->getFullPath($directory) : $this->projectRoot;
        
        echo "=== 文件结构分析 ===\n";
        echo "目录: {$directory}\n\n";

        $stats = [
            'total_files' => 0,
            'total_size' => 0,
            'by_extension' => [],
            'large_files' => []
        ];

        $this->scanDirectory($targetDir, $stats);

        echo "文件统计:\n";
        echo "总文件数: {$stats['total_files']}\n";
        echo "总大小: " . $this->formatFileSize($stats['total_size']) . "\n\n";

        echo "按扩展名分类:\n";
        arsort($stats['by_extension']);
        foreach ($stats['by_extension'] as $ext => $count) {
            echo "  .{$ext}: {$count} 个文件\n";
        }

        if (!empty($stats['large_files'])) {
            echo "\n大文件 (>100KB):\n";
            foreach ($stats['large_files'] as $file) {
                echo "  {$file['path']}: {$file['size']}\n";
            }
        }
    }

    /**
     * 代码质量检查
     */
    public function checkCodeQuality($filePattern = '*.php')
    {
        echo "=== 代码质量检查 ===\n";
        
        $files = $this->findFiles($filePattern);
        $issues = [];

        foreach ($files as $file) {
            $content = file_get_contents($file);
            $lines = file($file);
            $filePath = str_replace($this->projectRoot . DIRECTORY_SEPARATOR, '', $file);

            // 检查长行
            foreach ($lines as $lineNum => $line) {
                if (strlen(trim($line)) > 120) {
                    $issues[] = "{$filePath}:{$lineNum} - 行过长 (" . strlen(trim($line)) . " 字符)";
                }
            }

            // 检查调试代码
            $debugPatterns = ['var_dump', 'print_r', 'console.log', 'alert(', 'echo '];
            foreach ($debugPatterns as $pattern) {
                if (stripos($content, $pattern) !== false) {
                    $issues[] = "{$filePath} - 包含调试代码: {$pattern}";
                }
            }

            // 检查TODO注释
            if (stripos($content, 'TODO') !== false) {
                $issues[] = "{$filePath} - 包含TODO注释";
            }
        }

        if (empty($issues)) {
            echo "✅ 代码质量良好，未发现问题\n";
        } else {
            echo "发现 " . count($issues) . " 个问题:\n";
            foreach ($issues as $issue) {
                echo "  - {$issue}\n";
            }
        }
    }

    // 私有方法
    private function findFiles($pattern)
    {
        $files = [];
        $this->scanForFiles($this->projectRoot, $pattern, $files);
        return $files;
    }

    private function scanForFiles($dir, $pattern, &$files)
    {
        if (!is_dir($dir)) return;

        $items = scandir($dir);
        foreach ($items as $item) {
            if ($item === '.' || $item === '..') continue;
            
            $fullPath = $dir . DIRECTORY_SEPARATOR . $item;
            
            if (is_dir($fullPath)) {
                $dirName = basename($fullPath);
                if (!in_array($dirName, $this->excludeDirs)) {
                    $this->scanForFiles($fullPath, $pattern, $files);
                }
            } else {
                if ($this->matchesPattern($item, $pattern)) {
                    $files[] = $fullPath;
                }
            }
        }
    }

    private function matchesPattern($filename, $pattern)
    {
        if ($pattern === '*') {
            $ext = pathinfo($filename, PATHINFO_EXTENSION);
            return in_array($ext, $this->includeExts);
        }
        
        return fnmatch($pattern, $filename);
    }

    private function showFileInfo($file)
    {
        $size = filesize($file);
        $modified = date('Y-m-d H:i:s', filemtime($file));
        $relativePath = str_replace($this->projectRoot . DIRECTORY_SEPARATOR, '', $file);
        
        echo sprintf("%-50s %10s %s\n", 
            $relativePath, 
            $this->formatFileSize($size), 
            $modified
        );
    }

    private function scanDirectory($dir, &$stats)
    {
        if (!is_dir($dir)) return;

        $items = scandir($dir);
        foreach ($items as $item) {
            if ($item === '.' || $item === '..') continue;
            
            $fullPath = $dir . DIRECTORY_SEPARATOR . $item;
            
            if (is_dir($fullPath)) {
                $dirName = basename($fullPath);
                if (!in_array($dirName, $this->excludeDirs)) {
                    $this->scanDirectory($fullPath, $stats);
                }
            } else {
                $stats['total_files']++;
                $size = filesize($fullPath);
                $stats['total_size'] += $size;
                
                $ext = pathinfo($fullPath, PATHINFO_EXTENSION);
                $stats['by_extension'][$ext] = ($stats['by_extension'][$ext] ?? 0) + 1;
                
                if ($size > 100 * 1024) { // 大于100KB
                    $relativePath = str_replace($this->projectRoot . DIRECTORY_SEPARATOR, '', $fullPath);
                    $stats['large_files'][] = [
                        'path' => $relativePath,
                        'size' => $this->formatFileSize($size)
                    ];
                }
            }
        }
    }

    private function getFullPath($relativePath)
    {
        return $this->projectRoot . DIRECTORY_SEPARATOR . ltrim($relativePath, '/\\');
    }

    private function formatFileSize($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}

// 使用示例
if (php_sapi_name() === 'cli') {
    $analyzer = new FileAnalyzer();
    
    $command = $argv[1] ?? 'help';
    
    switch ($command) {
        case 'batch':
            $pattern = $argv[2] ?? '*.php';
            $analyzer->batchFileInfo($pattern);
            break;
            
        case 'view':
            $file = $argv[2] ?? '';
            $start = intval($argv[3] ?? 1);
            $count = intval($argv[4] ?? 50);
            $analyzer->viewLargeFile($file, $start, $count);
            break;
            
        case 'search':
            $text = $argv[2] ?? '';
            $pattern = $argv[3] ?? '*.php';
            $analyzer->searchContent($text, $pattern);
            break;
            
        case 'structure':
            $dir = $argv[2] ?? '';
            $analyzer->analyzeStructure($dir);
            break;
            
        case 'quality':
            $pattern = $argv[2] ?? '*.php';
            $analyzer->checkCodeQuality($pattern);
            break;
            
        default:
            echo "文件分析工具使用说明:\n";
            echo "php tools/file_analyzer.php batch [pattern]     - 批量查询文件\n";
            echo "php tools/file_analyzer.php view file [start] [count] - 分段查看大文件\n";
            echo "php tools/file_analyzer.php search text [pattern] - 搜索内容\n";
            echo "php tools/file_analyzer.php structure [dir]     - 分析文件结构\n";
            echo "php tools/file_analyzer.php quality [pattern]   - 代码质量检查\n";
    }
}
?>
