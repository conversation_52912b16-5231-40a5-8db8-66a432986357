<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * 专业布局分析工具 - ThinkPHP6企业级应用
 * 功能：精准检测页面布局对称性、间距一致性、元素对齐、安全修改建议
 */

class LayoutAnalyzer {

    private $issues = [];
    private $suggestions = [];
    private $severity = ['low', 'medium', 'high', 'critical'];

    /**
     * 分析HTML文件的布局完整性
     */
    public function analyzeSymmetry($htmlFile) {
        if (!file_exists($htmlFile)) {
            echo "❌ 文件不存在: $htmlFile\n";
            return false;
        }

        $content = file_get_contents($htmlFile);
        if (empty($content)) {
            echo "❌ 文件内容为空\n";
            return false;
        }

        echo "🔍 开始分析布局: " . basename($htmlFile) . "\n";
        echo str_repeat("=", 60) . "\n";

        $this->checkGridSymmetry($content);
        $this->checkSpacingConsistency($content);
        $this->checkAlignmentIssues($content);
        $this->checkResponsiveBreakpoints($content);
        $this->checkContainerStructure($content);
        $this->checkFlexboxLayout($content);
        $this->checkCSSClassExistence($content);

        $this->displaySummary();
        return true;
    }
    
    /**
     * 检测网格布局对称性 - 专业版
     */
    private function checkGridSymmetry($content) {
        echo "📐 网格布局对称性检测\n";

        // 使用更精确的方法提取row内容
        $rows = $this->extractRowContents($content);

        if (!empty($rows)) {
            echo "✅ 发现 " . count($rows) . " 个网格行\n";

            foreach ($rows as $index => $rowContent) {
                $this->analyzeGridRow($rowContent, $index + 1);
            }
        } else {
            echo "ℹ️ 未发现Bootstrap网格系统\n";
        }

        // 检测CSS Grid
        if (preg_match('/display:\s*grid|grid-template/', $content)) {
            echo "✅ 检测到CSS Grid布局\n";
            $this->analyzeCSSGrid($content);
        }

        // 检测Flexbox
        if (preg_match_all('/class="[^"]*d-flex[^"]*"/', $content)) {
            echo "✅ 检测到Flexbox布局\n";
            $this->analyzeFlexbox($content);
        }
    }

    /**
     * 提取row内容 - 处理嵌套div的复杂情况
     */
    private function extractRowContents($content) {
        $rows = [];
        $lines = explode("\n", $content);
        $inRow = false;
        $rowContent = '';
        $divDepth = 0;

        foreach ($lines as $line) {
            $trimmedLine = trim($line);

            // 检测row开始
            if (preg_match('/<div[^>]*class="[^"]*row[^"]*"[^>]*>/', $trimmedLine)) {
                $inRow = true;
                $rowContent = '';
                $divDepth = 1;

                // 检查是否是自闭合标签
                if (preg_match('/<div[^>]*\/>/', $trimmedLine)) {
                    $inRow = false;
                    $divDepth = 0;
                }
                continue;
            }

            if ($inRow) {
                $rowContent .= $line . "\n";

                // 计算div深度
                $divDepth += substr_count($trimmedLine, '<div');
                $divDepth -= substr_count($trimmedLine, '</div>');

                // 当div深度回到0时，row结束
                if ($divDepth <= 0) {
                    $rows[] = $rowContent;
                    $inRow = false;
                    $rowContent = '';
                }
            }
        }

        return $rows;
    }

    /**
     * 分析单个网格行 - 修复完整行分析逻辑
     */
    private function analyzeGridRow($rowContent, $rowNumber) {
        // 提取所有直接子列元素（避免嵌套行的干扰）
        preg_match_all('/<div[^>]*class="[^"]*col[^"]*"[^>]*>/', $rowContent, $divMatches);

        if (!empty($divMatches[0])) {
            $elements = [];

            // 分析每个列元素的响应式类
            foreach ($divMatches[0] as $div) {
                $element = [];

                // 提取class属性
                if (preg_match('/class="([^"]*)"/', $div, $classMatch)) {
                    $classes = explode(' ', $classMatch[1]);

                    // 分析每个class
                    foreach ($classes as $class) {
                        $class = trim($class);

                        // 匹配col-{breakpoint}-{width}格式
                        if (preg_match('/^col-(\w+)-(\d+)$/', $class, $match)) {
                            $breakpoint = $match[1];
                            $width = (int)$match[2];
                            $element[$breakpoint] = $width;
                        }
                        // 匹配col-{width}格式（默认xs断点）
                        elseif (preg_match('/^col-(\d+)$/', $class, $match)) {
                            $width = (int)$match[1];
                            $element['xs'] = $width;
                        }
                        // 匹配col格式（自动宽度）
                        elseif ($class === 'col') {
                            $element['auto'] = true;
                        }
                    }
                }

                if (!empty($element)) {
                    $elements[] = $element;
                }
            }

            if (!empty($elements)) {
                // 分析每个断点的总宽度
                $this->analyzeBreakpointWidths($elements, $rowNumber);
            }
        }
    }

    /**
     * 分析断点宽度 - 修复完整行宽度计算
     */
    private function analyzeBreakpointWidths($elements, $rowNumber) {
        $breakpoints = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];
        $analyzed = false;

        // 从大到小检查断点，找到第一个有明确定义的断点
        foreach ($breakpoints as $bp) {
            $totalWidth = 0;
            $widths = [];
            $hasThisBp = false;
            $autoColumns = 0;

            foreach ($elements as $element) {
                // 检查是否有当前断点的明确定义
                if (isset($element[$bp])) {
                    $widths[] = $element[$bp];
                    $totalWidth += $element[$bp];
                    $hasThisBp = true;
                } elseif (isset($element['auto'])) {
                    $autoColumns++;
                    $hasThisBp = true;
                } else {
                    // 使用级联规则：使用更小断点的值
                    $width = $this->getEffectiveWidth($element, $bp);
                    if ($width > 0) {
                        $widths[] = $width;
                        $totalWidth += $width;
                    }
                }
            }

            // 如果有明确定义的断点或者是最小断点，进行分析
            if ($hasThisBp || $bp === 'xs') {
                if ($autoColumns > 0) {
                    echo "  行 $rowNumber ($bp): " . implode(' + ', $widths) . " + {$autoColumns}个auto列";
                    if ($totalWidth <= 12) {
                        echo " ✅\n";
                    } else {
                        echo " ❌ (超过12列)\n";
                        $this->addIssue('high', "第{$rowNumber}行在{$bp}断点超过12列，会导致换行");
                    }
                } else {
                    echo "  行 $rowNumber ($bp): " . implode(' + ', $widths) . " = $totalWidth";

                    if ($totalWidth === 12) {
                        echo " ✅\n";
                    } elseif ($totalWidth < 12 && $totalWidth > 0) {
                        // 只有当总宽度明显不足时才报警（比如只有1-8列）
                        if ($totalWidth < 9) {
                            echo " ⚠️ (未满12列)\n";
                            $this->addIssue('low', "第{$rowNumber}行在{$bp}断点未满12列，可能是设计意图");
                        } else {
                            echo " ⚠️ (接近12列)\n";
                        }
                    } elseif ($totalWidth > 12) {
                        echo " ❌ (超过12列)\n";
                        $this->addIssue('high', "第{$rowNumber}行在{$bp}断点超过12列，会导致换行");
                    } else {
                        echo " ℹ️ (无列定义)\n";
                    }
                }
                $analyzed = true;
                break; // 只分析第一个有定义的断点
            }
        }

        if (!$analyzed) {
            echo "  行 $rowNumber: 未发现有效的网格列定义\n";
        }
    }

    /**
     * 获取有效宽度（考虑Bootstrap级联规则）
     */
    private function getEffectiveWidth($element, $targetBp) {
        $breakpointOrder = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];
        $targetIndex = array_search($targetBp, $breakpointOrder);

        // 从目标断点开始，向下查找定义的宽度
        for ($i = $targetIndex; $i < count($breakpointOrder); $i++) {
            $bp = $breakpointOrder[$i];
            if (isset($element[$bp])) {
                return $element[$bp];
            }
        }

        return 0;
    }
    
    /**
     * 检测间距一致性 - 专业版
     */
    private function checkSpacingConsistency($content) {
        echo "\n📏 间距一致性检测\n";

        // 分析Bootstrap间距类
        $spacingTypes = [
            'm' => 'margin',
            'p' => 'padding',
            'mt' => 'margin-top',
            'mb' => 'margin-bottom',
            'ml' => 'margin-left',
            'mr' => 'margin-right',
            'mx' => 'margin-x',
            'my' => 'margin-y',
            'pt' => 'padding-top',
            'pb' => 'padding-bottom',
            'pl' => 'padding-left',
            'pr' => 'padding-right',
            'px' => 'padding-x',
            'py' => 'padding-y'
        ];

        $allSpacings = [];
        foreach ($spacingTypes as $prefix => $name) {
            preg_match_all("/class=\"[^\"]*{$prefix}-(\d+)[^\"]*\"/", $content, $matches);
            if (!empty($matches[1])) {
                foreach ($matches[1] as $value) {
                    $allSpacings[$value] = ($allSpacings[$value] ?? 0) + 1;
                }
            }
        }

        if (!empty($allSpacings)) {
            arsort($allSpacings);
            echo "间距值使用统计：\n";

            $standardSpacings = [0, 1, 2, 3, 4, 5]; // Bootstrap标准间距
            $nonStandardCount = 0;

            foreach ($allSpacings as $spacing => $count) {
                $status = in_array($spacing, $standardSpacings) ? '✅' : '⚠️';
                echo "  间距-{$spacing}: {$count}次 {$status}\n";

                if (!in_array($spacing, $standardSpacings)) {
                    $nonStandardCount++;
                }
            }

            // 专业建议
            if (count($allSpacings) > 6) {
                $this->addIssue('medium', "使用了" . count($allSpacings) . "种不同间距值，建议精简到4-6种");
            }

            if ($nonStandardCount > 0) {
                $this->addIssue('low', "发现{$nonStandardCount}个非标准间距值，建议使用Bootstrap标准间距(0-5)");
            }
        } else {
            echo "ℹ️ 未发现Bootstrap间距类\n";
        }
    }
    
    /**
     * 检测对齐问题 - 专业版
     */
    private function checkAlignmentIssues($content) {
        echo "\n🎯 对齐问题检测\n";

        // 文本对齐检测
        $textAlignments = [];
        preg_match_all('/class="[^"]*text-(left|center|right|justify)[^"]*"/', $content, $textMatches);
        if (!empty($textMatches[1])) {
            $textAlignments = array_count_values($textMatches[1]);
            echo "文本对齐分布：\n";
            foreach ($textAlignments as $align => $count) {
                echo "  text-{$align}: {$count}次\n";
            }
        }

        // Flexbox对齐检测
        $flexAlignments = [
            'justify-content-start' => 'flex-start',
            'justify-content-center' => 'center',
            'justify-content-end' => 'flex-end',
            'justify-content-between' => 'space-between',
            'justify-content-around' => 'space-around',
            'align-items-start' => 'flex-start',
            'align-items-center' => 'center',
            'align-items-end' => 'flex-end'
        ];

        $flexUsage = [];
        foreach ($flexAlignments as $class => $value) {
            if (preg_match_all("/class=\"[^\"]*{$class}[^\"]*\"/", $content)) {
                $flexUsage[$class] = preg_match_all("/class=\"[^\"]*{$class}[^\"]*\"/", $content);
            }
        }

        if (!empty($flexUsage)) {
            echo "Flexbox对齐使用：\n";
            foreach ($flexUsage as $class => $count) {
                echo "  {$class}: {$count}次\n";
            }
        }

        // 检测对齐一致性问题
        if (count($textAlignments) > 3) {
            $this->addIssue('medium', "使用了过多文本对齐方式，建议统一设计规范");
        }

        // 检测混合对齐方式
        if (!empty($textAlignments) && !empty($flexUsage)) {
            $this->addSuggestion("同时使用了文本对齐和Flexbox对齐，建议统一使用Flexbox");
        }
    }
    
    /**
     * 检测响应式断点 - 专业版
     */
    private function checkResponsiveBreakpoints($content) {
        echo "\n📱 响应式断点检测\n";

        $breakpoints = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'];
        $responsiveUsage = [];

        foreach ($breakpoints as $bp) {
            $colCount = preg_match_all("/col-{$bp}-\d+/", $content);
            $displayCount = preg_match_all("/d-{$bp}-(none|block|inline|flex)/", $content);
            $spacingCount = preg_match_all("/[mp][tblrxy]?-{$bp}-\d+/", $content);

            $total = $colCount + $displayCount + $spacingCount;
            if ($total > 0) {
                $responsiveUsage[$bp] = [
                    'grid' => $colCount,
                    'display' => $displayCount,
                    'spacing' => $spacingCount,
                    'total' => $total
                ];
            }
        }

        if (!empty($responsiveUsage)) {
            echo "响应式类使用统计：\n";
            foreach ($responsiveUsage as $bp => $usage) {
                echo "  {$bp}: 网格{$usage['grid']} + 显示{$usage['display']} + 间距{$usage['spacing']} = {$usage['total']}个\n";
            }

            // 检测响应式覆盖度
            $coverageScore = count($responsiveUsage) / count($breakpoints) * 100;
            echo "响应式覆盖度: " . round($coverageScore, 1) . "%\n";

            if ($coverageScore < 50) {
                $this->addIssue('medium', "响应式覆盖度较低({$coverageScore}%)，建议增加移动端适配");
            }
        } else {
            echo "⚠️ 未发现响应式断点类，可能缺少移动端适配\n";
            $this->addIssue('high', "页面缺少响应式设计，需要添加移动端适配");
        }
    }

    /**
     * 检测容器结构
     */
    private function checkContainerStructure($content) {
        echo "\n📦 容器结构检测\n";

        $containers = [
            'container' => preg_match_all('/class="[^"]*container[^"]*"/', $content),
            'container-fluid' => preg_match_all('/class="[^"]*container-fluid[^"]*"/', $content),
            'container-sm' => preg_match_all('/class="[^"]*container-sm[^"]*"/', $content),
            'container-md' => preg_match_all('/class="[^"]*container-md[^"]*"/', $content),
            'container-lg' => preg_match_all('/class="[^"]*container-lg[^"]*"/', $content),
            'container-xl' => preg_match_all('/class="[^"]*container-xl[^"]*"/', $content)
        ];

        $totalContainers = array_sum($containers);
        if ($totalContainers > 0) {
            echo "容器类型分布：\n";
            foreach ($containers as $type => $count) {
                if ($count > 0) {
                    echo "  {$type}: {$count}个\n";
                }
            }

            // 检测嵌套容器
            if (preg_match('/<div[^>]*container[^>]*>.*<div[^>]*container[^>]*>/s', $content)) {
                $this->addIssue('medium', "检测到嵌套容器，可能导致布局问题");
            }
        } else {
            echo "ℹ️ 未发现Bootstrap容器\n";
        }
    }
    
    /**
     * 检测Flexbox布局
     */
    private function checkFlexboxLayout($content) {
        echo "\n🔧 Flexbox布局检测\n";

        $flexClasses = [
            'd-flex' => '基础flex',
            'd-inline-flex' => '行内flex',
            'flex-row' => '水平排列',
            'flex-column' => '垂直排列',
            'flex-wrap' => '允许换行',
            'flex-nowrap' => '禁止换行'
        ];

        $flexUsage = [];
        foreach ($flexClasses as $class => $desc) {
            $count = preg_match_all("/class=\"[^\"]*{$class}[^\"]*\"/", $content);
            if ($count > 0) {
                $flexUsage[$class] = $count;
                echo "  {$desc}({$class}): {$count}次\n";
            }
        }

        if (empty($flexUsage)) {
            echo "ℹ️ 未使用Flexbox布局\n";
        }
    }

    /**
     * 检测CSS类存在性
     */
    private function checkCSSClassExistence($content) {
        echo "\n🎨 CSS类存在性检测\n";

        // 提取所有class属性
        preg_match_all('/class="([^"]*)"/', $content, $matches);
        $allClasses = [];

        foreach ($matches[1] as $classString) {
            $classes = explode(' ', trim($classString));
            foreach ($classes as $class) {
                $class = trim($class);
                if (!empty($class)) {
                    $allClasses[$class] = ($allClasses[$class] ?? 0) + 1;
                }
            }
        }

        if (!empty($allClasses)) {
            $totalClasses = count($allClasses);
            $uniqueClasses = array_keys($allClasses);

            echo "CSS类统计: {$totalClasses}个不同类\n";

            // 检测可能的自定义类
            $customClasses = [];
            $bootstrapPrefixes = ['col-', 'row', 'container', 'd-', 'text-', 'bg-', 'btn', 'form-', 'nav', 'card', 'alert', 'badge', 'modal', 'table', 'list-', 'dropdown', 'pagination', 'breadcrumb', 'progress', 'spinner', 'toast', 'tooltip', 'popover', 'carousel', 'accordion', 'offcanvas'];

            foreach ($uniqueClasses as $class) {
                $isBootstrap = false;
                foreach ($bootstrapPrefixes as $prefix) {
                    if (strpos($class, $prefix) === 0) {
                        $isBootstrap = true;
                        break;
                    }
                }
                if (!$isBootstrap && !in_array($class, ['active', 'show', 'hide', 'disabled', 'selected'])) {
                    $customClasses[] = $class;
                }
            }

            if (!empty($customClasses)) {
                echo "自定义类(" . count($customClasses) . "个): " . implode(', ', array_slice($customClasses, 0, 10));
                if (count($customClasses) > 10) {
                    echo "...";
                }
                echo "\n";

                if (count($customClasses) > 20) {
                    $this->addIssue('medium', "自定义CSS类过多(" . count($customClasses) . "个)，建议整理和复用");
                }
            }
        }
    }

    /**
     * 分析CSS Grid布局
     */
    private function analyzeCSSGrid($content) {
        echo "  CSS Grid属性检测:\n";

        $gridProperties = [
            'grid-template-columns' => '列模板',
            'grid-template-rows' => '行模板',
            'grid-gap' => '网格间距',
            'gap' => '间距',
            'grid-auto-flow' => '自动流向'
        ];

        foreach ($gridProperties as $property => $desc) {
            if (preg_match("/{$property}:/", $content)) {
                echo "    ✅ {$desc}({$property})\n";
            }
        }
    }

    /**
     * 分析Flexbox布局
     */
    private function analyzeFlexbox($content) {
        echo "  Flexbox类使用:\n";

        $flexClasses = [
            'justify-content-center' => '水平居中',
            'justify-content-between' => '两端对齐',
            'align-items-center' => '垂直居中',
            'flex-wrap' => '允许换行'
        ];

        foreach ($flexClasses as $class => $desc) {
            $count = preg_match_all("/class=\"[^\"]*{$class}[^\"]*\"/", $content);
            if ($count > 0) {
                echo "    ✅ {$desc}({$class}): {$count}次\n";
            }
        }
    }

    /**
     * 添加问题记录
     */
    private function addIssue($severity, $message) {
        $this->issues[] = [
            'severity' => $severity,
            'message' => $message
        ];
    }

    /**
     * 添加建议
     */
    private function addSuggestion($message) {
        $this->suggestions[] = $message;
    }

    /**
     * 显示分析总结
     */
    private function displaySummary() {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📊 分析总结\n";

        if (!empty($this->issues)) {
            echo "\n❗ 发现问题 (" . count($this->issues) . "个):\n";
            foreach ($this->issues as $issue) {
                $icon = $this->getSeverityIcon($issue['severity']);
                echo "  {$icon} [{$issue['severity']}] {$issue['message']}\n";
            }
        } else {
            echo "\n✅ 未发现布局问题\n";
        }

        if (!empty($this->suggestions)) {
            echo "\n💡 优化建议:\n";
            foreach ($this->suggestions as $suggestion) {
                echo "  • {$suggestion}\n";
            }
        }

        echo "\n🎯 布局质量评分: " . $this->calculateScore() . "/100\n";
    }

    /**
     * 获取严重程度图标
     */
    private function getSeverityIcon($severity) {
        $icons = [
            'low' => '🟡',
            'medium' => '🟠',
            'high' => '🔴',
            'critical' => '💀'
        ];
        return $icons[$severity] ?? '⚪';
    }

    /**
     * 计算布局质量评分
     */
    private function calculateScore() {
        $score = 100;

        foreach ($this->issues as $issue) {
            switch ($issue['severity']) {
                case 'low':
                    $score -= 5;
                    break;
                case 'medium':
                    $score -= 10;
                    break;
                case 'high':
                    $score -= 20;
                    break;
                case 'critical':
                    $score -= 30;
                    break;
            }
        }

        return max(0, $score);
    }

    /**
     * 批量分析目录下的所有HTML文件
     */
    public function batchAnalyze($directory) {
        echo "📁 批量分析目录: $directory\n";
        echo str_repeat("=", 60) . "\n";

        $htmlFiles = $this->findHtmlFiles($directory);
        if (empty($htmlFiles)) {
            echo "❌ 未找到HTML文件\n";
            return;
        }

        $results = [];
        foreach ($htmlFiles as $file) {
            echo "\n🔍 分析文件: " . basename($file) . "\n";
            echo str_repeat("-", 40) . "\n";

            // 重置问题和建议
            $this->issues = [];
            $this->suggestions = [];

            $result = $this->analyzeSymmetry($file);
            if ($result) {
                $score = $this->calculateScore();
                $results[] = [
                    'file' => $file,
                    'score' => $score,
                    'issues' => count($this->issues),
                    'critical_issues' => count(array_filter($this->issues, function($issue) {
                        return in_array($issue['severity'], ['critical', 'high']);
                    }))
                ];
            }
        }

        // 生成批量分析报告
        $this->generateBatchReport($results);
    }

    /**
     * 查找目录下的HTML文件
     */
    private function findHtmlFiles($directory) {
        $files = [];
        if (!is_dir($directory)) {
            return $files;
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'html') {
                $files[] = $file->getPathname();
            }
        }

        return $files;
    }

    /**
     * 生成批量分析报告
     */
    private function generateBatchReport($results) {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📊 批量分析报告\n";
        echo str_repeat("=", 60) . "\n";

        if (empty($results)) {
            echo "❌ 无分析结果\n";
            return;
        }

        // 按评分排序
        usort($results, function($a, $b) {
            return $b['score'] - $a['score'];
        });

        $totalFiles = count($results);
        $avgScore = array_sum(array_column($results, 'score')) / $totalFiles;
        $criticalFiles = count(array_filter($results, function($r) {
            return $r['critical_issues'] > 0;
        }));

        echo "📈 总体统计:\n";
        echo "  • 分析文件数: $totalFiles\n";
        echo "  • 平均评分: " . round($avgScore, 1) . "/100\n";
        echo "  • 严重问题文件: $criticalFiles\n\n";

        echo "📋 文件评分排行:\n";
        foreach ($results as $result) {
            $filename = basename($result['file']);
            $status = $result['score'] >= 80 ? '✅' : ($result['score'] >= 60 ? '⚠️' : '❌');
            echo sprintf("  %s %-30s 评分:%3d 问题:%2d 严重:%d\n",
                $status, $filename, $result['score'], $result['issues'], $result['critical_issues']);
        }

        echo "\n🎯 改进建议:\n";
        if ($avgScore < 70) {
            echo "  • 整体质量偏低，建议优先修复严重问题\n";
        }
        if ($criticalFiles > 0) {
            echo "  • $criticalFiles 个文件存在严重问题，需要立即修复\n";
        }
        echo "  • 建议建立布局规范模板，统一设计标准\n";
    }

    /**
     * 生成专业修复建议
     */
    public function generateFixSuggestions($htmlFile) {
        echo "\n🔧 专业修复建议\n";
        echo str_repeat("=", 60) . "\n";

        if (!empty($this->issues)) {
            echo "基于检测到的问题，提供以下修复方案：\n\n";

            $groupedIssues = [];
            foreach ($this->issues as $issue) {
                $groupedIssues[$issue['severity']][] = $issue['message'];
            }

            foreach (['critical', 'high', 'medium', 'low'] as $severity) {
                if (!empty($groupedIssues[$severity])) {
                    echo "🔥 {$severity}级问题修复:\n";
                    foreach ($groupedIssues[$severity] as $message) {
                        echo "  • {$message}\n";
                        echo "    " . $this->getFixSuggestion($message) . "\n\n";
                    }
                }
            }
        } else {
            echo "✅ 布局质量良好，无需修复\n";
        }

        // 通用优化建议
        echo "📋 通用优化建议:\n";
        echo "  1. 使用Bootstrap标准间距系统 (0-5)\n";
        echo "  2. 保持12列网格系统的对称性\n";
        echo "  3. 统一使用Flexbox进行对齐\n";
        echo "  4. 确保响应式断点覆盖主要设备\n";
        echo "  5. 避免过度嵌套和冗余CSS类\n";
    }

    /**
     * 根据问题获取具体修复建议
     */
    private function getFixSuggestion($message) {
        if (strpos($message, '网格') !== false) {
            return "检查row内的col类，确保总和为12";
        } elseif (strpos($message, '间距') !== false) {
            return "使用Bootstrap标准间距类: m-0到m-5, p-0到p-5";
        } elseif (strpos($message, '响应式') !== false) {
            return "添加col-sm-*, col-md-*, col-lg-*等响应式类";
        } elseif (strpos($message, '对齐') !== false) {
            return "统一使用justify-content-*和align-items-*类";
        } elseif (strpos($message, 'CSS类') !== false) {
            return "整理自定义CSS，提取公共样式到common.css";
        } else {
            return "参考Bootstrap文档进行标准化改进";
        }
    }
}

// 命令行使用
if (isset($argv[1])) {
    $analyzer = new LayoutAnalyzer();

    switch ($argv[1]) {
        case 'analyze':
            if (isset($argv[2])) {
                echo "🚀 启动布局分析...\n\n";
                $result = $analyzer->analyzeSymmetry($argv[2]);
                if ($result) {
                    echo "\n✅ 分析完成！\n";
                }
            } else {
                echo "❌ 用法: php layout_analyzer.php analyze 文件路径\n";
            }
            break;

        case 'fix':
            if (isset($argv[2])) {
                echo "🚀 生成修复建议...\n\n";
                // 先分析再生成建议
                $analyzer->analyzeSymmetry($argv[2]);
                $analyzer->generateFixSuggestions($argv[2]);
            } else {
                echo "❌ 用法: php layout_analyzer.php fix 文件路径\n";
            }
            break;

        case 'check':
            if (isset($argv[2])) {
                echo "🚀 启动完整检测...\n\n";
                $result = $analyzer->analyzeSymmetry($argv[2]);
                if ($result) {
                    $analyzer->generateFixSuggestions($argv[2]);
                    echo "\n🎉 检测完成！\n";
                }
            } else {
                echo "❌ 用法: php layout_analyzer.php check 文件路径\n";
            }
            break;

        case 'help':
        case '--help':
        case '-h':
            echo "🔧 专业布局分析工具 - 三只鱼网络科技\n\n";
            echo "📋 可用命令：\n";
            echo "  analyze 文件路径  - 深度分析布局对称性和结构\n";
            echo "  fix 文件路径      - 生成专业修复建议和优化方案\n";
            echo "  check 文件路径    - 完整检测+修复建议(推荐)\n";
            echo "  help             - 显示此帮助信息\n\n";
            echo "💡 示例：\n";
            echo "  php layout_analyzer.php check app/view/index/index.html\n";
            echo "  php layout_analyzer.php analyze public/admin/dashboard.html\n\n";
            echo "🎯 功能特性：\n";
            echo "  • Bootstrap网格系统对称性检测\n";
            echo "  • 间距一致性和标准化分析\n";
            echo "  • Flexbox和CSS Grid布局检测\n";
            echo "  • 响应式断点覆盖度评估\n";
            echo "  • CSS类存在性和冗余检测\n";
            echo "  • 专业修复建议和质量评分\n";
            break;

        case 'batch':
            if (isset($argv[2])) {
                echo "🚀 启动批量检测...\n\n";
                $analyzer->batchAnalyze($argv[2]);
            } else {
                echo "❌ 用法: php layout_analyzer.php batch 目录路径\n";
            }
            break;

        default:
            echo "❌ 未知命令: {$argv[1]}\n";
            echo "💡 使用 'php layout_analyzer.php help' 查看帮助\n";
    }
} else {
    echo "🔧 专业布局分析工具\n";
    echo "💡 使用 'php layout_analyzer.php help' 查看使用说明\n";
}
