if(typeof cptable === 'undefined') cptable = {};
cptable[52936] = (function(){ var d = [], e = {}, D = [], j;
D[0] = "\u0000\u0001\u0002\u0003\u0004\u0005\u0006\u0007\b\t\n\u000b\f\r\u000e\u000f\u0010\u0011\u0012\u0013\u0014\u0015\u0016\u0017\u0018\u0019\u001a\u001b\u001c\u001d\u001e\u001f !\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}�€������������������������������������������������������������������������������������������������������������������������������".split("");
for(j = 0; j != D[0].length; ++j) if(D[0][j].charCodeAt(0) !== 0xFFFD) { e[D[0][j]] = 0 + j; d[0 + j] = D[0][j];}
D[126] = "���������������������������������������������������������������������������������������������������������������������������~���������������������������������������������������������������������������������������������������������������������������������".split("");
for(j = 0; j != D[126].length; ++j) if(D[126][j].charCodeAt(0) !== 0xFFFD) { e[D[126][j]] = 32256 + j; d[32256 + j] = D[126][j];}
return {"enc": e, "dec": d }; })();
