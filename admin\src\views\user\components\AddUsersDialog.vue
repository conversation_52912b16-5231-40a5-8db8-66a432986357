<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 添加用户到角色对话框
-->

<template>
  <el-dialog
    v-model="visible"
    title="添加用户到角色"
    width="800px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="add-users-container">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input
          v-model="searchForm.keyword"
          placeholder="搜索用户名、姓名、邮箱"
          style="width: 300px"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </div>

      <!-- 用户列表 -->
      <el-table
        v-loading="loading"
        :data="userList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="username" label="用户名" width="120" />
        
        <el-table-column prop="realName" label="姓名" width="100" />
        
        <el-table-column prop="email" label="邮箱" width="180" />
        
        <el-table-column prop="phone" label="手机号" width="120" />
        
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="roles" label="当前角色" min-width="150">
          <template #default="{ row }">
            <el-tag
              v-for="role in row.roles"
              :key="role.id"
              size="small"
              style="margin-right: 4px"
            >
              {{ role.name }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 已选择用户 -->
      <div v-if="selectedUsers.length > 0" class="selected-users">
        <div class="selected-header">
          <span>已选择 {{ selectedUsers.length }} 个用户</span>
          <el-button type="text" @click="clearSelection">清空选择</el-button>
        </div>
        <div class="selected-list">
          <el-tag
            v-for="user in selectedUsers"
            :key="user.id"
            closable
            @close="removeSelection(user)"
          >
            {{ user.realName }}({{ user.username }})
          </el-tag>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          :disabled="selectedUsers.length === 0"
        >
          确认添加 ({{ selectedUsers.length }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import { userApi } from '@/api/user'
import { roleApi } from '@/api/role'
import type { Role, User } from '@/types/auth'

interface Props {
  modelValue: boolean
  roleData?: Role | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const loading = ref(false)
const submitting = ref(false)
const userList = ref<User[]>([])
const selectedUsers = ref<User[]>([])

// 搜索表单
const searchForm = reactive({
  keyword: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 控制对话框显示
const visible = ref(false)

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val
    if (val) {
      loadUsers()
    }
  },
  { immediate: true }
)

watch(visible, (val) => {
  emit('update:modelValue', val)
  if (!val) {
    // 关闭时清空选择
    selectedUsers.value = []
  }
})

/**
 * 加载用户列表（排除已有该角色的用户）
 */
const loadUsers = async () => {
  if (!props.roleData?.id) return

  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      size: pagination.size,
      keyword: searchForm.keyword,
      excludeRoleId: props.roleData.id // 排除已有该角色的用户
    }
    
    const { data } = await userApi.getList(params)
    userList.value = data.list
    pagination.total = data.total
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 搜索
 */
const handleSearch = () => {
  pagination.page = 1
  loadUsers()
}

/**
 * 重置
 */
const handleReset = () => {
  searchForm.keyword = ''
  pagination.page = 1
  loadUsers()
}

/**
 * 处理选择变化
 */
const handleSelectionChange = (selection: User[]) => {
  selectedUsers.value = selection
}

/**
 * 处理页码变化
 */
const handleCurrentChange = () => {
  loadUsers()
}

/**
 * 处理页大小变化
 */
const handleSizeChange = () => {
  pagination.page = 1
  loadUsers()
}

/**
 * 清空选择
 */
const clearSelection = () => {
  selectedUsers.value = []
}

/**
 * 移除选择
 */
const removeSelection = (user: User) => {
  const index = selectedUsers.value.findIndex(u => u.id === user.id)
  if (index > -1) {
    selectedUsers.value.splice(index, 1)
  }
}

/**
 * 提交添加
 */
const handleSubmit = async () => {
  if (!props.roleData?.id || selectedUsers.value.length === 0) return

  try {
    submitting.value = true
    
    const userIds = selectedUsers.value.map(user => user.id)
    await roleApi.addRoleUsers(props.roleData.id, userIds)
    
    ElMessage.success(`成功添加 ${selectedUsers.value.length} 个用户到角色`)
    emit('success')
    handleClose()
  } catch (error) {
    console.error('添加用户到角色失败:', error)
    ElMessage.error('添加用户到角色失败')
  } finally {
    submitting.value = false
  }
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.add-users-container {
  .search-bar {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    align-items: center;
  }

  .pagination {
    margin-top: 20px;
    text-align: right;
  }

  .selected-users {
    margin-top: 20px;
    padding: 16px;
    background: var(--el-fill-color-lighter);
    border-radius: 6px;

    .selected-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      font-weight: 500;
    }

    .selected-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
