<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 绑定手机号对话框
-->

<template>
  <el-dialog
    v-model="visible"
    title="绑定手机号"
    width="450px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="form.phone"
          placeholder="请输入手机号"
          maxlength="11"
          clearable
        >
          <template #prefix>
            <span>+86</span>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="验证码" prop="code">
        <div class="code-input-group">
          <el-input
            v-model="form.code"
            placeholder="请输入验证码"
            maxlength="6"
            clearable
          />
          <el-button
            :disabled="!canSendCode || countdown > 0"
            @click="sendCode"
            :loading="sendingCode"
          >
            {{ countdown > 0 ? `${countdown}s后重发` : '发送验证码' }}
          </el-button>
        </div>
      </el-form-item>

      <el-form-item>
        <div class="tips">
          <el-icon><InfoFilled /></el-icon>
          <span>验证码将发送到您的手机，请注意查收</span>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确认绑定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { userApi } from '@/api/user'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success', phone: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)
const sendingCode = ref(false)
const countdown = ref(0)
let countdownTimer: NodeJS.Timeout | null = null

// 表单数据
const form = reactive({
  phone: '',
  code: ''
})

// 是否可以发送验证码
const canSendCode = computed(() => {
  return /^1[3-9]\d{9}$/.test(form.phone)
})

// 自定义验证函数
const validatePhone = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请输入手机号'))
    return
  }
  
  if (!/^1[3-9]\d{9}$/.test(value)) {
    callback(new Error('请输入正确的手机号'))
    return
  }
  
  callback()
}

const validateCode = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请输入验证码'))
    return
  }
  
  if (!/^\d{6}$/.test(value)) {
    callback(new Error('验证码为6位数字'))
    return
  }
  
  callback()
}

// 表单验证规则
const rules: FormRules = {
  phone: [
    { validator: validatePhone, trigger: 'blur' }
  ],
  code: [
    { validator: validateCode, trigger: 'blur' }
  ]
}

// 控制对话框显示
const visible = ref(false)

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val
    if (val) {
      resetForm()
    } else {
      clearCountdown()
    }
  },
  { immediate: true }
)

watch(visible, (val) => {
  emit('update:modelValue', val)
})

/**
 * 发送验证码
 */
const sendCode = async () => {
  if (!canSendCode.value || sendingCode.value) return

  try {
    sendingCode.value = true
    
    await userApi.sendSmsCode({
      phone: form.phone,
      type: 'bind'
    })
    
    ElMessage.success('验证码发送成功')
    startCountdown()
  } catch (error) {
    console.error('发送验证码失败:', error)
    ElMessage.error('发送验证码失败')
  } finally {
    sendingCode.value = false
  }
}

/**
 * 开始倒计时
 */
const startCountdown = () => {
  countdown.value = 60
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearCountdown()
    }
  }, 1000)
}

/**
 * 清除倒计时
 */
const clearCountdown = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
  countdown.value = 0
}

/**
 * 重置表单
 */
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    phone: '',
    code: ''
  })
  clearCountdown()
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    await userApi.bindPhone({
      phone: form.phone,
      code: form.code
    })

    ElMessage.success('手机号绑定成功')
    emit('success', form.phone)
    handleClose()
  } catch (error) {
    console.error('绑定手机号失败:', error)
    ElMessage.error('绑定手机号失败，请检查验证码是否正确')
  } finally {
    loading.value = false
  }
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.code-input-group {
  display: flex;
  gap: 12px;

  .el-input {
    flex: 1;
  }

  .el-button {
    white-space: nowrap;
    min-width: 100px;
  }
}

.tips {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-secondary);
  font-size: 14px;

  .el-icon {
    color: var(--el-color-info);
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-input__prefix) {
  color: var(--el-text-color-regular);
  font-weight: 500;
}
</style>
