import { defineComponent, onMounted } from "vue";
import { initToolbar } from "@stagewise/toolbar";
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "StagewiseToolbar",
  props: {
    config: {},
    enabled: { type: Boolean, default: () => process.env.NODE_ENV === "development" }
  },
  setup(__props) {
    const props = __props;
    onMounted(() => {
      if (props.enabled) {
        initToolbar(props.config);
      }
    });
    return (_ctx, _cache) => {
      return null;
    };
  }
});
export {
  _sfc_main as StagewiseToolbar
};
