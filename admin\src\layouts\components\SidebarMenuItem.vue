<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 侧边栏菜单项组件
-->

<template>
  <template v-if="!item.meta?.hidden">
    <!-- 单个菜单项 -->
    <el-menu-item
      v-if="!hasChildren"
      :index="resolvePath(item.path)"
      :class="{ 'is-active': isActive }"
    >
      <el-icon v-if="item.meta?.icon">
        <component :is="item.meta.icon" />
      </el-icon>
      <template #title>
        <span>{{ item.meta?.title || item.name }}</span>
      </template>
    </el-menu-item>

    <!-- 子菜单 -->
    <el-sub-menu
      v-else
      :index="resolvePath(item.path)"
      :class="{ 'is-active': hasActiveChild }"
    >
      <template #title>
        <el-icon v-if="item.meta?.icon">
          <component :is="item.meta.icon" />
        </el-icon>
        <span>{{ item.meta?.title || item.name }}</span>
      </template>
      
      <template v-for="child in item.children" :key="child.path">
        <SidebarMenuItem
          :item="child"
          :base-path="resolvePath(child.path)"
        />
      </template>
    </el-sub-menu>
  </template>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import type { RouteRecordNormalized } from 'vue-router'

interface Props {
  item: RouteRecordNormalized
  basePath: string
}

const props = defineProps<Props>()
const route = useRoute()

// 是否有子菜单
const hasChildren = computed(() => {
  return props.item.children && props.item.children.length > 0
})

// 是否有活动的子菜单
const hasActiveChild = computed(() => {
  if (!hasChildren.value) return false
  
  return props.item.children?.some(child => {
    const childPath = resolvePath(child.path)
    return route.path.startsWith(childPath)
  })
})

// 当前菜单是否激活
const isActive = computed(() => {
  const itemPath = resolvePath(props.item.path)
  return route.path === itemPath || route.meta?.activeMenu === itemPath
})

/**
 * 解析路径
 */
const resolvePath = (path: string) => {
  if (path.startsWith('/')) {
    return path
  }
  return `${props.basePath}/${path}`.replace(/\/+/g, '/')
}
</script>

<style lang="scss" scoped>
// 样式继承自父组件
</style>
