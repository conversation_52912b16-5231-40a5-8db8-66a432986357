# 🚀 企业DIY网站管理系统

**三只鱼网络科技 | 韩总 | 2024-12-19**  
**项目代号：QiyeDIY - 新一代企业DIY建站系统**  
**技术栈：ThinkPHP8 + Vue3 + Nuxt3 + GrapesJS**

---

## 📋 项目概述

### 🎯 项目简介
QiyeDIY是一个全新的企业级DIY网站管理系统，采用现代化的前后端分离架构，提供强大的可视化编辑功能，让非技术人员也能轻松创建和管理专业的企业网站。

### ✨ 核心特性
- 🎨 **全站DIY化** - 所有页面都可通过可视化编辑器创建
- 🧩 **组件化设计** - 丰富的组件库，支持自定义组件
- 📱 **响应式设计** - 自动适配桌面、平板、手机
- 🔍 **SEO友好** - 完善的SEO管理和优化功能
- 👥 **权限管理** - 多角色权限控制系统
- ⚡ **高性能** - 缓存策略和CDN支持
- 🔒 **安全可靠** - 完善的安全防护机制
- 🤖 **AI可视化编程** - 集成Stagewise工具栏，支持AI辅助编程

### 🏗️ 技术架构

```
┌─────────────────────────────────────────────────────────┐
│                    前端展示层 (Nuxt3)                     │
│              SSR渲染 + 响应式设计 + SEO优化                │
├─────────────────────────────────────────────────────────┤
│                  管理后台 (Vue3 + TS)                     │
│           Element Plus + GrapesJS + 可视化编辑器           │
├─────────────────────────────────────────────────────────┤
│                   后端API (ThinkPHP8)                     │
│              RESTful API + JWT认证 + 权限控制              │
├─────────────────────────────────────────────────────────┤
│                   数据存储层 (MySQL8)                     │
│              主数据库 + Redis缓存 + 文件存储               │
└─────────────────────────────────────────────────────────┘
```

---

## 📁 项目结构

```
qiyediy/
├── backend/                 # 后端API服务 (ThinkPHP8)
│   ├── app/                # 应用目录
│   │   ├── controller/     # 控制器
│   │   ├── model/          # 模型
│   │   ├── service/        # 服务层
│   │   ├── middleware/     # 中间件
│   │   └── validate/       # 验证器
│   ├── config/             # 配置文件
│   ├── database/           # 数据库文件
│   │   ├── migrations/     # 数据库迁移
│   │   └── seeds/          # 数据填充
│   ├── public/             # 公共目录
│   └── composer.json       # 依赖管理
├── admin/                   # 管理后台 (Vue3 + TypeScript)
│   ├── src/                # 源码目录
│   │   ├── components/     # 组件
│   │   ├── views/          # 页面
│   │   ├── router/         # 路由
│   │   ├── store/          # 状态管理
│   │   ├── api/            # API接口
│   │   └── utils/          # 工具函数
│   ├── public/             # 静态资源
│   ├── package.json        # 依赖管理
│   └── vite.config.ts      # 构建配置
├── frontend/                # 前端展示 (Nuxt3)
│   ├── components/         # 组件
│   ├── pages/              # 页面
│   ├── layouts/            # 布局
│   ├── plugins/            # 插件
│   ├── middleware/         # 中间件
│   ├── composables/        # 组合式函数
│   ├── assets/             # 资源文件
│   ├── public/             # 静态文件
│   ├── package.json        # 依赖管理
│   └── nuxt.config.ts      # 配置文件
├── shared/                  # 共享资源
│   ├── types/              # TypeScript类型定义
│   ├── utils/              # 通用工具函数
│   ├── constants/          # 常量定义
│   └── schemas/            # 数据模式定义
├── docs/                   # 项目文档
│   ├── api/                # API文档
│   ├── development/        # 开发文档
│   └── deployment/         # 部署文档
├── docker/                 # Docker配置
│   ├── backend/            # 后端容器配置
│   ├── admin/              # 管理后台容器配置
│   ├── frontend/           # 前端容器配置
│   └── docker-compose.yml  # 容器编排
├── scripts/                # 脚本文件
│   ├── build.sh            # 构建脚本
│   ├── deploy.sh           # 部署脚本
│   └── init.sh             # 初始化脚本
├── tests/                  # 测试文件
│   ├── backend/            # 后端测试
│   ├── admin/              # 管理后台测试
│   └── frontend/           # 前端测试
├── .env.example            # 环境变量示例
├── .gitignore              # Git忽略文件
├── package.json            # 根目录依赖管理
└── README.md               # 项目说明
```

---

## 🛠️ 技术栈详情

### 后端技术栈
- **框架**: ThinkPHP 8.0 - 现代化PHP框架
- **数据库**: MySQL 8.0 - 主数据库
- **缓存**: Redis 7.0 - 缓存和会话存储
- **认证**: JWT - 无状态身份认证
- **文档**: Swagger/OpenAPI - API文档生成
- **测试**: PHPUnit - 单元测试框架

### 前端管理后台技术栈
- **框架**: Vue 3.4 + TypeScript - 现代化前端框架
- **构建工具**: Vite 5.0 - 快速构建工具
- **UI组件**: Element Plus - 企业级UI组件库
- **状态管理**: Pinia - 轻量级状态管理
- **路由**: Vue Router 4 - 单页面路由
- **DIY编辑器**: GrapesJS - 专业可视化编辑器
- **图标**: @element-plus/icons-vue - 图标库
- **HTTP客户端**: Axios - HTTP请求库

### 前端展示技术栈
- **框架**: Nuxt 3.8 - Vue.js全栈框架
- **样式**: TailwindCSS 3.4 - 原子化CSS框架
- **UI组件**: Headless UI - 无样式组件库
- **图标**: Heroicons - 精美图标库
- **动画**: Framer Motion - 动画库
- **SEO**: Nuxt SEO - SEO优化模块

### DIY编辑器技术栈
- **核心**: GrapesJS 0.21 - 可视化网页编辑器
- **插件生态**:
  - grapesjs-blocks-basic - 基础组件
  - grapesjs-plugin-forms - 表单组件
  - grapesjs-component-countdown - 倒计时组件
  - grapesjs-plugin-export - 导出功能
  - grapesjs-tui-image-editor - 图片编辑
  - grapesjs-style-gradient - 渐变样式
- **自定义组件**: 企业级业务组件库
- **响应式**: 内置响应式设计支持

---

## 🗄️ 数据库设计

### 核心表结构

#### 用户和权限管理
```sql
-- 用户表
users (id, username, email, password, avatar, status, created_at, updated_at)

-- 角色表  
roles (id, name, description, permissions, created_at, updated_at)

-- 用户角色关联表
user_roles (user_id, role_id)
```

#### DIY系统核心
```sql
-- DIY页面表
diy_pages (id, title, slug, content, template_id, seo_title, seo_description, status, created_at, updated_at)

-- 组件表
diy_components (id, name, type, config, preview, category, created_at, updated_at)

-- 模板表
diy_templates (id, name, description, preview, content, category, created_at, updated_at)

-- 页面区块表
diy_blocks (id, page_id, component_id, config, sort_order, created_at, updated_at)
```

#### 内容管理
```sql
-- 统一内容表
contents (id, type, title, slug, content, excerpt, featured_image, status, created_at, updated_at)

-- 内容分类表
content_categories (id, name, slug, type, parent_id, sort_order, created_at, updated_at)

-- 内容标签表
content_tags (id, name, slug, created_at, updated_at)

-- 内容标签关联表
content_tag_relations (content_id, tag_id)

-- 媒体文件表
media_files (id, filename, original_name, mime_type, size, path, alt_text, created_at, updated_at)
```

#### 系统配置
```sql
-- 网站设置表
site_settings (id, key, value, type, group, description, created_at, updated_at)

-- SEO设置表
seo_settings (id, page_type, page_id, title, description, keywords, og_image, created_at, updated_at)

-- 菜单项表
menu_items (id, menu_id, title, url, target, icon, parent_id, sort_order, created_at, updated_at)
```

#### 扩展功能
```sql
-- 联系表单表
contact_forms (id, name, email, phone, subject, message, status, created_at, updated_at)

-- 统计分析表
analytics (id, page_id, event_type, data, ip_address, user_agent, created_at)

-- 操作日志表
logs (id, user_id, action, resource, resource_id, data, ip_address, created_at)
```

---

## 🚀 快速开始

### 环境要求
- PHP >= 8.1
- MySQL >= 8.0
- Redis >= 7.0
- Node.js >= 18.0
- Composer >= 2.0
- npm/yarn/pnpm

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url> qiyediy
cd qiyediy
```

2. **后端安装**
```bash
cd backend
composer install
cp .env.example .env
# 配置数据库连接信息
php think migrate:run
php think seed:run
php think serve
```

3. **管理后台安装**
```bash
cd admin
npm install
cp .env.example .env
# 配置API地址
npm run dev
```

4. **前端展示安装**
```bash
cd frontend
npm install
cp .env.example .env
# 配置API地址
npm run dev
```

### 默认账号
- 用户名: admin
- 密码: admin123
- 邮箱: <EMAIL>

---

## 📖 功能特性

### 🎨 DIY可视化编辑器
- **拖拽式编辑** - 直观的拖拽操作，所见即所得
- **丰富组件库** - 40+预设组件，覆盖所有常用场景
- **响应式设计** - 自动适配不同设备尺寸
- **样式编辑器** - 可视化CSS样式编辑
- **代码编辑器** - 高级用户可直接编辑HTML/CSS
- **实时预览** - 多设备实时预览功能
- **模板系统** - 丰富的页面模板库
- **导入导出** - 支持页面和组件的导入导出

### 📝 内容管理系统
- **统一内容管理** - 新闻、产品、案例统一管理
- **分类标签系统** - 灵活的分类和标签管理
- **富文本编辑器** - 强大的内容编辑功能
- **媒体库管理** - 图片、视频等媒体文件管理
- **SEO优化** - 完善的SEO设置和优化
- **批量操作** - 支持内容的批量操作
- **版本控制** - 内容版本历史管理
- **定时发布** - 支持内容定时发布

### 👥 用户权限管理
- **多角色系统** - 灵活的角色权限配置
- **细粒度权限** - 精确到功能点的权限控制
- **用户管理** - 完善的用户信息管理
- **登录安全** - 多重安全验证机制
- **操作日志** - 详细的用户操作记录
- **会话管理** - 安全的会话控制

### 🔍 SEO优化系统
- **页面SEO** - 每个页面独立的SEO设置
- **URL优化** - 友好的URL结构
- **元信息管理** - 标题、描述、关键词管理
- **Open Graph** - 社交媒体分享优化
- **网站地图** - 自动生成XML网站地图
- **结构化数据** - Schema.org结构化数据支持

### 📊 数据统计分析
- **访问统计** - 页面访问量统计
- **用户行为** - 用户行为轨迹分析
- **内容分析** - 内容表现数据分析
- **转化跟踪** - 表单提交等转化跟踪
- **实时监控** - 网站实时访问监控

---

## 🔧 开发指南

### 开发环境配置
详见 `docs/development/setup.md`

### API文档
详见 `docs/api/` 目录

### 组件开发指南
详见 `docs/development/components.md`

### 部署指南
详见 `docs/deployment/` 目录

---

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

---

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来帮助改进项目

---

## 📞 联系我们

- 官网: https://www.qiyediy.com
- 邮箱: <EMAIL>
- QQ群: 123456789

---

**© 2024 三只鱼网络科技 | 韩总 - QiyeDIY企业建站系统**
