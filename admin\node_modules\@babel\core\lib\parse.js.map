{"version": 3, "names": ["_gensync", "data", "require", "_index", "_index2", "_normalizeOpts", "_rewriteStackTrace", "parse<PERSON><PERSON><PERSON>", "gens<PERSON>", "parse", "code", "opts", "config", "loadConfig", "parser", "passes", "normalizeOptions", "exports", "callback", "undefined", "beginHiddenCallStack", "sync", "errback", "parseSync", "args", "parseAsync", "async"], "sources": ["../src/parse.ts"], "sourcesContent": ["import gensync, { type <PERSON><PERSON> } from \"gensync\";\n\nimport loadConfig, { type InputOptions } from \"./config/index.ts\";\nimport parser, { type ParseResult } from \"./parser/index.ts\";\nimport normalizeOptions from \"./transformation/normalize-opts.ts\";\nimport type { ValidatedOptions } from \"./config/validation/options.ts\";\n\nimport { beginHiddenCallStack } from \"./errors/rewrite-stack-trace.ts\";\n\ntype FileParseCallback = {\n  (err: Error, ast: null): void;\n  (err: null, ast: ParseResult | null): void;\n};\n\ntype Parse = {\n  (code: string, callback: FileParseCallback): void;\n  (\n    code: string,\n    opts: InputOptions | undefined | null,\n    callback: FileParseCallback,\n  ): void;\n  (code: string, opts?: InputOptions | null): ParseResult | null;\n};\n\nconst parseRunner = gensync(function* parse(\n  code: string,\n  opts: InputOptions | undefined | null,\n): Handler<ParseResult | null> {\n  const config = yield* loadConfig(opts);\n\n  if (config === null) {\n    return null;\n  }\n\n  return yield* parser(config.passes, normalizeOptions(config), code);\n});\n\nexport const parse: Parse = function parse(\n  code,\n  opts?,\n  callback?: FileParseCallback,\n) {\n  if (typeof opts === \"function\") {\n    callback = opts;\n    opts = undefined as ValidatedOptions;\n  }\n\n  if (callback === undefined) {\n    if (process.env.BABEL_8_BREAKING) {\n      throw new Error(\n        \"Starting from Babel 8.0.0, the 'parse' function expects a callback. If you need to call it synchronously, please use 'parseSync'.\",\n      );\n    } else {\n      // console.warn(\n      //   \"Starting from Babel 8.0.0, the 'parse' function will expect a callback. If you need to call it synchronously, please use 'parseSync'.\",\n      // );\n      return beginHiddenCallStack(parseRunner.sync)(code, opts);\n    }\n  }\n\n  beginHiddenCallStack(parseRunner.errback)(code, opts, callback);\n};\n\nexport function parseSync(...args: Parameters<typeof parseRunner.sync>) {\n  return beginHiddenCallStack(parseRunner.sync)(...args);\n}\nexport function parseAsync(...args: Parameters<typeof parseRunner.async>) {\n  return beginHiddenCallStack(parseRunner.async)(...args);\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,SAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,QAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAE,MAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,cAAA,GAAAH,OAAA;AAGA,IAAAI,kBAAA,GAAAJ,OAAA;AAiBA,MAAMK,WAAW,GAAGC,SAAMA,CAAC,CAAC,UAAUC,KAAKA,CACzCC,IAAY,EACZC,IAAqC,EACR;EAC7B,MAAMC,MAAM,GAAG,OAAO,IAAAC,cAAU,EAACF,IAAI,CAAC;EAEtC,IAAIC,MAAM,KAAK,IAAI,EAAE;IACnB,OAAO,IAAI;EACb;EAEA,OAAO,OAAO,IAAAE,eAAM,EAACF,MAAM,CAACG,MAAM,EAAE,IAAAC,sBAAgB,EAACJ,MAAM,CAAC,EAAEF,IAAI,CAAC;AACrE,CAAC,CAAC;AAEK,MAAMD,KAAY,GAAAQ,OAAA,CAAAR,KAAA,GAAG,SAASA,KAAKA,CACxCC,IAAI,EACJC,IAAK,EACLO,QAA4B,EAC5B;EACA,IAAI,OAAOP,IAAI,KAAK,UAAU,EAAE;IAC9BO,QAAQ,GAAGP,IAAI;IACfA,IAAI,GAAGQ,SAA6B;EACtC;EAEA,IAAID,QAAQ,KAAKC,SAAS,EAAE;IAKnB;MAIL,OAAO,IAAAC,uCAAoB,EAACb,WAAW,CAACc,IAAI,CAAC,CAACX,IAAI,EAAEC,IAAI,CAAC;IAC3D;EACF;EAEA,IAAAS,uCAAoB,EAACb,WAAW,CAACe,OAAO,CAAC,CAACZ,IAAI,EAAEC,IAAI,EAAEO,QAAQ,CAAC;AACjE,CAAC;AAEM,SAASK,SAASA,CAAC,GAAGC,IAAyC,EAAE;EACtE,OAAO,IAAAJ,uCAAoB,EAACb,WAAW,CAACc,IAAI,CAAC,CAAC,GAAGG,IAAI,CAAC;AACxD;AACO,SAASC,UAAUA,CAAC,GAAGD,IAA0C,EAAE;EACxE,OAAO,IAAAJ,uCAAoB,EAACb,WAAW,CAACmB,KAAK,CAAC,CAAC,GAAGF,IAAI,CAAC;AACzD;AAAC", "ignoreList": []}