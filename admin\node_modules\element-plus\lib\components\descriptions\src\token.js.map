{"version": 3, "file": "token.js", "sources": ["../../../../../../packages/components/descriptions/src/token.ts"], "sourcesContent": ["import type { InjectionKey } from 'vue'\nimport type { IDescriptionsInject } from './descriptions.type'\n\nexport const descriptionsKey: InjectionKey<IDescriptionsInject> =\n  Symbol('elDescriptions')\n"], "names": [], "mappings": ";;;;AAAY,MAAC,eAAe,GAAG,MAAM,CAAC,gBAAgB;;;;"}