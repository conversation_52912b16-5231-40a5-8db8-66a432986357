{"mcpServers": {"thinkphp6-server": {"command": "php", "args": ["tools/mcp_server.php"], "env": {"APP_ENV": "development", "DB_HOST": "localhost", "DB_NAME": "san_com", "PROJECT_ROOT": "D:/EServer/core/www/san.com"}}, "database-analyzer": {"command": "php", "args": ["tools/db_analyzer.php", "mcp"], "env": {"MCP_MODE": "true", "DB_CONFIG": "config/database.php"}}, "file-manager": {"command": "php", "args": ["tools/file_manager.php"], "env": {"UPLOAD_PATH": "public/uploads", "ALLOWED_TYPES": "jpg,png,gif,webp,pdf,doc,docx"}}, "layout-analyzer": {"command": "php", "args": ["tools/layout_analyzer.php", "mcp"], "env": {"VIEW_PATH": "app/view", "ASSETS_PATH": "public/assets"}}}}