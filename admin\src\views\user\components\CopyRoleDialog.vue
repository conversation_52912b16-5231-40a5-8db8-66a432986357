<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 复制角色对话框
-->

<template>
  <el-dialog
    v-model="visible"
    title="复制角色"
    width="500px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="源角色">
        <el-input :value="roleData?.name" readonly />
      </el-form-item>

      <el-form-item label="新角色名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入新角色名称"
          maxlength="50"
        />
      </el-form-item>

      <el-form-item label="角色编码" prop="code">
        <el-input
          v-model="form.code"
          placeholder="请输入角色编码"
          maxlength="50"
        />
      </el-form-item>

      <el-form-item label="角色描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入角色描述"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="复制选项">
        <el-checkbox-group v-model="copyOptions">
          <el-checkbox value="permissions">复制权限</el-checkbox>
          <el-checkbox value="users">复制用户关联</el-checkbox>
          <el-checkbox value="settings">复制其他设置</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :value="1">启用</el-radio>
          <el-radio :value="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确认复制
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { roleApi } from '@/api/role'
import type { Role } from '@/types/auth'

interface Props {
  modelValue: boolean
  roleData?: Role | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)
const copyOptions = ref(['permissions'])

// 表单数据
const form = reactive({
  name: '',
  code: '',
  description: '',
  status: 1
})

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入角色编码', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '编码必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
  ]
}

// 控制对话框显示
const visible = ref(false)

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val
    if (val && props.roleData) {
      initForm()
    }
  },
  { immediate: true }
)

watch(visible, (val) => {
  emit('update:modelValue', val)
})

/**
 * 初始化表单
 */
const initForm = () => {
  if (!props.roleData) return
  
  Object.assign(form, {
    name: `${props.roleData.name}_副本`,
    code: `${props.roleData.code}_copy`,
    description: props.roleData.description || '',
    status: 1
  })
  
  copyOptions.value = ['permissions']
}

/**
 * 重置表单
 */
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    name: '',
    code: '',
    description: '',
    status: 1
  })
  copyOptions.value = ['permissions']
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  if (!formRef.value || !props.roleData) return

  try {
    await formRef.value.validate()
    loading.value = true

    const copyData = {
      sourceRoleId: props.roleData.id,
      ...form,
      copyOptions: copyOptions.value
    }

    await roleApi.copyRole(copyData)
    
    ElMessage.success('角色复制成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('复制角色失败:', error)
    ElMessage.error('复制角色失败')
  } finally {
    loading.value = false
  }
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  visible.value = false
  resetForm()
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-checkbox-group) {
  .el-checkbox {
    margin-right: 20px;
    margin-bottom: 8px;
  }
}
</style>
