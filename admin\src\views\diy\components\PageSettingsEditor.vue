<template>
  <div class="page-settings-editor">
    <el-form :model="formData" label-width="80px" size="small">
      <!-- 基本信息 -->
      <div class="settings-section">
        <h4 class="section-title">基本信息</h4>
        <el-form-item label="页面标题">
          <el-input 
            v-model="formData.title" 
            placeholder="请输入页面标题"
            @input="handleUpdate"
          />
        </el-form-item>
        
        <el-form-item label="页面描述">
          <el-input 
            v-model="formData.description" 
            type="textarea"
            :rows="3"
            placeholder="请输入页面描述"
            @input="handleUpdate"
          />
        </el-form-item>
        
        <el-form-item label="页面关键词">
          <el-input 
            v-model="formData.keywords" 
            placeholder="请输入页面关键词，多个用逗号分隔"
            @input="handleUpdate"
          />
        </el-form-item>
      </div>

      <!-- SEO设置 -->
      <div class="settings-section">
        <h4 class="section-title">SEO设置</h4>
        <el-form-item label="SEO标题">
          <el-input 
            v-model="formData.seo_title" 
            placeholder="请输入SEO标题"
            @input="handleUpdate"
          />
        </el-form-item>
        
        <el-form-item label="SEO描述">
          <el-input 
            v-model="formData.seo_description" 
            type="textarea"
            :rows="3"
            placeholder="请输入SEO描述"
            @input="handleUpdate"
          />
        </el-form-item>
        
        <el-form-item label="SEO关键词">
          <el-input 
            v-model="formData.seo_keywords" 
            placeholder="请输入SEO关键词，多个用逗号分隔"
            @input="handleUpdate"
          />
        </el-form-item>
      </div>

      <!-- 页面配置 -->
      <div class="settings-section">
        <h4 class="section-title">页面配置</h4>
        <el-form-item label="背景颜色">
          <el-color-picker 
            v-model="formData.background_color" 
            @change="handleUpdate"
          />
        </el-form-item>
        
        <el-form-item label="背景图片">
          <el-input 
            v-model="formData.background_image" 
            placeholder="请输入背景图片URL"
            @input="handleUpdate"
          >
            <template #append>
              <el-button @click="handleSelectImage">选择</el-button>
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item label="页面宽度">
          <el-select 
            v-model="formData.page_width" 
            placeholder="请选择页面宽度"
            @change="handleUpdate"
          >
            <el-option label="自适应" value="auto" />
            <el-option label="1200px" value="1200px" />
            <el-option label="1400px" value="1400px" />
            <el-option label="100%" value="100%" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="页面边距">
          <el-input-number 
            v-model="formData.page_padding" 
            :min="0"
            :max="100"
            controls-position="right"
            @change="handleUpdate"
          />
          <span class="unit">px</span>
        </el-form-item>
      </div>

      <!-- 高级设置 -->
      <div class="settings-section">
        <h4 class="section-title">高级设置</h4>
        <el-form-item label="自定义CSS">
          <el-input 
            v-model="formData.custom_css" 
            type="textarea"
            :rows="6"
            placeholder="请输入自定义CSS代码"
            @input="handleUpdate"
          />
        </el-form-item>
        
        <el-form-item label="自定义JS">
          <el-input 
            v-model="formData.custom_js" 
            type="textarea"
            :rows="6"
            placeholder="请输入自定义JavaScript代码"
            @input="handleUpdate"
          />
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { DiyPage } from '@/api/diy'

interface Props {
  page: DiyPage
}

interface Emits {
  (e: 'update', data: Partial<DiyPage>): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单数据
const formData = reactive({
  title: '',
  description: '',
  keywords: '',
  seo_title: '',
  seo_description: '',
  seo_keywords: '',
  background_color: '#ffffff',
  background_image: '',
  page_width: 'auto',
  page_padding: 20,
  custom_css: '',
  custom_js: ''
})

// 监听页面数据变化
watch(() => props.page, (newPage) => {
  if (newPage) {
    Object.assign(formData, {
      title: newPage.title || '',
      description: newPage.description || '',
      keywords: newPage.keywords || '',
      seo_title: newPage.seo_title || '',
      seo_description: newPage.seo_description || '',
      seo_keywords: newPage.seo_keywords || '',
      background_color: newPage.config?.background_color || '#ffffff',
      background_image: newPage.config?.background_image || '',
      page_width: newPage.config?.page_width || 'auto',
      page_padding: newPage.config?.page_padding || 20,
      custom_css: newPage.custom_css || '',
      custom_js: newPage.custom_js || ''
    })
  }
}, { immediate: true, deep: true })

/**
 * 处理更新
 */
const handleUpdate = () => {
  emit('update', {
    title: formData.title,
    description: formData.description,
    keywords: formData.keywords,
    seo_title: formData.seo_title,
    seo_description: formData.seo_description,
    seo_keywords: formData.seo_keywords,
    custom_css: formData.custom_css,
    custom_js: formData.custom_js,
    config: {
      background_color: formData.background_color,
      background_image: formData.background_image,
      page_width: formData.page_width,
      page_padding: formData.page_padding
    }
  })
}

/**
 * 选择图片
 */
const handleSelectImage = () => {
  // TODO: 实现图片选择功能
  ElMessage.info('图片选择功能待实现')
}
</script>

<style scoped>
.page-settings-editor {
  padding: 16px;
}

.settings-section {
  margin-bottom: 24px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.unit {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-size: 12px;
  color: #606266;
}

:deep(.el-textarea__inner) {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
}
</style>
