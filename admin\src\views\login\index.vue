<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 登录页面
-->

<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="login-bg">
      <div class="bg-shape shape-1"></div>
      <div class="bg-shape shape-2"></div>
      <div class="bg-shape shape-3"></div>
    </div>

    <!-- 登录表单 -->
    <div class="login-form-container">
      <div class="login-form">
        <!-- Logo和标题 -->
        <div class="login-header">
          <div class="logo">
            <img src="/logo.png" alt="QiyeDIY" class="logo-img">
          </div>
          <h1 class="title">QiyeDIY 企业建站系统</h1>
          <p class="subtitle">专业的企业DIY建站解决方案</p>
        </div>

        <!-- 登录表单 -->
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form-content"
          size="large"
          @keyup.enter="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名或邮箱"
              prefix-icon="User"
              clearable
              :disabled="loading"
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              prefix-icon="Lock"
              show-password
              clearable
              :disabled="loading"
            />
          </el-form-item>

          <el-form-item>
            <div class="login-options">
              <el-checkbox v-model="loginForm.remember">记住我</el-checkbox>
              <el-link type="primary" @click="showForgotPassword">忘记密码？</el-link>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              class="login-btn"
              :loading="loading"
              @click="handleLogin"
            >
              {{ loading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>

          <el-form-item>
            <div class="register-link">
              还没有账号？
              <el-link type="primary" @click="showRegister">立即注册</el-link>
            </div>
          </el-form-item>
        </el-form>

        <!-- 其他登录方式 -->
        <div class="other-login">
          <el-divider>其他登录方式</el-divider>
          <div class="social-login">
            <el-button circle size="large" @click="handleSocialLogin('wechat')">
              微信
            </el-button>
            <el-button circle size="large" @click="handleSocialLogin('qq')">
              QQ
            </el-button>
            <el-button circle size="large" @click="handleSocialLogin('github')">
              GitHub
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 注册对话框 -->
    <RegisterDialog v-model="showRegisterDialog" @success="handleRegisterSuccess" />

    <!-- 忘记密码对话框 -->
    <ForgotPasswordDialog v-model="showForgotDialog" />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import type { FormInstance, FormRules } from 'element-plus'
import RegisterDialog from './components/RegisterDialog.vue'
import ForgotPasswordDialog from './components/ForgotPasswordDialog.vue'

const router = useRouter()
const userStore = useUserStore()

// 表单引用
const loginFormRef = ref<FormInstance>()

// 状态
const loading = ref(false)
const showRegisterDialog = ref(false)
const showForgotDialog = ref(false)

// 登录表单
const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

// 验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名或邮箱', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 32, message: '密码长度在 6 到 32 个字符', trigger: 'blur' }
  ]
}

/**
 * 处理登录
 */
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    loading.value = true

    await userStore.login({
      username: loginForm.username,
      password: loginForm.password
    })

    // 记住登录状态
    if (loginForm.remember) {
      localStorage.setItem('remember_username', loginForm.username)
    } else {
      localStorage.removeItem('remember_username')
    }

    // 跳转到首页
    router.push('/')

  } catch (error) {
    console.error('登录失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 显示注册对话框
 */
const showRegister = () => {
  showRegisterDialog.value = true
}

/**
 * 显示忘记密码对话框
 */
const showForgotPassword = () => {
  showForgotDialog.value = true
}

/**
 * 处理注册成功
 */
const handleRegisterSuccess = () => {
  showRegisterDialog.value = false
  ElMessage.success('注册成功，请登录')
}

/**
 * 处理第三方登录
 */
const handleSocialLogin = (type: string) => {
  ElMessage.info(`${type} 登录功能开发中...`)
}

// 初始化
onMounted(() => {
  // 恢复记住的用户名
  const rememberedUsername = localStorage.getItem('remember_username')
  if (rememberedUsername) {
    loginForm.username = rememberedUsername
    loginForm.remember = true
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.login-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;

  .bg-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;

    &.shape-1 {
      width: 200px;
      height: 200px;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    &.shape-2 {
      width: 150px;
      height: 150px;
      top: 60%;
      right: 15%;
      animation-delay: 2s;
    }

    &.shape-3 {
      width: 100px;
      height: 100px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.login-form-container {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 400px;
  padding: 20px;
}

.login-form {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
  text-align: center;
  margin-bottom: 40px;

  .logo {
    margin-bottom: 20px;

    .logo-img {
      width: 64px;
      height: 64px;
      border-radius: 12px;
    }
  }

  .title {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 8px 0;
  }

  .subtitle {
    font-size: 14px;
    color: #7f8c8d;
    margin: 0;
  }
}

.login-form-content {
  .el-form-item {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-input {
    height: 48px;

    :deep(.el-input__wrapper) {
      border-radius: 8px;
      box-shadow: 0 0 0 1px #e4e7ed;
      transition: all 0.3s;

      &:hover {
        box-shadow: 0 0 0 1px #c0c4cc;
      }

      &.is-focus {
        box-shadow: 0 0 0 1px var(--el-color-primary);
      }
    }
  }
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .el-checkbox {
    :deep(.el-checkbox__label) {
      font-size: 14px;
      color: #606266;
    }
  }

  .el-link {
    font-size: 14px;
  }
}

.login-btn {
  width: 100%;
  height: 48px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  transition: all 0.3s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
}

.register-link {
  text-align: center;
  font-size: 14px;
  color: #606266;

  .el-link {
    font-size: 14px;
    margin-left: 4px;
  }
}

.other-login {
  margin-top: 30px;

  .el-divider {
    margin: 20px 0;

    :deep(.el-divider__text) {
      font-size: 12px;
      color: #909399;
    }
  }

  .social-login {
    display: flex;
    justify-content: center;
    gap: 16px;

    .el-button {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      border: 1px solid #e4e7ed;
      background: #fff;
      color: #606266;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-form-container {
    max-width: 320px;
    padding: 16px;
  }

  .login-form {
    padding: 30px 24px;
  }

  .login-header {
    margin-bottom: 30px;

    .title {
      font-size: 20px;
    }

    .subtitle {
      font-size: 13px;
    }
  }
}
</style>
