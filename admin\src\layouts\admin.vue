<!--
  三只鱼网络科技 | 韩总 | 2024-12-20
  QiyeDIY企业建站系统 - 管理后台布局
-->

<template>
  <div class="admin-layout">
    <!-- 顶部导航 -->
    <div class="header">
      <div class="header-left">
        <h1 class="logo">QiyeDIY管理后台</h1>
      </div>
      <div class="header-right">
        <el-dropdown @command="handleCommand">
          <span class="user-info">
            <el-avatar :size="32" :src="userStore.avatar" />
            <span class="username">{{ userStore.displayName }}</span>
            <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                个人中心
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <el-icon><Setting /></el-icon>
                系统设置
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="main-container">
      <!-- 侧边栏 -->
      <div class="sidebar">
        <el-menu
          :default-active="activeMenu"
          class="sidebar-menu"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#ffffff"
          :collapse="false"
          router
        >
          <el-menu-item index="/dashboard">
            <el-icon><House /></el-icon>
            <span>仪表盘</span>
          </el-menu-item>
          <el-sub-menu index="site">
            <template #title>
              <el-icon><Monitor /></el-icon>
              <span>网站管理</span>
            </template>
            <el-menu-item index="/site/list">网站列表</el-menu-item>
            <el-menu-item index="/site/create">创建网站</el-menu-item>
          </el-sub-menu>
          <el-sub-menu index="template">
            <template #title>
              <el-icon><Document /></el-icon>
              <span>模板管理</span>
            </template>
            <el-menu-item index="/template/list">模板列表</el-menu-item>
            <el-menu-item index="/template/upload">上传模板</el-menu-item>
          </el-sub-menu>
          <el-sub-menu index="user">
            <template #title>
              <el-icon><User /></el-icon>
              <span>用户管理</span>
            </template>
            <el-menu-item index="/user/list">用户列表</el-menu-item>
            <el-menu-item index="/user/role">角色权限</el-menu-item>
          </el-sub-menu>
          <el-menu-item index="/diy/editor">
            <el-icon><Edit /></el-icon>
            <span>DIY编辑器</span>
          </el-menu-item>
          <el-menu-item index="/system">
            <el-icon><Setting /></el-icon>
            <span>系统设置</span>
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 内容区域 -->
      <div class="content">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import {
  House, Monitor, Document, User, Edit, Setting,
  ArrowDown, SwitchButton
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 当前激活的菜单
const activeMenu = computed(() => route.path)

/**
 * 处理下拉菜单命令
 */
const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/system')
      break
    case 'logout':
      await handleLogout()
      break
  }
}

/**
 * 处理退出登录
 */
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await userStore.logout()
    router.push('/login')
  } catch (error) {
    // 用户取消
  }
}
</script>

<style lang="scss" scoped>
.admin-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  z-index: 1000;

  .header-left {
    .logo {
      font-size: 20px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0;
    }
  }

  .header-right {
    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 6px;
      transition: background-color 0.3s;

      &:hover {
        background-color: #f5f7fa;
      }

      .username {
        font-size: 14px;
        color: #606266;
        font-weight: 500;
      }
    }
  }
}

.main-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar {
  width: 200px;
  background: #304156;
  overflow-y: auto;

  .sidebar-menu {
    border: none;
    height: 100%;

    .el-menu-item,
    .el-sub-menu__title {
      height: 50px;
      line-height: 50px;
      padding: 0 20px !important;

      &:hover {
        background-color: #263445 !important;
      }
    }

    .el-menu-item.is-active {
      background-color: #409eff !important;
    }

    .el-sub-menu .el-menu-item {
      padding-left: 40px !important;
      background-color: #1f2d3d !important;

      &:hover {
        background-color: #001528 !important;
      }

      &.is-active {
        background-color: #409eff !important;
      }
    }
  }
}

.content {
  flex: 1;
  padding: 20px;
  background: #f5f7fa;
  overflow: auto;
}

// 响应式设计
@media (max-width: 768px) {
  .sidebar {
    width: 60px;

    .sidebar-menu {
      .el-menu-item span,
      .el-sub-menu__title span {
        display: none;
      }
    }
  }
}
</style>
