/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - 管理后台入口文件
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

import App from './App.vue'
import router from './router'
import '@/styles/index.scss'

console.log('开始初始化应用...')

// 创建应用实例
const app = createApp(App)

console.log('应用实例创建成功')

// 创建Pinia状态管理
const pinia = createPinia()

// 安装插件
app.use(pinia)
app.use(router)
app.use(ElementPlus)

console.log('插件安装成功: Pinia, Router, ElementPlus')

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err, info)
}

// 挂载应用
console.log('准备挂载应用到#app')
app.mount('#app')
console.log('应用挂载成功')
