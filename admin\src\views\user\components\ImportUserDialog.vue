<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 导入用户对话框
-->

<template>
  <el-dialog
    v-model="visible"
    title="批量导入用户"
    width="600px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="import-container">
      <!-- 步骤指示器 -->
      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step title="下载模板" />
        <el-step title="上传文件" />
        <el-step title="导入结果" />
      </el-steps>

      <!-- 步骤1: 下载模板 -->
      <div v-if="currentStep === 0" class="step-content">
        <div class="template-info">
          <el-alert
            title="导入说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>1. 请先下载用户导入模板</p>
              <p>2. 按照模板格式填写用户信息</p>
              <p>3. 支持 Excel (.xlsx) 格式文件</p>
              <p>4. 单次最多导入 1000 条记录</p>
            </template>
          </el-alert>
        </div>
        
        <div class="template-download">
          <el-button type="primary" @click="downloadTemplate" :loading="downloading">
            <el-icon><Download /></el-icon>
            下载导入模板
          </el-button>
        </div>
      </div>

      <!-- 步骤2: 上传文件 -->
      <div v-if="currentStep === 1" class="step-content">
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :auto-upload="false"
          :limit="1"
          :on-change="handleFileChange"
          :on-exceed="handleExceed"
          accept=".xlsx,.xls"
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 xlsx/xls 文件，且不超过 10MB
            </div>
          </template>
        </el-upload>

        <div v-if="uploadFile" class="file-info">
          <el-tag type="success">{{ uploadFile.name }}</el-tag>
          <el-button type="text" @click="removeFile">移除</el-button>
        </div>
      </div>

      <!-- 步骤3: 导入结果 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="import-result">
          <el-result
            :icon="importResult.success ? 'success' : 'error'"
            :title="importResult.title"
            :sub-title="importResult.message"
          >
            <template #extra>
              <div v-if="importResult.details" class="result-details">
                <p>成功导入: {{ importResult.details.success }} 条</p>
                <p>失败: {{ importResult.details.failed }} 条</p>
                <p>跳过: {{ importResult.details.skipped }} 条</p>
              </div>
              
              <div v-if="importResult.errors?.length" class="error-list">
                <el-collapse>
                  <el-collapse-item title="查看错误详情" name="errors">
                    <div v-for="(error, index) in importResult.errors" :key="index" class="error-item">
                      <span>第 {{ error.row }} 行: {{ error.message }}</span>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </template>
          </el-result>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          {{ currentStep === 2 ? '关闭' : '取消' }}
        </el-button>
        
        <el-button
          v-if="currentStep === 0"
          type="primary"
          @click="nextStep"
          :disabled="!templateDownloaded"
        >
          下一步
        </el-button>
        
        <el-button
          v-if="currentStep === 1"
          @click="prevStep"
        >
          上一步
        </el-button>
        
        <el-button
          v-if="currentStep === 1"
          type="primary"
          @click="handleImport"
          :loading="importing"
          :disabled="!uploadFile"
        >
          开始导入
        </el-button>
        
        <el-button
          v-if="currentStep === 2 && importResult.success"
          type="primary"
          @click="handleClose"
        >
          完成
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, UploadFilled } from '@element-plus/icons-vue'
import type { UploadInstance, UploadFile, UploadFiles } from 'element-plus'
import { userApi } from '@/api/user'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const uploadRef = ref<UploadInstance>()
const currentStep = ref(0)
const downloading = ref(false)
const importing = ref(false)
const templateDownloaded = ref(false)
const uploadFile = ref<UploadFile | null>(null)

// 导入结果
const importResult = reactive({
  success: false,
  title: '',
  message: '',
  details: null as any,
  errors: [] as any[]
})

// 控制对话框显示
const visible = ref(false)

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val
    if (val) {
      resetDialog()
    }
  },
  { immediate: true }
)

watch(visible, (val) => {
  emit('update:modelValue', val)
})

/**
 * 重置对话框
 */
const resetDialog = () => {
  currentStep.value = 0
  templateDownloaded.value = false
  uploadFile.value = null
  Object.assign(importResult, {
    success: false,
    title: '',
    message: '',
    details: null,
    errors: []
  })
}

/**
 * 下载模板
 */
const downloadTemplate = async () => {
  try {
    downloading.value = true
    
    // 模拟下载模板
    const link = document.createElement('a')
    link.href = '/templates/user_import_template.xlsx'
    link.download = '用户导入模板.xlsx'
    link.click()
    
    templateDownloaded.value = true
    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败')
  } finally {
    downloading.value = false
  }
}

/**
 * 处理文件变化
 */
const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  uploadFile.value = file
}

/**
 * 处理文件超出限制
 */
const handleExceed = () => {
  ElMessage.warning('只能上传一个文件')
}

/**
 * 移除文件
 */
const removeFile = () => {
  uploadFile.value = null
  uploadRef.value?.clearFiles()
}

/**
 * 下一步
 */
const nextStep = () => {
  currentStep.value++
}

/**
 * 上一步
 */
const prevStep = () => {
  currentStep.value--
}

/**
 * 开始导入
 */
const handleImport = async () => {
  if (!uploadFile.value) return

  try {
    importing.value = true
    
    const formData = new FormData()
    formData.append('file', uploadFile.value.raw!)
    
    const result = await userApi.importUsers(formData)
    
    Object.assign(importResult, {
      success: true,
      title: '导入完成',
      message: '用户数据导入成功',
      details: result.data,
      errors: result.errors || []
    })
    
    emit('success')
  } catch (error: any) {
    Object.assign(importResult, {
      success: false,
      title: '导入失败',
      message: error.message || '导入过程中发生错误',
      details: null,
      errors: error.errors || []
    })
  } finally {
    importing.value = false
    currentStep.value = 2
  }
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.import-container {
  padding: 20px 0;
}

.step-content {
  margin-top: 30px;
  min-height: 200px;
}

.template-info {
  margin-bottom: 20px;
}

.template-download {
  text-align: center;
  margin-top: 30px;
}

.upload-demo {
  margin-bottom: 20px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
}

.import-result {
  text-align: center;
}

.result-details {
  margin: 20px 0;
  
  p {
    margin: 5px 0;
    font-size: 14px;
  }
}

.error-list {
  margin-top: 20px;
  text-align: left;
}

.error-item {
  padding: 5px 0;
  font-size: 12px;
  color: var(--el-color-danger);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-steps) {
  margin-bottom: 20px;
}
</style>
