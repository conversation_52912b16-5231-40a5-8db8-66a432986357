<?php
/**
 * 三只鱼网络科技 | 韩总 | 2025-01-15
 * ThinkPHP6安全审计工具 - 企业级应用安全检测
 */

class SecurityAuditor
{
    private $projectRoot;
    private $vulnerabilities = [];
    private $securityScore = 100;
    private $scanResults = [];
    
    public function __construct($projectRoot = '.')
    {
        $this->projectRoot = realpath($projectRoot);
    }
    
    /**
     * 执行安全扫描
     */
    public function scanSecurity()
    {
        echo "🛡️ ThinkPHP6安全审计工具\n";
        echo "============================================================\n\n";
        
        $this->scanSQLInjection();
        $this->scanXSSVulnerabilities();
        $this->scanCSRFProtection();
        $this->scanAuthenticationSecurity();
        $this->scanFileUploadSecurity();
        $this->scanConfigurationSecurity();
        $this->scanSessionSecurity();
        $this->scanInputValidation();
        
        $this->generateSecurityReport();
    }
    
    /**
     * SQL注入检测
     */
    private function scanSQLInjection()
    {
        echo "💉 SQL注入漏洞检测...\n";
        echo "----------------------------------------\n";
        
        $controllerPaths = [
            $this->projectRoot . '/app/controller',
            $this->projectRoot . '/app/admin/controller'
        ];
        
        $riskyPatterns = [
            '/\$_GET\[.*?\].*?->.*?query\(/',
            '/\$_POST\[.*?\].*?->.*?query\(/',
            '/\$_REQUEST\[.*?\].*?->.*?query\(/',
            '/Db::query\(.*?\$.*?\)/',
            '/->query\(.*?\$.*?\)/',
            '/WHERE.*?\$.*?[^->]/',
        ];
        
        $sqlInjectionRisks = 0;
        
        foreach ($controllerPaths as $path) {
            if (!is_dir($path)) continue;
            
            $files = $this->scanDirectory($path, '*.php');
            foreach ($files as $file) {
                $content = file_get_contents($path . '/' . $file);
                
                foreach ($riskyPatterns as $pattern) {
                    if (preg_match($pattern, $content)) {
                        $this->addVulnerability('high', 'SQL注入', 
                            "文件 {$file} 可能存在SQL注入风险");
                        $sqlInjectionRisks++;
                        $this->securityScore -= 15;
                    }
                }
                
                // 检查是否使用了参数化查询
                if (strpos($content, 'query(') !== false) {
                    if (strpos($content, 'bind(') === false && 
                        strpos($content, '->where(') === false) {
                        $this->addVulnerability('medium', 'SQL注入', 
                            "文件 {$file} 建议使用参数化查询");
                        $this->securityScore -= 8;
                    }
                }
            }
        }
        
        echo "✅ SQL注入风险检测: " . ($sqlInjectionRisks > 0 ? "{$sqlInjectionRisks}个风险点" : "未发现明显风险") . "\n\n";
    }
    
    /**
     * XSS漏洞检测
     */
    private function scanXSSVulnerabilities()
    {
        echo "🔍 XSS漏洞检测...\n";
        echo "----------------------------------------\n";
        
        $viewPath = $this->projectRoot . '/app/view';
        $xssRisks = 0;
        
        if (is_dir($viewPath)) {
            $htmlFiles = $this->scanDirectoryRecursive($viewPath, '*.html');
            
            foreach ($htmlFiles as $file) {
                $content = file_get_contents($file);
                
                // 检查未转义的变量输出
                $riskyPatterns = [
                    '/\{\$[^}]*\|raw\}/',
                    '/\{\$[^}]*\}(?!\|)/',
                    '/<script[^>]*>\s*\{\$/',
                    '/innerHTML\s*=\s*["\']?\{\$/',
                ];
                
                foreach ($riskyPatterns as $pattern) {
                    if (preg_match($pattern, $content)) {
                        $this->addVulnerability('medium', 'XSS漏洞', 
                            "模板文件 " . basename($file) . " 可能存在XSS风险");
                        $xssRisks++;
                        $this->securityScore -= 10;
                        break;
                    }
                }
            }
        }
        
        echo "✅ XSS风险检测: " . ($xssRisks > 0 ? "{$xssRisks}个风险点" : "未发现明显风险") . "\n\n";
    }
    
    /**
     * CSRF保护检测
     */
    private function scanCSRFProtection()
    {
        echo "🔒 CSRF保护检测...\n";
        echo "----------------------------------------\n";
        
        // 检查CSRF配置
        $appConfig = $this->projectRoot . '/config/app.php';
        $hasCSRFConfig = false;
        
        if (file_exists($appConfig)) {
            $content = file_get_contents($appConfig);
            if (strpos($content, 'csrf') !== false) {
                $hasCSRFConfig = true;
                echo "✅ CSRF配置存在\n";
            }
        }
        
        if (!$hasCSRFConfig) {
            $this->addVulnerability('high', 'CSRF保护', '缺少CSRF保护配置');
            $this->securityScore -= 12;
        }
        
        // 检查表单CSRF令牌
        $viewPath = $this->projectRoot . '/app/view';
        if (is_dir($viewPath)) {
            $htmlFiles = $this->scanDirectoryRecursive($viewPath, '*.html');
            $formsWithoutCSRF = 0;
            
            foreach ($htmlFiles as $file) {
                $content = file_get_contents($file);
                
                // 检查表单是否有CSRF令牌
                if (preg_match('/<form[^>]*method\s*=\s*["\']post["\'][^>]*>/i', $content)) {
                    if (strpos($content, 'csrf') === false && 
                        strpos($content, '__token__') === false) {
                        $formsWithoutCSRF++;
                    }
                }
            }
            
            if ($formsWithoutCSRF > 0) {
                $this->addVulnerability('medium', 'CSRF保护', 
                    "{$formsWithoutCSRF}个POST表单缺少CSRF令牌");
                $this->securityScore -= ($formsWithoutCSRF * 5);
            }
        }
        
        echo "\n";
    }
    
    /**
     * 身份认证安全检测
     */
    private function scanAuthenticationSecurity()
    {
        echo "🔐 身份认证安全检测...\n";
        echo "----------------------------------------\n";
        
        // 检查认证中间件
        $middlewarePath = $this->projectRoot . '/app/middleware';
        $hasAuthMiddleware = false;
        
        if (is_dir($middlewarePath)) {
            $middlewares = $this->scanDirectory($middlewarePath, '*.php');
            foreach ($middlewares as $middleware) {
                if (strpos(strtolower($middleware), 'auth') !== false) {
                    $hasAuthMiddleware = true;
                    
                    // 检查中间件安全性
                    $content = file_get_contents($middlewarePath . '/' . $middleware);
                    
                    // 检查会话安全
                    if (strpos($content, 'session_regenerate_id') === false) {
                        $this->addVulnerability('medium', '身份认证', 
                            '建议在认证成功后重新生成会话ID');
                        $this->securityScore -= 5;
                    }
                    
                    break;
                }
            }
        }
        
        if (!$hasAuthMiddleware) {
            $this->addVulnerability('high', '身份认证', '缺少身份认证中间件');
            $this->securityScore -= 15;
        } else {
            echo "✅ 身份认证中间件存在\n";
        }
        
        // 检查密码安全
        $this->checkPasswordSecurity();
        
        echo "\n";
    }
    
    /**
     * 文件上传安全检测
     */
    private function scanFileUploadSecurity()
    {
        echo "📁 文件上传安全检测...\n";
        echo "----------------------------------------\n";
        
        $uploadRisks = 0;
        $controllerPaths = [
            $this->projectRoot . '/app/controller',
            $this->projectRoot . '/app/admin/controller'
        ];
        
        foreach ($controllerPaths as $path) {
            if (!is_dir($path)) continue;
            
            $files = $this->scanDirectory($path, '*.php');
            foreach ($files as $file) {
                $content = file_get_contents($path . '/' . $file);
                
                // 检查文件上传处理
                if (strpos($content, 'move_uploaded_file') !== false || 
                    strpos($content, '$_FILES') !== false ||
                    strpos($content, 'upload') !== false) {
                    
                    // 检查文件类型验证
                    if (strpos($content, 'getExtension') === false && 
                        strpos($content, 'getMime') === false) {
                        $this->addVulnerability('high', '文件上传', 
                            "文件 {$file} 文件上传缺少类型验证");
                        $uploadRisks++;
                        $this->securityScore -= 12;
                    }
                    
                    // 检查文件大小限制
                    if (strpos($content, 'getSize') === false && 
                        strpos($content, 'size') === false) {
                        $this->addVulnerability('medium', '文件上传', 
                            "文件 {$file} 建议添加文件大小限制");
                        $this->securityScore -= 5;
                    }
                }
            }
        }
        
        echo "✅ 文件上传风险检测: " . ($uploadRisks > 0 ? "{$uploadRisks}个风险点" : "未发现明显风险") . "\n\n";
    }
    
    /**
     * 配置安全检测
     */
    private function scanConfigurationSecurity()
    {
        echo "⚙️ 配置安全检测...\n";
        echo "----------------------------------------\n";
        
        // 检查调试模式
        $appConfig = $this->projectRoot . '/config/app.php';
        if (file_exists($appConfig)) {
            $content = file_get_contents($appConfig);
            if (strpos($content, "'debug' => true") !== false || 
                strpos($content, '"debug" => true') !== false) {
                $this->addVulnerability('medium', '配置安全', 
                    '生产环境应关闭调试模式');
                $this->securityScore -= 8;
            }
        }
        
        // 检查敏感文件
        $sensitiveFiles = ['.env', 'config/database.php'];
        foreach ($sensitiveFiles as $file) {
            $filePath = $this->projectRoot . '/' . $file;
            if (file_exists($filePath)) {
                // 检查文件权限（在Unix系统上）
                if (function_exists('fileperms')) {
                    $perms = fileperms($filePath);
                    if (($perms & 0x0004) || ($perms & 0x0020)) {
                        $this->addVulnerability('high', '配置安全', 
                            "敏感文件 {$file} 权限过于宽松");
                        $this->securityScore -= 10;
                    }
                }
            }
        }
        
        echo "✅ 配置安全检查完成\n\n";
    }
    
    /**
     * 会话安全检测
     */
    private function scanSessionSecurity()
    {
        echo "🍪 会话安全检测...\n";
        echo "----------------------------------------\n";
        
        // 检查会话配置
        $sessionConfig = $this->projectRoot . '/config/session.php';
        if (file_exists($sessionConfig)) {
            $content = file_get_contents($sessionConfig);
            
            // 检查httponly设置
            if (strpos($content, 'httponly') === false) {
                $this->addVulnerability('medium', '会话安全', 
                    '建议启用session httponly');
                $this->securityScore -= 5;
            }
            
            // 检查secure设置
            if (strpos($content, 'secure') === false) {
                $this->addVulnerability('low', '会话安全', 
                    'HTTPS环境建议启用session secure');
                $this->securityScore -= 3;
            }
        }
        
        echo "✅ 会话安全检查完成\n\n";
    }
    
    /**
     * 输入验证检测
     */
    private function scanInputValidation()
    {
        echo "✅ 输入验证检测...\n";
        echo "----------------------------------------\n";
        
        $validationRisks = 0;
        $controllerPaths = [
            $this->projectRoot . '/app/controller',
            $this->projectRoot . '/app/admin/controller'
        ];
        
        foreach ($controllerPaths as $path) {
            if (!is_dir($path)) continue;
            
            $files = $this->scanDirectory($path, '*.php');
            foreach ($files as $file) {
                $content = file_get_contents($path . '/' . $file);
                
                // 检查是否处理用户输入
                if (strpos($content, '$_POST') !== false || 
                    strpos($content, '$_GET') !== false ||
                    strpos($content, 'input(') !== false) {
                    
                    // 检查是否有验证
                    if (strpos($content, 'validate') === false && 
                        strpos($content, 'filter') === false) {
                        $this->addVulnerability('high', '输入验证', 
                            "控制器 {$file} 处理用户输入但缺少验证");
                        $validationRisks++;
                        $this->securityScore -= 10;
                    }
                }
            }
        }
        
        echo "✅ 输入验证风险: " . ($validationRisks > 0 ? "{$validationRisks}个风险点" : "未发现明显风险") . "\n\n";
    }
    
    /**
     * 检查密码安全
     */
    private function checkPasswordSecurity()
    {
        $controllerPaths = [
            $this->projectRoot . '/app/controller',
            $this->projectRoot . '/app/admin/controller'
        ];
        
        foreach ($controllerPaths as $path) {
            if (!is_dir($path)) continue;
            
            $files = $this->scanDirectory($path, '*.php');
            foreach ($files as $file) {
                $content = file_get_contents($path . '/' . $file);
                
                // 检查密码处理
                if (strpos($content, 'password') !== false) {
                    // 检查是否使用了安全的密码哈希
                    if (strpos($content, 'password_hash') === false && 
                        strpos($content, 'md5') !== false) {
                        $this->addVulnerability('high', '密码安全', 
                            "文件 {$file} 使用不安全的密码哈希方法");
                        $this->securityScore -= 15;
                    }
                }
            }
        }
    }
    
    /**
     * 生成安全报告
     */
    private function generateSecurityReport()
    {
        echo "============================================================\n";
        echo "📊 安全审计报告\n";
        echo "============================================================\n\n";
        
        // 安全评分
        $grade = $this->getSecurityGrade($this->securityScore);
        echo "🛡️ 安全评分: {$this->securityScore}/100 ({$grade})\n\n";
        
        // 漏洞统计
        $criticalCount = count(array_filter($this->vulnerabilities, fn($v) => $v['level'] === 'critical'));
        $highCount = count(array_filter($this->vulnerabilities, fn($v) => $v['level'] === 'high'));
        $mediumCount = count(array_filter($this->vulnerabilities, fn($v) => $v['level'] === 'medium'));
        $lowCount = count(array_filter($this->vulnerabilities, fn($v) => $v['level'] === 'low'));
        
        echo "🚨 发现安全问题 (" . count($this->vulnerabilities) . "个):\n";
        if ($criticalCount > 0) echo "  🔴 严重: {$criticalCount}个\n";
        if ($highCount > 0) echo "  🟠 高危: {$highCount}个\n";
        if ($mediumCount > 0) echo "  🟡 中危: {$mediumCount}个\n";
        if ($lowCount > 0) echo "  🔵 低危: {$lowCount}个\n";
        
        if (count($this->vulnerabilities) > 0) {
            echo "\n📋 安全问题详情:\n";
            foreach ($this->vulnerabilities as $vuln) {
                $icon = $this->getVulnIcon($vuln['level']);
                echo "  {$icon} [{$vuln['category']}] {$vuln['message']}\n";
            }
        }
        
        echo "\n💡 安全建议:\n";
        $this->generateSecurityRecommendations();
        
        echo "\n✅ 安全审计完成！\n";
    }
    
    // 辅助方法
    private function scanDirectory($path, $pattern = '*')
    {
        if (!is_dir($path)) return [];
        $files = glob($path . '/' . $pattern);
        return array_map('basename', $files);
    }
    
    private function scanDirectoryRecursive($path, $pattern = '*')
    {
        if (!is_dir($path)) return [];
        $files = glob($path . '/**/' . $pattern, GLOB_BRACE);
        return $files;
    }
    
    private function addVulnerability($level, $category, $message)
    {
        $this->vulnerabilities[] = [
            'level' => $level,
            'category' => $category,
            'message' => $message
        ];
    }
    
    private function getSecurityGrade($score)
    {
        if ($score >= 90) return '优秀';
        if ($score >= 80) return '良好';
        if ($score >= 70) return '一般';
        if ($score >= 60) return '及格';
        return '危险';
    }
    
    private function getVulnIcon($level)
    {
        return match($level) {
            'critical' => '🔴',
            'high' => '🟠',
            'medium' => '🟡',
            'low' => '🔵',
            default => 'ℹ️'
        };
    }
    
    private function generateSecurityRecommendations()
    {
        if ($this->securityScore >= 90) {
            echo "  • 安全状况优秀，继续保持良好的安全实践\n";
        } elseif ($this->securityScore >= 80) {
            echo "  • 安全基础良好，建议修复发现的中高危漏洞\n";
        } else {
            echo "  • 建议立即修复所有高危和严重漏洞\n";
            echo "  • 加强输入验证和输出过滤\n";
            echo "  • 完善身份认证和授权机制\n";
            echo "  • 定期进行安全审计和渗透测试\n";
        }
    }
}

// 命令行执行
if (php_sapi_name() === 'cli') {
    $auditor = new SecurityAuditor();
    
    if (isset($argv[1]) && $argv[1] === 'scan') {
        $auditor->scanSecurity();
    } else {
        echo "用法: php security_auditor.php scan\n";
    }
}
