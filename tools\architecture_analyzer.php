<?php
/**
 * 三只鱼网络科技 | 韩总 | 2025-01-15
 * ThinkPHP6架构分析工具 - 企业级应用架构检测
 */

class ArchitectureAnalyzer
{
    private $projectRoot;
    private $issues = [];
    private $score = 100;
    
    public function __construct($projectRoot = '.')
    {
        $this->projectRoot = realpath($projectRoot);
    }
    
    /**
     * 执行完整架构分析
     */
    public function analyzeArchitecture()
    {
        echo "🏗️ ThinkPHP6架构分析工具\n";
        echo "============================================================\n\n";
        
        $this->analyzeMVCStructure();
        $this->analyzeRouteConfiguration();
        $this->analyzeMiddlewareSetup();
        $this->analyzeServiceLayer();
        $this->analyzeModelRelations();
        $this->analyzeDependencyInjection();
        $this->analyzeConfigurationManagement();
        
        $this->generateReport();
    }
    
    /**
     * 分析MVC架构结构
     */
    private function analyzeMVCStructure()
    {
        echo "📐 MVC架构结构分析...\n";
        echo "----------------------------------------\n";
        
        // 检查控制器结构
        $controllerPath = $this->projectRoot . '/app/controller';
        $adminControllerPath = $this->projectRoot . '/app/admin/controller';
        
        if (!is_dir($controllerPath)) {
            $this->addIssue('critical', 'MVC结构', '缺少主控制器目录');
            $this->score -= 20;
        } else {
            $controllers = $this->scanDirectory($controllerPath, '*.php');
            echo "✅ 前台控制器: " . count($controllers) . "个\n";
            
            // 检查控制器是否继承BaseController
            foreach ($controllers as $controller) {
                if (!$this->checkControllerInheritance($controller)) {
                    $this->addIssue('high', 'MVC结构', "控制器 {$controller} 未继承BaseController");
                    $this->score -= 5;
                }
            }
        }
        
        if (!is_dir($adminControllerPath)) {
            $this->addIssue('medium', 'MVC结构', '缺少后台控制器目录');
            $this->score -= 10;
        } else {
            $adminControllers = $this->scanDirectory($adminControllerPath, '*.php');
            echo "✅ 后台控制器: " . count($adminControllers) . "个\n";
        }
        
        // 检查模型结构
        $modelPath = $this->projectRoot . '/app/model';
        if (!is_dir($modelPath)) {
            $this->addIssue('critical', 'MVC结构', '缺少模型目录');
            $this->score -= 20;
        } else {
            $models = $this->scanDirectory($modelPath, '*.php');
            echo "✅ 数据模型: " . count($models) . "个\n";
            
            // 检查模型是否继承think\Model
            foreach ($models as $model) {
                if (!$this->checkModelInheritance($model)) {
                    $this->addIssue('high', 'MVC结构', "模型 {$model} 未继承think\\Model");
                    $this->score -= 5;
                }
            }
        }
        
        // 检查视图结构
        $viewPath = $this->projectRoot . '/app/view';
        if (!is_dir($viewPath)) {
            $this->addIssue('critical', 'MVC结构', '缺少视图目录');
            $this->score -= 20;
        } else {
            $viewDirs = $this->getDirectories($viewPath);
            echo "✅ 视图模块: " . count($viewDirs) . "个\n";
        }
        
        echo "\n";
    }
    
    /**
     * 分析路由配置
     */
    private function analyzeRouteConfiguration()
    {
        echo "🛣️ 路由配置分析...\n";
        echo "----------------------------------------\n";
        
        $routePath = $this->projectRoot . '/route';
        if (!is_dir($routePath)) {
            $this->addIssue('critical', '路由配置', '缺少路由配置目录');
            $this->score -= 15;
            return;
        }
        
        $routeFiles = $this->scanDirectory($routePath, '*.php');
        echo "✅ 路由文件: " . count($routeFiles) . "个\n";
        
        // 检查主要路由文件
        $requiredRoutes = ['app.php'];
        foreach ($requiredRoutes as $routeFile) {
            $filePath = $routePath . '/' . $routeFile;
            if (!file_exists($filePath)) {
                $this->addIssue('high', '路由配置', "缺少核心路由文件: {$routeFile}");
                $this->score -= 10;
            } else {
                // 分析路由文件内容
                $content = file_get_contents($filePath);
                if (strpos($content, 'Route::') === false) {
                    $this->addIssue('medium', '路由配置', "路由文件 {$routeFile} 可能为空或格式错误");
                    $this->score -= 5;
                }
            }
        }
        
        echo "\n";
    }
    
    /**
     * 分析中间件设置
     */
    private function analyzeMiddlewareSetup()
    {
        echo "🛡️ 中间件设置分析...\n";
        echo "----------------------------------------\n";
        
        // 检查中间件目录
        $middlewarePath = $this->projectRoot . '/app/middleware';
        if (!is_dir($middlewarePath)) {
            $this->addIssue('medium', '中间件设置', '缺少中间件目录');
            $this->score -= 10;
        } else {
            $middlewares = $this->scanDirectory($middlewarePath, '*.php');
            echo "✅ 自定义中间件: " . count($middlewares) . "个\n";
        }
        
        // 检查中间件配置文件
        $middlewareConfig = $this->projectRoot . '/config/middleware.php';
        if (!file_exists($middlewareConfig)) {
            $this->addIssue('high', '中间件设置', '缺少中间件配置文件');
            $this->score -= 10;
        } else {
            echo "✅ 中间件配置文件存在\n";
        }
        
        // 检查全局中间件
        $globalMiddleware = $this->projectRoot . '/app/middleware.php';
        if (!file_exists($globalMiddleware)) {
            $this->addIssue('medium', '中间件设置', '缺少全局中间件配置');
            $this->score -= 5;
        } else {
            echo "✅ 全局中间件配置存在\n";
        }
        
        echo "\n";
    }
    
    /**
     * 分析服务层
     */
    private function analyzeServiceLayer()
    {
        echo "⚙️ 服务层分析...\n";
        echo "----------------------------------------\n";
        
        $servicePath = $this->projectRoot . '/app/service';
        if (!is_dir($servicePath)) {
            $this->addIssue('low', '服务层', '建议创建服务层目录以封装业务逻辑');
            $this->score -= 5;
        } else {
            $services = $this->scanDirectory($servicePath, '*.php');
            echo "✅ 服务类: " . count($services) . "个\n";
            
            if (count($services) === 0) {
                $this->addIssue('low', '服务层', '服务层目录存在但无服务类');
                $this->score -= 3;
            }
        }
        
        echo "\n";
    }
    
    /**
     * 分析模型关联
     */
    private function analyzeModelRelations()
    {
        echo "🔗 模型关联分析...\n";
        echo "----------------------------------------\n";
        
        $modelPath = $this->projectRoot . '/app/model';
        if (!is_dir($modelPath)) {
            echo "⚠️ 模型目录不存在，跳过关联分析\n\n";
            return;
        }
        
        $models = $this->scanDirectory($modelPath, '*.php');
        $relationCount = 0;
        
        foreach ($models as $model) {
            $content = file_get_contents($modelPath . '/' . $model);
            // 检查常见的关联方法
            $relations = ['hasOne', 'hasMany', 'belongsTo', 'belongsToMany'];
            foreach ($relations as $relation) {
                if (strpos($content, $relation) !== false) {
                    $relationCount++;
                }
            }
        }
        
        echo "✅ 发现模型关联: {$relationCount}个\n";
        
        if ($relationCount === 0 && count($models) > 1) {
            $this->addIssue('medium', '模型关联', '多个模型但未发现关联关系，可能缺少数据关联');
            $this->score -= 8;
        }
        
        echo "\n";
    }
    
    /**
     * 分析依赖注入
     */
    private function analyzeDependencyInjection()
    {
        echo "💉 依赖注入分析...\n";
        echo "----------------------------------------\n";
        
        // 检查容器配置
        $containerConfig = $this->projectRoot . '/config/container.php';
        if (file_exists($containerConfig)) {
            echo "✅ 容器配置文件存在\n";
        } else {
            echo "ℹ️ 未发现容器配置文件（可选）\n";
        }
        
        // 检查服务提供者
        $providerFile = $this->projectRoot . '/app/provider.php';
        if (file_exists($providerFile)) {
            echo "✅ 服务提供者配置存在\n";
        } else {
            echo "ℹ️ 未发现服务提供者配置（可选）\n";
        }
        
        echo "\n";
    }
    
    /**
     * 分析配置管理
     */
    private function analyzeConfigurationManagement()
    {
        echo "⚙️ 配置管理分析...\n";
        echo "----------------------------------------\n";
        
        $configPath = $this->projectRoot . '/config';
        if (!is_dir($configPath)) {
            $this->addIssue('critical', '配置管理', '缺少配置目录');
            $this->score -= 20;
            return;
        }
        
        $configs = $this->scanDirectory($configPath, '*.php');
        echo "✅ 配置文件: " . count($configs) . "个\n";
        
        // 检查核心配置文件
        $requiredConfigs = ['app.php', 'database.php'];
        foreach ($requiredConfigs as $config) {
            if (!in_array($config, $configs)) {
                $this->addIssue('critical', '配置管理', "缺少核心配置文件: {$config}");
                $this->score -= 15;
            }
        }
        
        // 检查环境配置
        $envFile = $this->projectRoot . '/.env';
        if (file_exists($envFile)) {
            echo "✅ 环境配置文件存在\n";
        } else {
            $this->addIssue('medium', '配置管理', '建议使用.env文件管理环境配置');
            $this->score -= 5;
        }
        
        echo "\n";
    }
    
    /**
     * 生成分析报告
     */
    private function generateReport()
    {
        echo "============================================================\n";
        echo "📊 架构分析报告\n";
        echo "============================================================\n\n";
        
        // 评分
        $grade = $this->getGrade($this->score);
        echo "🎯 架构健康度评分: {$this->score}/100 ({$grade})\n\n";
        
        // 问题统计
        $criticalCount = count(array_filter($this->issues, fn($issue) => $issue['level'] === 'critical'));
        $highCount = count(array_filter($this->issues, fn($issue) => $issue['level'] === 'high'));
        $mediumCount = count(array_filter($this->issues, fn($issue) => $issue['level'] === 'medium'));
        $lowCount = count(array_filter($this->issues, fn($issue) => $issue['level'] === 'low'));
        
        echo "❗ 发现问题 (" . count($this->issues) . "个):\n";
        if ($criticalCount > 0) echo "  🔴 严重: {$criticalCount}个\n";
        if ($highCount > 0) echo "  🟠 高危: {$highCount}个\n";
        if ($mediumCount > 0) echo "  🟡 中等: {$mediumCount}个\n";
        if ($lowCount > 0) echo "  🔵 轻微: {$lowCount}个\n";
        
        if (count($this->issues) > 0) {
            echo "\n📋 问题详情:\n";
            foreach ($this->issues as $issue) {
                $icon = $this->getIssueIcon($issue['level']);
                echo "  {$icon} [{$issue['category']}] {$issue['message']}\n";
            }
        }
        
        echo "\n💡 改进建议:\n";
        $this->generateRecommendations();
        
        echo "\n✅ 架构分析完成！\n";
    }
    
    // 辅助方法
    private function scanDirectory($path, $pattern = '*')
    {
        if (!is_dir($path)) return [];
        $files = glob($path . '/' . $pattern);
        return array_map('basename', $files);
    }
    
    private function getDirectories($path)
    {
        if (!is_dir($path)) return [];
        return array_filter(scandir($path), function($item) use ($path) {
            return $item !== '.' && $item !== '..' && is_dir($path . '/' . $item);
        });
    }
    
    private function checkControllerInheritance($controllerFile)
    {
        $content = file_get_contents($this->projectRoot . '/app/controller/' . $controllerFile);
        return strpos($content, 'extends BaseController') !== false;
    }
    
    private function checkModelInheritance($modelFile)
    {
        $content = file_get_contents($this->projectRoot . '/app/model/' . $modelFile);
        return strpos($content, 'extends Model') !== false;
    }
    
    private function addIssue($level, $category, $message)
    {
        $this->issues[] = [
            'level' => $level,
            'category' => $category,
            'message' => $message
        ];
    }
    
    private function getGrade($score)
    {
        if ($score >= 90) return '优秀';
        if ($score >= 80) return '良好';
        if ($score >= 70) return '一般';
        if ($score >= 60) return '及格';
        return '需改进';
    }
    
    private function getIssueIcon($level)
    {
        return match($level) {
            'critical' => '🔴',
            'high' => '🟠',
            'medium' => '🟡',
            'low' => '🔵',
            default => 'ℹ️'
        };
    }
    
    private function generateRecommendations()
    {
        if ($this->score >= 90) {
            echo "  • 架构设计优秀，继续保持良好的开发规范\n";
        } elseif ($this->score >= 80) {
            echo "  • 架构基础良好，建议优化发现的中高级问题\n";
        } else {
            echo "  • 建议优先修复严重和高危问题\n";
            echo "  • 完善MVC分层结构和配置管理\n";
            echo "  • 加强代码规范和最佳实践应用\n";
        }
    }
}

// 命令行执行
if (php_sapi_name() === 'cli') {
    $analyzer = new ArchitectureAnalyzer();
    
    if (isset($argv[1]) && $argv[1] === 'full') {
        $analyzer->analyzeArchitecture();
    } else {
        echo "用法: php architecture_analyzer.php full\n";
    }
}
