<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 权限设置对话框
-->

<template>
  <el-dialog
    v-model="visible"
    title="权限设置"
    width="800px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="permission-container">
      <div class="permission-header">
        <div class="role-info">
          <span class="role-name">{{ roleData?.name }}</span>
          <span class="role-desc">{{ roleData?.description }}</span>
        </div>
        <div class="permission-actions">
          <el-button @click="expandAll">全部展开</el-button>
          <el-button @click="collapseAll">全部收起</el-button>
          <el-button type="primary" @click="checkAll">全选</el-button>
          <el-button @click="uncheckAll">取消全选</el-button>
        </div>
      </div>

      <el-divider />

      <div class="permission-tree">
        <el-tree
          ref="treeRef"
          :data="permissionTree"
          :props="treeProps"
          :default-checked-keys="checkedKeys"
          show-checkbox
          node-key="id"
          check-strictly
          @check="handleCheck"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <el-icon v-if="data.icon">
                <component :is="data.icon" />
              </el-icon>
              <span class="node-label">{{ data.name }}</span>
              <el-tag v-if="data.type" :type="getPermissionTypeTag(data.type)" size="small">
                {{ getPermissionTypeName(data.type) }}
              </el-tag>
            </div>
          </template>
        </el-tree>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="loading">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import type { ElTree } from 'element-plus'
import { roleApi } from '@/api/role'
import type { Role, Permission } from '@/types/auth'

interface Props {
  modelValue: boolean
  roleData?: Role | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const treeRef = ref<InstanceType<typeof ElTree>>()
const loading = ref(false)
const permissionTree = ref<Permission[]>([])
const checkedKeys = ref<number[]>([])

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'name',
  disabled: 'disabled'
}

// 控制对话框显示
const visible = ref(false)

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val
    if (val && props.roleData) {
      loadPermissions()
    }
  },
  { immediate: true }
)

watch(visible, (val) => {
  emit('update:modelValue', val)
})

/**
 * 加载权限数据
 */
const loadPermissions = async () => {
  try {
    loading.value = true
    
    // 加载权限树
    const { data: permissions } = await roleApi.getPermissions()
    permissionTree.value = permissions
    
    // 加载角色权限
    if (props.roleData?.id) {
      const { data: rolePermissions } = await roleApi.getRolePermissions(props.roleData.id)
      checkedKeys.value = rolePermissions.map((p: Permission) => p.id)
      
      // 等待树渲染完成后设置选中状态
      await nextTick()
      treeRef.value?.setCheckedKeys(checkedKeys.value)
    }
  } catch (error) {
    console.error('加载权限数据失败:', error)
    ElMessage.error('加载权限数据失败')
  } finally {
    loading.value = false
  }
}

/**
 * 获取权限类型标签
 */
const getPermissionTypeTag = (type: string) => {
  const typeMap: Record<string, string> = {
    menu: 'primary',
    button: 'success',
    api: 'warning'
  }
  return typeMap[type] || 'info'
}

/**
 * 获取权限类型名称
 */
const getPermissionTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    menu: '菜单',
    button: '按钮',
    api: '接口'
  }
  return typeMap[type] || '未知'
}

/**
 * 处理权限选择
 */
const handleCheck = (data: Permission, checked: any) => {
  // 可以在这里添加权限选择的业务逻辑
}

/**
 * 全部展开
 */
const expandAll = () => {
  const expandKeys = getAllNodeKeys(permissionTree.value)
  treeRef.value?.setExpandedKeys(expandKeys)
}

/**
 * 全部收起
 */
const collapseAll = () => {
  treeRef.value?.setExpandedKeys([])
}

/**
 * 全选
 */
const checkAll = () => {
  const allKeys = getAllNodeKeys(permissionTree.value)
  treeRef.value?.setCheckedKeys(allKeys)
}

/**
 * 取消全选
 */
const uncheckAll = () => {
  treeRef.value?.setCheckedKeys([])
}

/**
 * 获取所有节点的key
 */
const getAllNodeKeys = (nodes: Permission[]): number[] => {
  const keys: number[] = []
  
  const traverse = (nodeList: Permission[]) => {
    nodeList.forEach(node => {
      keys.push(node.id)
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }
  
  traverse(nodes)
  return keys
}

/**
 * 保存权限设置
 */
const handleSave = async () => {
  if (!props.roleData?.id) return

  try {
    loading.value = true
    
    const checkedNodes = treeRef.value?.getCheckedKeys() || []
    const halfCheckedNodes = treeRef.value?.getHalfCheckedKeys() || []
    const permissionIds = [...checkedNodes, ...halfCheckedNodes] as number[]
    
    await roleApi.updateRolePermissions(props.roleData.id, permissionIds)
    
    ElMessage.success('权限设置保存成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('保存权限设置失败:', error)
    ElMessage.error('保存权限设置失败')
  } finally {
    loading.value = false
  }
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.permission-container {
  max-height: 500px;
  overflow-y: auto;
}

.permission-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .role-info {
    .role-name {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .role-desc {
      margin-left: 12px;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
  }

  .permission-actions {
    display: flex;
    gap: 8px;
  }
}

.permission-tree {
  .tree-node {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;

    .node-label {
      flex: 1;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-tree) {
  .el-tree-node__content {
    height: 36px;
  }
}
</style>
