<?php
/**
 * 三只鱼网络科技 | 韩总 | 2025-01-12
 * ThinkPHP6 MCP服务器 - ThinkPHP6企业级应用
 */

require_once __DIR__ . '/../vendor/autoload.php';

use think\App;
use think\facade\Db;
use think\facade\Config;

class ThinkPHP6MCPServer {
    private $app;
    private $projectRoot;
    
    public function __construct() {
        $this->projectRoot = $_ENV['PROJECT_ROOT'] ?? dirname(__DIR__);
        $this->initializeApp();
    }
    
    private function initializeApp() {
        $this->app = new App($this->projectRoot);
        $this->app->initialize();
    }
    
    public function handleRequest($input) {
        $request = json_decode($input, true);
        
        switch ($request['method']) {
            case 'initialize':
                return $this->initialize();
            case 'tools/list':
                return $this->listTools();
            case 'tools/call':
                return $this->callTool($request['params']);
            case 'resources/list':
                return $this->listResources();
            case 'resources/read':
                return $this->readResource($request['params']);
            case 'prompts/list':
                return $this->listPrompts();
            case 'prompts/get':
                return $this->getPrompt($request['params']);
            default:
                return $this->error('Unknown method: ' . $request['method']);
        }
    }
    
    private function initialize() {
        return [
            'jsonrpc' => '2.0',
            'result' => [
                'protocolVersion' => '2024-11-05',
                'capabilities' => [
                    'tools' => [],
                    'resources' => [],
                    'prompts' => []
                ],
                'serverInfo' => [
                    'name' => 'ThinkPHP6 MCP Server',
                    'version' => '1.0.0'
                ]
            ]
        ];
    }
    
    private function listTools() {
        return [
            'jsonrpc' => '2.0',
            'result' => [
                'tools' => [
                    [
                        'name' => 'database_query',
                        'description' => '执行数据库查询',
                        'inputSchema' => [
                            'type' => 'object',
                            'properties' => [
                                'sql' => ['type' => 'string', 'description' => 'SQL查询语句'],
                                'params' => ['type' => 'array', 'description' => '查询参数']
                            ],
                            'required' => ['sql']
                        ]
                    ],
                    [
                        'name' => 'upload_image',
                        'description' => '上传图片文件',
                        'inputSchema' => [
                            'type' => 'object',
                            'properties' => [
                                'file_path' => ['type' => 'string', 'description' => '文件路径'],
                                'category' => ['type' => 'string', 'description' => '分类：news, products, cases, banners']
                            ],
                            'required' => ['file_path', 'category']
                        ]
                    ],
                    [
                        'name' => 'get_project_stats',
                        'description' => '获取项目统计信息',
                        'inputSchema' => [
                            'type' => 'object',
                            'properties' => [
                                'type' => ['type' => 'string', 'description' => '统计类型：all, database, files, performance']
                            ]
                        ]
                    ],
                    [
                        'name' => 'optimize_css',
                        'description' => '优化CSS文件',
                        'inputSchema' => [
                            'type' => 'object',
                            'properties' => [
                                'action' => ['type' => 'string', 'description' => 'full, duplicates, minify'],
                                'target' => ['type' => 'string', 'description' => '目标路径']
                            ],
                            'required' => ['action']
                        ]
                    ]
                ]
            ]
        ];
    }
    
    private function callTool($params) {
        $toolName = $params['name'];
        $arguments = $params['arguments'] ?? [];
        
        try {
            switch ($toolName) {
                case 'database_query':
                    return $this->executeDatabaseQuery($arguments);
                case 'upload_image':
                    return $this->handleImageUpload($arguments);
                case 'get_project_stats':
                    return $this->getProjectStats($arguments);
                case 'optimize_css':
                    return $this->optimizeCSS($arguments);
                default:
                    return $this->error('Unknown tool: ' . $toolName);
            }
        } catch (Exception $e) {
            return $this->error('Tool execution failed: ' . $e->getMessage());
        }
    }
    
    private function executeDatabaseQuery($args) {
        $sql = $args['sql'];
        $params = $args['params'] ?? [];
        
        try {
            if (stripos($sql, 'SELECT') === 0) {
                $result = Db::query($sql, $params);
            } else {
                $result = Db::execute($sql, $params);
            }
            
            return [
                'jsonrpc' => '2.0',
                'result' => [
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
                        ]
                    ]
                ]
            ];
        } catch (Exception $e) {
            return $this->error('Database query failed: ' . $e->getMessage());
        }
    }
    
    private function handleImageUpload($args) {
        $filePath = $args['file_path'];
        $category = $args['category'];
        
        // 模拟图片上传处理
        $uploadPath = "public/uploads/images/{$category}/" . date('Y/m/d') . '/';
        $fileName = basename($filePath);
        $newPath = $uploadPath . $fileName;
        
        return [
            'jsonrpc' => '2.0',
            'result' => [
                'content' => [
                    [
                        'type' => 'text',
                        'text' => "图片上传成功\n路径: {$newPath}\n分类: {$category}\n文件名: {$fileName}"
                    ]
                ]
            ]
        ];
    }
    
    private function getProjectStats($args) {
        $type = $args['type'] ?? 'all';
        
        $stats = [
            'database' => [
                'tables' => $this->getTableCount(),
                'records' => $this->getTotalRecords()
            ],
            'files' => [
                'total_files' => $this->countFiles(),
                'css_files' => $this->countCSSFiles(),
                'js_files' => $this->countJSFiles(),
                'images' => $this->countImages()
            ],
            'performance' => [
                'css_size' => $this->getCSSSize(),
                'js_size' => $this->getJSSize(),
                'image_size' => $this->getImageSize()
            ]
        ];
        
        $result = $type === 'all' ? $stats : ($stats[$type] ?? []);
        
        return [
            'jsonrpc' => '2.0',
            'result' => [
                'content' => [
                    [
                        'type' => 'text',
                        'text' => json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
                    ]
                ]
            ]
        ];
    }
    
    private function optimizeCSS($args) {
        $action = $args['action'];
        $target = $args['target'] ?? 'public/assets/css/';
        
        // 调用现有的CSS优化工具
        $command = "php tools/css_js_optimizer.php {$action}";
        if ($target !== 'public/assets/css/') {
            $command .= " {$target}";
        }
        
        $output = shell_exec($command);
        
        return [
            'jsonrpc' => '2.0',
            'result' => [
                'content' => [
                    [
                        'type' => 'text',
                        'text' => "CSS优化完成\n操作: {$action}\n目标: {$target}\n结果:\n{$output}"
                    ]
                ]
            ]
        ];
    }
    
    private function listResources() {
        return [
            'jsonrpc' => '2.0',
            'result' => [
                'resources' => [
                    [
                        'uri' => 'thinkphp6://config',
                        'name' => '项目配置',
                        'description' => 'ThinkPHP6项目配置信息',
                        'mimeType' => 'application/json'
                    ],
                    [
                        'uri' => 'thinkphp6://database/schema',
                        'name' => '数据库结构',
                        'description' => '数据库表结构信息',
                        'mimeType' => 'application/json'
                    ],
                    [
                        'uri' => 'thinkphp6://files/structure',
                        'name' => '文件结构',
                        'description' => '项目文件结构',
                        'mimeType' => 'text/plain'
                    ]
                ]
            ]
        ];
    }
    
    private function readResource($params) {
        $uri = $params['uri'];
        
        switch ($uri) {
            case 'thinkphp6://config':
                return $this->getProjectConfig();
            case 'thinkphp6://database/schema':
                return $this->getDatabaseSchema();
            case 'thinkphp6://files/structure':
                return $this->getFileStructure();
            default:
                return $this->error('Unknown resource: ' . $uri);
        }
    }
    
    private function listPrompts() {
        return [
            'jsonrpc' => '2.0',
            'result' => [
                'prompts' => [
                    [
                        'name' => 'analyze_project',
                        'description' => '分析ThinkPHP6项目结构和性能'
                    ],
                    [
                        'name' => 'optimize_database',
                        'description' => '数据库优化建议'
                    ],
                    [
                        'name' => 'css_refactor',
                        'description' => 'CSS重构建议'
                    ]
                ]
            ]
        ];
    }
    
    private function getPrompt($params) {
        $name = $params['name'];
        
        $prompts = [
            'analyze_project' => [
                'description' => '分析ThinkPHP6项目',
                'arguments' => [
                    [
                        'name' => 'focus',
                        'description' => '分析重点：performance, security, structure',
                        'required' => false
                    ]
                ]
            ]
        ];
        
        return [
            'jsonrpc' => '2.0',
            'result' => $prompts[$name] ?? $this->error('Unknown prompt: ' . $name)
        ];
    }
    
    // 辅助方法
    private function getTableCount() {
        try {
            $tables = Db::query("SHOW TABLES");
            return count($tables);
        } catch (Exception $e) {
            return 0;
        }
    }
    
    private function getTotalRecords() {
        // 实现获取总记录数的逻辑
        return 0;
    }
    
    private function countFiles() {
        return count(glob($this->projectRoot . '/**/*', GLOB_BRACE));
    }
    
    private function countCSSFiles() {
        return count(glob($this->projectRoot . '/public/assets/css/*.css'));
    }
    
    private function countJSFiles() {
        return count(glob($this->projectRoot . '/public/assets/js/*.js'));
    }
    
    private function countImages() {
        return count(glob($this->projectRoot . '/public/uploads/images/**/*.{jpg,png,gif,webp}', GLOB_BRACE));
    }
    
    private function getCSSSize() {
        $files = glob($this->projectRoot . '/public/assets/css/*.css');
        $totalSize = 0;
        foreach ($files as $file) {
            $totalSize += filesize($file);
        }
        return round($totalSize / 1024, 2) . ' KB';
    }
    
    private function getJSSize() {
        $files = glob($this->projectRoot . '/public/assets/js/*.js');
        $totalSize = 0;
        foreach ($files as $file) {
            $totalSize += filesize($file);
        }
        return round($totalSize / 1024, 2) . ' KB';
    }
    
    private function getImageSize() {
        $files = glob($this->projectRoot . '/public/uploads/images/**/*.{jpg,png,gif,webp}', GLOB_BRACE);
        $totalSize = 0;
        foreach ($files as $file) {
            $totalSize += filesize($file);
        }
        return round($totalSize / 1024 / 1024, 2) . ' MB';
    }
    
    private function getProjectConfig() {
        $config = [
            'app_name' => '三只鱼网络科技企业站',
            'version' => '1.0.0',
            'framework' => 'ThinkPHP6',
            'php_version' => PHP_VERSION,
            'environment' => $_ENV['APP_ENV'] ?? 'development'
        ];
        
        return [
            'jsonrpc' => '2.0',
            'result' => [
                'contents' => [
                    [
                        'uri' => 'thinkphp6://config',
                        'mimeType' => 'application/json',
                        'text' => json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
                    ]
                ]
            ]
        ];
    }
    
    private function getDatabaseSchema() {
        try {
            $tables = Db::query("SHOW TABLES");
            $schema = [];
            
            foreach ($tables as $table) {
                $tableName = array_values($table)[0];
                $columns = Db::query("DESCRIBE {$tableName}");
                $schema[$tableName] = $columns;
            }
            
            return [
                'jsonrpc' => '2.0',
                'result' => [
                    'contents' => [
                        [
                            'uri' => 'thinkphp6://database/schema',
                            'mimeType' => 'application/json',
                            'text' => json_encode($schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
                        ]
                    ]
                ]
            ];
        } catch (Exception $e) {
            return $this->error('Failed to get database schema: ' . $e->getMessage());
        }
    }
    
    private function getFileStructure() {
        $structure = $this->buildFileTree($this->projectRoot);
        
        return [
            'jsonrpc' => '2.0',
            'result' => [
                'contents' => [
                    [
                        'uri' => 'thinkphp6://files/structure',
                        'mimeType' => 'text/plain',
                        'text' => $structure
                    ]
                ]
            ]
        ];
    }
    
    private function buildFileTree($dir, $prefix = '') {
        $tree = '';
        $files = scandir($dir);
        
        foreach ($files as $file) {
            if ($file === '.' || $file === '..' || $file === '.git') continue;
            
            $path = $dir . '/' . $file;
            $tree .= $prefix . $file . "\n";
            
            if (is_dir($path) && !in_array($file, ['vendor', 'runtime', 'node_modules'])) {
                $tree .= $this->buildFileTree($path, $prefix . '  ');
            }
        }
        
        return $tree;
    }
    
    private function error($message) {
        return [
            'jsonrpc' => '2.0',
            'error' => [
                'code' => -1,
                'message' => $message
            ]
        ];
    }
}

// 启动MCP服务器
if (php_sapi_name() === 'cli') {
    $server = new ThinkPHP6MCPServer();
    
    while (($line = fgets(STDIN)) !== false) {
        $response = $server->handleRequest(trim($line));
        echo json_encode($response) . "\n";
        flush();
    }
} 