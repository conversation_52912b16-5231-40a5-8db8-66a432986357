<!--
  三只鱼网络科技 | 韩总 | 2024-12-20
  QiyeDIY企业建站系统 - 模板管理页面
-->

<template>
  <div class="templates-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">模板管理</h2>
        <p class="page-subtitle">管理DIY页面模板，创建和编辑模板</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="createTemplate">
          <el-icon><Plus /></el-icon>
          创建模板
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="模板名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入模板名称"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="模板类型">
          <el-select v-model="searchForm.type" placeholder="请选择类型" clearable>
            <el-option label="全部" value="" />
            <el-option label="首页模板" value="home" />
            <el-option label="产品页" value="product" />
            <el-option label="关于我们" value="about" />
            <el-option label="联系我们" value="contact" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="全部" value="" />
            <el-option label="已发布" value="published" />
            <el-option label="草稿" value="draft" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 模板列表 -->
    <el-card class="templates-card">
      <div class="templates-grid">
        <div
          v-for="template in templates"
          :key="template.id"
          class="template-item"
        >
          <div class="template-preview">
            <img :src="template.preview" :alt="template.name" />
            <div class="template-overlay">
              <el-button size="small" @click="previewTemplate(template)">
                <el-icon><View /></el-icon>
                预览
              </el-button>
              <el-button size="small" type="primary" @click="editTemplate(template)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
            </div>
          </div>
          <div class="template-info">
            <h4 class="template-name">{{ template.name }}</h4>
            <p class="template-desc">{{ template.description }}</p>
            <div class="template-meta">
              <el-tag :type="getStatusType(template.status)" size="small">
                {{ getStatusText(template.status) }}
              </el-tag>
              <span class="template-time">{{ template.updated_at }}</span>
            </div>
            <div class="template-actions">
              <el-button size="small" text @click="copyTemplate(template)">
                <el-icon><CopyDocument /></el-icon>
                复制
              </el-button>
              <el-button size="small" text type="danger" @click="deleteTemplate(template)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[12, 24, 48, 96]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Refresh, View, Edit, CopyDocument, Delete
} from '@element-plus/icons-vue'

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  name: '',
  type: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 12,
  total: 0
})

// 模板列表
const templates = ref([
  {
    id: 1,
    name: '企业首页模板',
    description: '适用于企业官网首页的专业模板',
    type: 'home',
    status: 'published',
    preview: '/static/images/template-preview-1.jpg',
    updated_at: '2024-12-20 10:30'
  },
  {
    id: 2,
    name: '产品展示模板',
    description: '产品展示页面模板，支持多种布局',
    type: 'product',
    status: 'published',
    preview: '/static/images/template-preview-2.jpg',
    updated_at: '2024-12-19 15:20'
  },
  {
    id: 3,
    name: '关于我们模板',
    description: '企业介绍页面模板',
    type: 'about',
    status: 'draft',
    preview: '/static/images/template-preview-3.jpg',
    updated_at: '2024-12-18 09:15'
  }
])

/**
 * 获取状态类型
 */
const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    published: 'success',
    draft: 'warning'
  }
  return types[status] || 'info'
}

/**
 * 获取状态文本
 */
const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    published: '已发布',
    draft: '草稿'
  }
  return texts[status] || '未知'
}

/**
 * 搜索模板
 */
const handleSearch = () => {
  pagination.page = 1
  loadTemplates()
}

/**
 * 重置搜索
 */
const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    type: '',
    status: ''
  })
  handleSearch()
}

/**
 * 创建模板
 */
const createTemplate = () => {
  router.push('/diy/editor?mode=template')
}

/**
 * 预览模板
 */
const previewTemplate = (template: any) => {
  ElMessage.info(`预览模板: ${template.name}`)
}

/**
 * 编辑模板
 */
const editTemplate = (template: any) => {
  router.push(`/diy/editor?template=${template.id}`)
}

/**
 * 复制模板
 */
const copyTemplate = (template: any) => {
  ElMessage.success(`模板 "${template.name}" 复制成功`)
}

/**
 * 删除模板
 */
const deleteTemplate = async (template: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板 "${template.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('删除成功')
    loadTemplates()
  } catch (error) {
    // 用户取消
  }
}

/**
 * 分页大小改变
 */
const handleSizeChange = (size: number) => {
  pagination.size = size
  loadTemplates()
}

/**
 * 页码改变
 */
const handlePageChange = (page: number) => {
  pagination.page = page
  loadTemplates()
}

/**
 * 加载模板列表
 */
const loadTemplates = async () => {
  try {
    // 这里应该调用API获取模板列表
    // const response = await templateApi.getList(searchForm, pagination)
    // templates.value = response.data
    // pagination.total = response.total
    
    // 临时使用模拟数据
    pagination.total = 3
  } catch (error) {
    console.error('加载模板列表失败:', error)
    ElMessage.error('加载模板列表失败')
  }
}

onMounted(() => {
  loadTemplates()
})
</script>

<style lang="scss" scoped>
.templates-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 4px 0;
  }

  .page-subtitle {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin: 0;
  }
}

.search-card {
  margin-bottom: 20px;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.template-item {
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

.template-preview {
  position: relative;
  height: 200px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .template-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover .template-overlay {
    opacity: 1;
  }
}

.template-info {
  padding: 16px;

  .template-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 8px 0;
  }

  .template-desc {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin: 0 0 12px 0;
    line-height: 1.4;
  }

  .template-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .template-time {
      font-size: 12px;
      color: var(--el-text-color-placeholder);
    }
  }

  .template-actions {
    display: flex;
    gap: 8px;
  }
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

// 响应式设计
@media (max-width: 768px) {
  .templates-grid {
    grid-template-columns: 1fr;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}
</style>
