<?php
/**
 * 三只鱼网络科技 | 韩总 | 2025-06-08
 * 数据库分析工具 - 快速获取数据库结构信息
 */

require_once dirname(__DIR__) . '/vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

class DbAnalyzer
{
    private $app;

    public function __construct()
    {
        $this->app = new \think\App();
        $this->app->initialize();
    }

    /**
     * 获取数据库完整信息
     */
    public function getFullInfo()
    {
        try {
            echo "=== 数据库连接信息 ===\n";
            $config = Config::get('database.connections.mysql');
            echo "数据库: {$config['database']}\n";
            echo "主机: {$config['hostname']}:{$config['hostport']}\n\n";

            echo "=== 数据表列表 ===\n";
            $tables = Db::query('SHOW TABLES');
            $tableNames = [];
            
            foreach ($tables as $table) {
                $tableName = array_values($table)[0];
                $tableNames[] = $tableName;
                echo "- {$tableName}\n";
            }
            echo "\n";

            echo "=== 表结构详情 ===\n";
            foreach ($tableNames as $tableName) {
                echo "## 表: {$tableName}\n";
                
                // 获取表结构
                $columns = Db::query("DESCRIBE {$tableName}");
                echo "字段信息:\n";
                foreach ($columns as $column) {
                    echo "  - {$column['Field']}: {$column['Type']} ";
                    echo ($column['Null'] === 'NO' ? 'NOT NULL' : 'NULL') . " ";
                    echo ($column['Key'] === 'PRI' ? 'PRIMARY KEY' : '') . " ";
                    echo ($column['Extra'] ? $column['Extra'] : '') . "\n";
                }
                
                // 获取索引信息
                $indexes = Db::query("SHOW INDEX FROM {$tableName}");
                if ($indexes) {
                    echo "索引信息:\n";
                    $indexGroups = [];
                    foreach ($indexes as $index) {
                        $indexGroups[$index['Key_name']][] = $index['Column_name'];
                    }
                    foreach ($indexGroups as $indexName => $columns) {
                        echo "  - {$indexName}: " . implode(', ', $columns) . "\n";
                    }
                }
                
                // 获取记录数
                $count = Db::table($tableName)->count();
                echo "记录数: {$count}\n\n";
            }

            echo "=== 外键关系 ===\n";
            $foreignKeys = Db::query("
                SELECT 
                    TABLE_NAME,
                    COLUMN_NAME,
                    CONSTRAINT_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM 
                    INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE 
                    REFERENCED_TABLE_SCHEMA = '{$config['database']}'
                    AND REFERENCED_TABLE_NAME IS NOT NULL
            ");
            
            if ($foreignKeys) {
                foreach ($foreignKeys as $fk) {
                    echo "- {$fk['TABLE_NAME']}.{$fk['COLUMN_NAME']} -> {$fk['REFERENCED_TABLE_NAME']}.{$fk['REFERENCED_COLUMN_NAME']}\n";
                }
            } else {
                echo "无外键关系\n";
            }

        } catch (\Exception $e) {
            echo "错误: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 获取指定表的详细信息
     */
    public function getTableInfo($tableName)
    {
        try {
            echo "=== 表信息: {$tableName} ===\n";
            
            // 检查表是否存在
            $tables = Db::query('SHOW TABLES');
            $tableExists = false;
            foreach ($tables as $table) {
                if (array_values($table)[0] === $tableName) {
                    $tableExists = true;
                    break;
                }
            }
            
            if (!$tableExists) {
                echo "❌ 表 {$tableName} 不存在\n";
                return false;
            }

            // 获取表结构
            $columns = Db::query("DESCRIBE {$tableName}");
            echo "字段信息:\n";
            foreach ($columns as $column) {
                echo "  - {$column['Field']}: {$column['Type']} ";
                echo ($column['Null'] === 'NO' ? 'NOT NULL' : 'NULL') . " ";
                echo ($column['Key'] === 'PRI' ? 'PRIMARY KEY' : '') . " ";
                echo ($column['Extra'] ? $column['Extra'] : '') . "\n";
            }
            
            // 获取记录数和示例数据
            $count = Db::table($tableName)->count();
            echo "\n记录数: {$count}\n";
            
            if ($count > 0) {
                echo "\n示例数据 (前3条):\n";
                $samples = Db::table($tableName)->limit(3)->select();
                foreach ($samples as $sample) {
                    echo "  ID: {$sample['id']} - ";
                    // 显示主要字段
                    $mainFields = ['name', 'title', 'username', 'setting_key'];
                    foreach ($mainFields as $field) {
                        if (isset($sample[$field])) {
                            echo "{$field}: {$sample[$field]} ";
                            break;
                        }
                    }
                    echo "\n";
                }
            }

        } catch (\Exception $e) {
            echo "错误: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 获取数据库统计信息
     */
    public function getStats()
    {
        try {
            echo "=== 数据库统计信息 ===\n";
            
            $tables = Db::query('SHOW TABLES');
            $totalTables = count($tables);
            $totalRecords = 0;
            $tableStats = [];
            
            foreach ($tables as $table) {
                $tableName = array_values($table)[0];
                $count = Db::table($tableName)->count();
                $totalRecords += $count;
                $tableStats[] = [
                    'name' => $tableName,
                    'records' => $count
                ];
            }
            
            echo "总表数: {$totalTables}\n";
            echo "总记录数: {$totalRecords}\n\n";
            
            echo "各表记录数:\n";
            // 按记录数排序
            usort($tableStats, function($a, $b) {
                return $b['records'] - $a['records'];
            });
            
            foreach ($tableStats as $stat) {
                echo "  - {$stat['name']}: {$stat['records']} 条\n";
            }

        } catch (\Exception $e) {
            echo "错误: " . $e->getMessage() . "\n";
        }
    }
}

// 命令行使用
if (php_sapi_name() === 'cli') {
    $analyzer = new DbAnalyzer();
    
    $command = $argv[1] ?? 'help';
    
    switch ($command) {
        case 'info':
            $analyzer->getFullInfo();
            break;
            
        case 'table':
            $tableName = $argv[2] ?? '';
            if (empty($tableName)) {
                echo "请指定表名: php tools/db_analyzer.php table 表名\n";
            } else {
                $analyzer->getTableInfo($tableName);
            }
            break;
            
        case 'stats':
            $analyzer->getStats();
            break;
            
        default:
            echo "数据库分析工具使用说明:\n";
            echo "php tools/db_analyzer.php info - 获取完整数据库信息\n";
            echo "php tools/db_analyzer.php table [表名] - 获取指定表信息\n";
            echo "php tools/db_analyzer.php stats - 获取数据库统计信息\n";
            echo "\n示例:\n";
            echo "php tools/db_analyzer.php info\n";
            echo "php tools/db_analyzer.php table page_templates\n";
            echo "php tools/db_analyzer.php stats\n";
    }
}
?>
