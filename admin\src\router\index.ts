/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 路由配置
 */

import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/layouts/admin.vue'),
    children: [
      {
        path: '',
        name: 'DashboardIndex',
        component: () => import('@/views/dashboard/index.vue'),
        meta: {
          title: '仪表盘',
          icon: 'Dashboard'
        }
      }
    ]
  },
  {
    path: '/diy',
    name: 'DIY',
    component: () => import('@/layouts/default.vue'),
    children: [
      {
        path: 'editor',
        name: 'DIYEditor',
        component: () => import('@/views/diy/editor.vue'),
        meta: {
          title: 'DIY编辑器',
          icon: 'Edit'
        }
      },
      {
        path: 'templates',
        name: 'DIYTemplates',
        component: () => import('@/views/diy/templates.vue'),
        meta: {
          title: '模板管理',
          icon: 'Document'
        }
      }
    ]
  },
  {
    path: '/system',
    name: 'System',
    component: () => import('@/layouts/default.vue'),
    children: [
      {
        path: 'settings',
        name: 'SystemSettings',
        component: () => import('@/views/system/settings.vue'),
        meta: {
          title: '系统设置',
          icon: 'Setting'
        }
      }
    ]
  },
  {
    path: '/user',
    name: 'User',
    component: () => import('@/layouts/default.vue'),
    children: [
      {
        path: 'list',
        name: 'UserList',
        component: () => import('@/views/user/list.vue'),
        meta: {
          title: '用户管理',
          icon: 'User'
        }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - QiyeDIY企业建站系统`
  }

  // 检查是否需要登录
  if (to.meta?.requiresAuth !== false) {
    const { useUserStore } = await import('@/store/modules/user')
    const userStore = useUserStore()

    // 如果没有token，跳转到登录页
    if (!userStore.token) {
      if (to.path !== '/login') {
        next('/login')
        return
      }
    } else {
      // 如果有token，验证token有效性
      try {
        // 如果用户信息不存在，获取用户信息
        if (!userStore.user) {
          await userStore.getUserInfo()
        }

        // 如果访问登录页但已登录，重定向到首页
        if (to.path === '/login') {
          next('/dashboard')
          return
        }
      } catch (error) {
        // token无效，清除登录状态并跳转到登录页
        console.error('Token验证失败:', error)
        await userStore.logout()
        if (to.path !== '/login') {
          next('/login')
          return
        }
      }
    }
  }

  next()
})

export default router
