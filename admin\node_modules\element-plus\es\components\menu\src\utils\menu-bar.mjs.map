{"version": 3, "file": "menu-bar.mjs", "sources": ["../../../../../../../packages/components/menu/src/utils/menu-bar.ts"], "sourcesContent": ["import MenuItem from './menu-item'\n\nimport type { RendererNode } from 'vue'\n\nclass Menu {\n  constructor(public domNode: RendererNode, namespace: string) {\n    this.init(namespace)\n  }\n  init(namespace: string): void {\n    const menuChildren = this.domNode.childNodes\n    Array.from<Node>(menuChildren).forEach((child) => {\n      if (child.nodeType === 1) {\n        new MenuItem(child as HTMLElement, namespace)\n      }\n    })\n  }\n}\n\nexport default Menu\n"], "names": [], "mappings": ";;AACA,MAAM,IAAI,CAAC;AACX,EAAE,WAAW,CAAC,OAAO,EAAE,SAAS,EAAE;AAClC,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACzB,GAAG;AACH,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;AACjD,IAAI,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AAChD,MAAM,IAAI,KAAK,CAAC,QAAQ,KAAK,CAAC,EAAE;AAChC,QAAQ,IAAI,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AACvC,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH;;;;"}