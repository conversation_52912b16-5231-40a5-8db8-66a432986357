{"version": 3, "file": "empty.mjs", "sources": ["../../../../../../packages/components/empty/src/empty.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\n\nexport const emptyProps = buildProps({\n  /**\n   * @description image URL of empty\n   */\n  image: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description image size (width) of empty\n   */\n  imageSize: Number,\n  /**\n   * @description description of empty\n   */\n  description: {\n    type: String,\n    default: '',\n  },\n} as const)\n\nexport type EmptyProps = ExtractPropTypes<typeof emptyProps>\n"], "names": [], "mappings": ";;AACY,MAAC,UAAU,GAAG,UAAU,CAAC;AACrC,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,CAAC;;;;"}