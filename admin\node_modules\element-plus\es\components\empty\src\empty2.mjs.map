{"version": 3, "file": "empty2.mjs", "sources": ["../../../../../../packages/components/empty/src/empty.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.b()\">\n    <div :class=\"ns.e('image')\" :style=\"imageStyle\">\n      <img v-if=\"image\" :src=\"image\" ondragstart=\"return false\" />\n      <slot v-else name=\"image\">\n        <img-empty />\n      </slot>\n    </div>\n    <div :class=\"ns.e('description')\">\n      <slot v-if=\"$slots.description\" name=\"description\" />\n      <p v-else>{{ emptyDescription }}</p>\n    </div>\n    <div v-if=\"$slots.default\" :class=\"ns.e('bottom')\">\n      <slot />\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { addUnit } from '@element-plus/utils'\nimport ImgEmpty from './img-empty.vue'\nimport { emptyProps } from './empty'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElEmpty',\n})\n\nconst props = defineProps(emptyProps)\n\nconst { t } = useLocale()\nconst ns = useNamespace('empty')\nconst emptyDescription = computed(\n  () => props.description || t('el.table.emptyText')\n)\nconst imageStyle = computed<CSSProperties>(() => ({\n  width: addUnit(props.imageSize),\n}))\n</script>\n"], "names": ["_openBlock", "_createElementBlock"], "mappings": ";;;;;;;;mCA2Bc,CAAA;AAAA,EACZ,IAAM,EAAA,SAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAIA,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAK,aAAa,OAAO,CAAA,CAAA;AAC/B,IAAA,MAAM,gBAAmB,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,WAAA,IAAA,CAAA,CAAA,oBAAA,CAAA,CAAA,CAAA;AAAA,IAAA,MACjB,UAAqB,GAAA,QAAA,CAAA,OAAsB;AAAA,MACnD,KAAA,EAAA,OAAA,CAAA,KAAA,CAAA,SAAA,CAAA;AACA,KAAM,CAAA,CAAA,CAAA;AAA4C,IAChD,OAAA,CAAA,IAAe,EAAA,MAAA,KAAe;AAAA,MAC9B,OAAAA,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}