/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * Stagewise工具栏类型声明文件
 */

declare module '@stagewise/toolbar-vue' {
  import { Component } from 'vue'

  export interface ToolbarConfig {
    plugins?: Array<{
      name: string
      [key: string]: any
    }>
    theme?: 'light' | 'dark'
    position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
    autoHide?: boolean
    [key: string]: any
  }

  export interface StagewiseToolbarProps {
    config?: ToolbarConfig
  }

  export const StagewiseToolbar: Component<StagewiseToolbarProps>
  
  export default StagewiseToolbar
}

// 全局类型扩展
declare global {
  interface Window {
    stagewise?: {
      toolbar?: any
      config?: any
    }
  }
}

export {}
