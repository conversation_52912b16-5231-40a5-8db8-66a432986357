{"version": 3, "sources": ["out-editor/vs/base/common/worker/simpleWorker.nls.es.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\ndefine(\"vs/base/common/worker/simpleWorker.nls.es\", {\n\t\"vs/base/common/platform\": [\n\t\t\"_\",\n\t],\n\t\"vs/editor/common/languages\": [\n\t\t\"matriz\",\n\t\t\"booleano\",\n\t\t\"clase\",\n\t\t\"constante\",\n\t\t\"constructor\",\n\t\t\"enumeración\",\n\t\t\"miembro de la enumeración\",\n\t\t\"evento\",\n\t\t\"campo\",\n\t\t\"archivo\",\n\t\t\"función\",\n\t\t\"interfaz\",\n\t\t\"clave\",\n\t\t\"método\",\n\t\t\"módulo\",\n\t\t\"espacio de nombres\",\n\t\t\"NULL\",\n\t\t\"número\",\n\t\t\"objeto\",\n\t\t\"operador\",\n\t\t\"paquete\",\n\t\t\"propiedad\",\n\t\t\"cadena\",\n\t\t\"estructura\",\n\t\t\"parámetro de tipo\",\n\t\t\"variable\",\n\t\t\"{0} ({1})\",\n\t]\n});"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DA<PERSON>,OAAO,4CAA6C,CACnD,0BAA2B,CAC1B,GACD,EACA,6BAA8B,CAC7B,SACA,WACA,QACA,YACA,cACA,iBACA,+BACA,SACA,QACA,UACA,aACA,WACA,QACA,YACA,YACA,qBACA,OACA,YACA,SACA,WACA,UACA,YACA,SACA,aACA,uBACA,WACA,WACD,CACD,CAAC", "names": [], "file": "simpleWorker.nls.es.js"}