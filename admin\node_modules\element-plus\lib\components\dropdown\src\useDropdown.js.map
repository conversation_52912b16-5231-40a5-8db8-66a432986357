{"version": 3, "file": "useDropdown.js", "sources": ["../../../../../../packages/components/dropdown/src/useDropdown.ts"], "sourcesContent": ["import { computed, inject } from 'vue'\nimport { DROPDOWN_INSTANCE_INJECTION_KEY } from './tokens'\nimport type { IElDropdownInstance } from './dropdown'\n\nexport const useDropdown = () => {\n  const elDropdown = inject<IElDropdownInstance>(\n    DROPDOWN_INSTANCE_INJECTION_KEY,\n    {}\n  )\n  const _elDropdownSize = computed(() => elDropdown?.dropdownSize)\n\n  return {\n    elDropdown,\n    _elDropdownSize,\n  }\n}\n"], "names": ["inject", "DROPDOWN_INSTANCE_INJECTION_KEY", "computed"], "mappings": ";;;;;;;AAEY,MAAC,WAAW,GAAG,MAAM;AACjC,EAAE,MAAM,UAAU,GAAGA,UAAM,CAACC,sCAA+B,EAAE,EAAE,CAAC,CAAC;AACjE,EAAE,MAAM,eAAe,GAAGC,YAAQ,CAAC,MAAM,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC;AAChG,EAAE,OAAO;AACT,IAAI,UAAU;AACd,IAAI,eAAe;AACnB,GAAG,CAAC;AACJ;;;;"}