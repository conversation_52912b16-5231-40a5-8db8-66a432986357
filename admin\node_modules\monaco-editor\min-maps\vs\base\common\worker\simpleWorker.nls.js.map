{"version": 3, "sources": ["out-editor/vs/base/common/worker/simpleWorker.nls.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\n/*---------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------*/\ndefine(\"vs/base/common/worker/simpleWorker.nls\", {\n\t\"vs/base/common/platform\": [\n\t\t\"_\"\n\t],\n\t\"vs/editor/common/languages\": [\n\t\t\"array\",\n\t\t\"boolean\",\n\t\t\"class\",\n\t\t\"constant\",\n\t\t\"constructor\",\n\t\t\"enumeration\",\n\t\t\"enumeration member\",\n\t\t\"event\",\n\t\t\"field\",\n\t\t\"file\",\n\t\t\"function\",\n\t\t\"interface\",\n\t\t\"key\",\n\t\t\"method\",\n\t\t\"module\",\n\t\t\"namespace\",\n\t\t\"null\",\n\t\t\"number\",\n\t\t\"object\",\n\t\t\"operator\",\n\t\t\"package\",\n\t\t\"property\",\n\t\t\"string\",\n\t\t\"struct\",\n\t\t\"type parameter\",\n\t\t\"variable\",\n\t\t\"{0} ({1})\"\n\t]\n});"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DAUA,OAAO,yCAA0C,CAChD,0BAA2B,CAC1B,GACD,EACA,6BAA8B,CAC7B,QACA,UACA,QACA,WACA,cACA,cACA,qBACA,QACA,QACA,OACA,WACA,YACA,MACA,SACA,SACA,YACA,OACA,SACA,SACA,WACA,UACA,WACA,SACA,SACA,iBACA,WACA,WACD,CACD,CAAC", "names": [], "file": "simpleWorker.nls.js"}