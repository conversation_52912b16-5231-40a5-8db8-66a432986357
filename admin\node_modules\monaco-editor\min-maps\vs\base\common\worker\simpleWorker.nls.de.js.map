{"version": 3, "sources": ["out-editor/vs/base/common/worker/simpleWorker.nls.de.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\ndefine(\"vs/base/common/worker/simpleWorker.nls.de\", {\n\t\"vs/base/common/platform\": [\n\t\t\"_\",\n\t],\n\t\"vs/editor/common/languages\": [\n\t\t\"Array\",\n\t\t\"Boolescher Wert\",\n\t\t\"Klasse\",\n\t\t\"Konstante\",\n\t\t\"Konstruktor\",\n\t\t\"Enumeration\",\n\t\t\"Enumerationsmember\",\n\t\t\"<PERSON><PERSON><PERSON><PERSON>\",\n\t\t\"Feld\",\n\t\t\"<PERSON><PERSON>\",\n\t\t\"<PERSON><PERSON>\",\n\t\t\"<PERSON><PERSON><PERSON>ste<PERSON>\",\n\t\t\"<PERSON><PERSON><PERSON><PERSON>\",\n\t\t\"<PERSON><PERSON>\",\n\t\t\"<PERSON><PERSON><PERSON>\",\n\t\t\"Namespace\",\n\t\t\"NULL\",\n\t\t\"<PERSON>ahl\",\n\t\t\"Objekt\",\n\t\t\"Operator\",\n\t\t\"Paket\",\n\t\t\"Eigenschaft\",\n\t\t\"Zeichenfolge\",\n\t\t\"Struktur\",\n\t\t\"Typparameter\",\n\t\t\"Variable\",\n\t\t\"{0} ({1})\",\n\t]\n});"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DAOA,OAAO,4CAA6C,CACnD,0BAA2B,CAC1B,GACD,EACA,6BAA8B,CAC7B,QACA,kBACA,SACA,YACA,cACA,cACA,qBACA,WACA,OACA,QACA,WACA,gBACA,eACA,UACA,QACA,YACA,OACA,OACA,SACA,WACA,QACA,cACA,eACA,WACA,eACA,WACA,WACD,CACD,CAAC", "names": [], "file": "simpleWorker.nls.de.js"}