<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - CORS跨域中间件
 */

declare(strict_types=1);

namespace app\middleware;

use think\Request;
use think\Response;

/**
 * CORS跨域中间件
 */
class CorsMiddleware
{
    /**
     * 处理请求
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle(Request $request, \Closure $next): Response
    {
        // 处理预检请求
        if ($request->method() === 'OPTIONS') {
            return $this->handlePreflightRequest();
        }

        $response = $next($request);

        return $this->addCorsHeaders($response);
    }

    /**
     * 处理预检请求
     * @return Response
     */
    private function handlePreflightRequest(): Response
    {
        $response = response('', 200);
        return $this->addCorsHeaders($response);
    }

    /**
     * 添加CORS头部
     * @param Response $response
     * @return Response
     */
    private function addCorsHeaders(Response $response): Response
    {
        $response->header([
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Methods' => 'GET, POST, PUT, DELETE, OPTIONS, PATCH',
            'Access-Control-Allow-Headers' => 'Content-Type, Authorization, X-Requested-With, Accept, Origin',
            'Access-Control-Allow-Credentials' => 'true',
            'Access-Control-Max-Age' => '86400',
        ]);

        return $response;
    }
}
