{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/message/index.ts"], "sourcesContent": ["import { withInstallFunction } from '@element-plus/utils'\n\nimport Message from './src/method'\n\nexport const ElMessage = withInstallFunction(Message, '$message')\nexport default ElMessage\n\nexport * from './src/message'\n"], "names": ["Message"], "mappings": ";;;;AAEY,MAAC,SAAS,GAAG,mBAAmB,CAACA,OAAO,EAAE,UAAU;;;;"}