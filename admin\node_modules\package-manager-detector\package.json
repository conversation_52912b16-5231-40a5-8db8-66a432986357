{"name": "package-manager-detector", "type": "module", "version": "1.3.0", "description": "Package manager detector", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/antfu-collective/package-manager-detector#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu-collective/package-manager-detector.git"}, "bugs": {"url": "https://github.com/antfu-collective/package-manager-detector/issues"}, "sideEffects": false, "exports": {".": "./dist/index.mjs", "./commands": "./dist/commands.mjs", "./detect": "./dist/detect.mjs", "./constants": "./dist/constants.mjs"}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.mts", "typesVersions": {"*": {"commands": ["./dist/commands.d.mts"], "detect": ["./dist/detect.d.mts"], "constants": ["./dist/constants.d.mts"]}}, "files": ["dist"], "devDependencies": {"@antfu/eslint-config": "^4.12.0", "@types/fs-extra": "^11.0.4", "@types/node": "^22.15.3", "bumpp": "^10.1.0", "eslint": "^9.26.0", "fs-extra": "^11.3.0", "typescript": "^5.8.3", "unbuild": "^3.5.0", "vitest": "^3.1.2", "vitest-package-exports": "^0.1.1"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub", "release": "bumpp", "lint": "eslint", "lint:fix": "eslint --fix", "test": "vitest"}}