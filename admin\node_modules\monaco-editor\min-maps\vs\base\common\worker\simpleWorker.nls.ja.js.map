{"version": 3, "sources": ["out-editor/vs/base/common/worker/simpleWorker.nls.ja.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\ndefine(\"vs/base/common/worker/simpleWorker.nls.ja\", {\n\t\"vs/base/common/platform\": [\n\t\t\"_\",\n\t],\n\t\"vs/editor/common/languages\": [\n\t\t\"配列\",\n\t\t\"ブール値\",\n\t\t\"クラス\",\n\t\t\"定数\",\n\t\t\"コンストラクター\",\n\t\t\"列挙型\",\n\t\t\"列挙型メンバー\",\n\t\t\"イベント\",\n\t\t\"フィールド\",\n\t\t\"ファイル\",\n\t\t\"関数\",\n\t\t\"インターフェイス\",\n\t\t\"キー\",\n\t\t\"メソッド\",\n\t\t\"モジュール\",\n\t\t\"名前空間\",\n\t\t\"NULL\",\n\t\t\"数値\",\n\t\t\"オブジェクト\",\n\t\t\"演算子\",\n\t\t\"パッケージ\",\n\t\t\"プロパティ\",\n\t\t\"文字列\",\n\t\t\"構造体\",\n\t\t\"型パラメーター\",\n\t\t\"変数\",\n\t\t\"{0} ({1})\",\n\t]\n});"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DAOA,OAAO,4CAA6C,CACnD,0BAA2B,CAC1B,GACD,EACA,6BAA8B,CAC7B,eACA,2BACA,qBACA,eACA,mDACA,qBACA,6CACA,2BACA,iCACA,2BACA,eACA,mDACA,eACA,2BACA,iCACA,2BACA,OACA,eACA,uCACA,qBACA,iCACA,iCACA,qBACA,qBACA,6CACA,eACA,WACD,CACD,CAAC", "names": [], "file": "simpleWorker.nls.ja.js"}