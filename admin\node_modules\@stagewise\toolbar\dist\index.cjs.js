"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const pluginUi_jsxRuntime=require("./jsx-runtime-r6LeVNNi.cjs"),panel=require("./panel-IzqaHDKs.cjs"),appStyle='/*! tailwindcss v4.1.5 | MIT License | https://tailwindcss.com */@layer properties{@supports ((-webkit-hyphens:none) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){stagewise-companion-anchor *,stagewise-companion-anchor :before,stagewise-companion-anchor :after,stagewise-companion-anchor ::backdrop{--tw-scale-x:1;--tw-scale-y:1;--tw-scale-z:1;--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-space-y-reverse:0;--tw-space-x-reverse:0;--tw-divide-y-reverse:0;--tw-border-style:solid;--tw-gradient-position:initial;--tw-gradient-from:#0000;--tw-gradient-via:#0000;--tw-gradient-to:#0000;--tw-gradient-stops:initial;--tw-gradient-via-stops:initial;--tw-gradient-from-position:0%;--tw-gradient-via-position:50%;--tw-gradient-to-position:100%;--tw-font-weight:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-outline-style:solid;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-backdrop-blur:initial;--tw-backdrop-brightness:initial;--tw-backdrop-contrast:initial;--tw-backdrop-grayscale:initial;--tw-backdrop-hue-rotate:initial;--tw-backdrop-invert:initial;--tw-backdrop-opacity:initial;--tw-backdrop-saturate:initial;--tw-backdrop-sepia:initial;--tw-duration:initial;--tw-ease:initial}}}@layer theme{stagewise-companion-anchor{--font-sans:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--color-red-100:oklch(93.6% .032 17.717);--color-red-200:oklch(88.5% .062 18.334);--color-red-500:oklch(63.7% .237 25.331);--color-red-600:oklch(57.7% .245 27.325);--color-red-700:oklch(50.5% .213 27.518);--color-orange-50:oklch(98% .016 73.684);--color-orange-100:oklch(95.4% .038 75.164);--color-orange-200:oklch(90.1% .076 70.697);--color-orange-300:oklch(83.7% .128 66.29);--color-orange-500:oklch(70.5% .213 47.604);--color-orange-600:oklch(64.6% .222 41.116);--color-orange-700:oklch(55.3% .195 38.402);--color-orange-800:oklch(47% .157 37.304);--color-amber-50:oklch(98.7% .022 95.277);--color-amber-800:oklch(47.3% .137 46.201);--color-yellow-500:oklch(79.5% .184 86.047);--color-green-500:oklch(72.3% .219 149.579);--color-green-600:oklch(62.7% .194 149.214);--color-teal-500:oklch(70.4% .14 182.503);--color-sky-600:oklch(58.8% .158 241.966);--color-sky-700:oklch(50% .134 242.749);--color-blue-50:oklch(97% .014 254.604);--color-blue-100:oklch(93.2% .032 255.585);--color-blue-200:oklch(88.2% .059 254.128);--color-blue-300:oklch(80.9% .105 251.813);--color-blue-500:oklch(62.3% .214 259.815);--color-blue-600:oklch(54.6% .245 262.881);--color-blue-700:oklch(48.8% .243 264.376);--color-blue-800:oklch(42.4% .199 265.638);--color-indigo-700:oklch(45.7% .24 277.023);--color-indigo-950:oklch(25.7% .09 281.288);--color-violet-700:oklch(49.1% .27 292.581);--color-purple-500:oklch(62.7% .265 303.9);--color-fuchsia-700:oklch(51.8% .253 323.949);--color-pink-500:oklch(65.6% .241 354.308);--color-rose-600:oklch(58.6% .253 17.585);--color-zinc-50:oklch(98.5% 0 0);--color-zinc-100:oklch(96.7% .001 286.375);--color-zinc-300:oklch(87.1% .006 286.286);--color-zinc-400:oklch(70.5% .015 286.067);--color-zinc-500:oklch(55.2% .016 285.938);--color-zinc-600:oklch(44.2% .017 285.786);--color-zinc-700:oklch(37% .013 285.805);--color-zinc-900:oklch(21% .006 285.885);--color-zinc-950:oklch(14.1% .005 285.823);--color-black:#000;--color-white:#fff;--spacing:.25rem;--text-xs:.75rem;--text-xs--line-height:calc(1/.75);--text-sm:.875rem;--text-sm--line-height:calc(1.25/.875);--text-base:1rem;--text-base--line-height: 1.5 ;--text-lg:1.125rem;--text-lg--line-height:calc(1.75/1.125);--font-weight-normal:400;--font-weight-medium:500;--font-weight-semibold:600;--font-weight-bold:700;--radius-md:.375rem;--radius-lg:.5rem;--radius-2xl:1rem;--radius-3xl:1.5rem;--drop-shadow-xs:0 1px 1px #0000000d;--drop-shadow-md:0 3px 3px #0000001f;--drop-shadow-xl:0 9px 7px #0000001a;--ease-out:cubic-bezier(0,0,.2,1);--animate-spin:spin 1s linear infinite;--animate-pulse:pulse 2s cubic-bezier(.4,0,.6,1)infinite;--blur-md:12px;--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-sans);--default-mono-font-family:var(--font-mono);--color-background:var(--color-white);--color-foreground:var(--color-zinc-950);--color-border:var(--color-zinc-500)}}@layer base{stagewise-companion-anchor *,stagewise-companion-anchor :after,stagewise-companion-anchor :before,stagewise-companion-anchor ::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}stagewise-companion-anchor ::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}:where(stagewise-companion-anchor),stagewise-companion-anchor{-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;line-height:1.5;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-feature-settings:var(--default-font-feature-settings,normal);font-variation-settings:var(--default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}stagewise-companion-anchor hr{height:0;color:inherit;border-top-width:1px}stagewise-companion-anchor abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}stagewise-companion-anchor h1,stagewise-companion-anchor h2,stagewise-companion-anchor h3,stagewise-companion-anchor h4,stagewise-companion-anchor h5,stagewise-companion-anchor h6{font-size:inherit;font-weight:inherit}stagewise-companion-anchor a{color:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}stagewise-companion-anchor b,stagewise-companion-anchor strong{font-weight:bolder}stagewise-companion-anchor code,stagewise-companion-anchor kbd,stagewise-companion-anchor samp,stagewise-companion-anchor pre{font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-feature-settings:var(--default-mono-font-feature-settings,normal);font-variation-settings:var(--default-mono-font-variation-settings,normal);font-size:1em}stagewise-companion-anchor small{font-size:80%}stagewise-companion-anchor sub,stagewise-companion-anchor sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}stagewise-companion-anchor sub{bottom:-.25em}stagewise-companion-anchor sup{top:-.5em}stagewise-companion-anchor table{text-indent:0;border-color:inherit;border-collapse:collapse}stagewise-companion-anchor :-moz-focusring{outline:auto}stagewise-companion-anchor progress{vertical-align:baseline}stagewise-companion-anchor summary{display:list-item}stagewise-companion-anchor ol,stagewise-companion-anchor ul,stagewise-companion-anchor menu{list-style:none}stagewise-companion-anchor img,stagewise-companion-anchor svg,stagewise-companion-anchor video,stagewise-companion-anchor canvas,stagewise-companion-anchor audio,stagewise-companion-anchor iframe,stagewise-companion-anchor embed,stagewise-companion-anchor object{vertical-align:middle;display:block}stagewise-companion-anchor img,stagewise-companion-anchor video{max-width:100%;height:auto}stagewise-companion-anchor button,stagewise-companion-anchor input,stagewise-companion-anchor select,stagewise-companion-anchor optgroup,stagewise-companion-anchor textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}stagewise-companion-anchor ::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}stagewise-companion-anchor :where(select:is([multiple],[size])) optgroup{font-weight:bolder}stagewise-companion-anchor :where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}stagewise-companion-anchor ::file-selector-button{margin-inline-end:4px}stagewise-companion-anchor ::-moz-placeholder{opacity:1}stagewise-companion-anchor ::placeholder{opacity:1}@supports (not (-webkit-appearance:-apple-pay-button)) or (contain-intrinsic-size:1px){stagewise-companion-anchor ::-moz-placeholder{color:currentColor}stagewise-companion-anchor ::placeholder{color:currentColor}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor ::-moz-placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}stagewise-companion-anchor ::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}stagewise-companion-anchor textarea{resize:vertical}stagewise-companion-anchor ::-webkit-search-decoration{-webkit-appearance:none}stagewise-companion-anchor ::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}stagewise-companion-anchor ::-webkit-datetime-edit{display:inline-flex}stagewise-companion-anchor ::-webkit-datetime-edit-fields-wrapper{padding:0}stagewise-companion-anchor ::-webkit-datetime-edit{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-year-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-month-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-day-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-hour-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-minute-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-second-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-millisecond-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-meridiem-field{padding-block:0}stagewise-companion-anchor :-moz-ui-invalid{box-shadow:none}stagewise-companion-anchor button,stagewise-companion-anchor input:where([type=button],[type=reset],[type=submit]){-webkit-appearance:button;-moz-appearance:button;appearance:button}stagewise-companion-anchor ::file-selector-button{-webkit-appearance:button;-moz-appearance:button;appearance:button}stagewise-companion-anchor ::-webkit-inner-spin-button{height:auto}stagewise-companion-anchor ::-webkit-outer-spin-button{height:auto}stagewise-companion-anchor [hidden]:where(:not([hidden=until-found])){display:none!important}stagewise-companion-anchor stagewise-companion-anchor *{min-width:0;min-height:0;position:relative}}@layer components{stagewise-companion-anchor .chat-loading-gradient{background:linear-gradient(#f8fafccc,#f8fafccc) padding-box padding-box,linear-gradient(45deg,#8b5cf6,#06b6d4,#8b5cf6) 0 0/400% 400% border-box;border:2px solid #0000;animation:2s infinite gradient-animation}stagewise-companion-anchor .chat-success-border{animation:2s ease-out blink-green-fade}stagewise-companion-anchor .chat-error-border{animation:1s ease-out blink-red-fade}@keyframes blink-green-fade{0%,50%{box-shadow:0 0 0 2px #22c55eb3}to{box-shadow:0 0 0 2px #22c55e00}}@keyframes blink-red-fade{0%,50%{box-shadow:0 0 0 2px #ef4444}to{box-shadow:0 0 0 2px #ef444400}}}@layer utilities{stagewise-companion-anchor .pointer-events-auto{pointer-events:auto!important}stagewise-companion-anchor .pointer-events-none{pointer-events:none!important}stagewise-companion-anchor .visible{visibility:visible!important}stagewise-companion-anchor .absolute{position:absolute!important}stagewise-companion-anchor .fixed{position:fixed!important}stagewise-companion-anchor .relative{position:relative!important}stagewise-companion-anchor .inset-0{inset:calc(var(--spacing)*0)!important}stagewise-companion-anchor .inset-4{inset:calc(var(--spacing)*4)!important}stagewise-companion-anchor .top-0{top:calc(var(--spacing)*0)!important}stagewise-companion-anchor .top-0\\.5{top:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .top-1\\/2{top:50%!important}stagewise-companion-anchor .top-\\[-20\\%\\]{top:-20%!important}stagewise-companion-anchor .top-\\[25\\%\\]{top:25%!important}stagewise-companion-anchor .right-0{right:calc(var(--spacing)*0)!important}stagewise-companion-anchor .right-1\\/2{right:50%!important}stagewise-companion-anchor .right-\\[100\\%\\]{right:100%!important}stagewise-companion-anchor .bottom-0{bottom:calc(var(--spacing)*0)!important}stagewise-companion-anchor .bottom-1\\/2{bottom:50%!important}stagewise-companion-anchor .bottom-3{bottom:calc(var(--spacing)*3)!important}stagewise-companion-anchor .left-0{left:calc(var(--spacing)*0)!important}stagewise-companion-anchor .left-0\\.5{left:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .left-1\\/2{left:50%!important}stagewise-companion-anchor .left-3{left:calc(var(--spacing)*3)!important}stagewise-companion-anchor .left-\\[-10\\%\\]{left:-10%!important}stagewise-companion-anchor .left-\\[25\\%\\]{left:25%!important}stagewise-companion-anchor .left-\\[100\\%\\]{left:100%!important}stagewise-companion-anchor .z-20{z-index:20!important}stagewise-companion-anchor .z-50{z-index:50!important}stagewise-companion-anchor .container{width:100%!important}@media (min-width:40rem){stagewise-companion-anchor .container{max-width:40rem!important}}@media (min-width:48rem){stagewise-companion-anchor .container{max-width:48rem!important}}@media (min-width:64rem){stagewise-companion-anchor .container{max-width:64rem!important}}@media (min-width:80rem){stagewise-companion-anchor .container{max-width:80rem!important}}@media (min-width:96rem){stagewise-companion-anchor .container{max-width:96rem!important}}stagewise-companion-anchor .-mx-4{margin-inline:calc(var(--spacing)*-4)!important}stagewise-companion-anchor .my-2{margin-block:calc(var(--spacing)*2)!important}stagewise-companion-anchor .mt-1{margin-top:calc(var(--spacing)*1)!important}stagewise-companion-anchor .mt-2{margin-top:calc(var(--spacing)*2)!important}stagewise-companion-anchor .mb-2{margin-bottom:calc(var(--spacing)*2)!important}stagewise-companion-anchor .mb-3{margin-bottom:calc(var(--spacing)*3)!important}stagewise-companion-anchor .block{display:block!important}stagewise-companion-anchor .contents{display:contents!important}stagewise-companion-anchor .flex{display:flex!important}stagewise-companion-anchor .hidden{display:none!important}stagewise-companion-anchor .inline{display:inline!important}stagewise-companion-anchor .aspect-square{aspect-ratio:1!important}stagewise-companion-anchor .size-0{width:calc(var(--spacing)*0)!important;height:calc(var(--spacing)*0)!important}stagewise-companion-anchor .size-1\\.5{width:calc(var(--spacing)*1.5)!important;height:calc(var(--spacing)*1.5)!important}stagewise-companion-anchor .size-2\\/3{width:66.6667%!important;height:66.6667%!important}stagewise-companion-anchor .size-3{width:calc(var(--spacing)*3)!important;height:calc(var(--spacing)*3)!important}stagewise-companion-anchor .size-4{width:calc(var(--spacing)*4)!important;height:calc(var(--spacing)*4)!important}stagewise-companion-anchor .size-4\\.5{width:calc(var(--spacing)*4.5)!important;height:calc(var(--spacing)*4.5)!important}stagewise-companion-anchor .size-5{width:calc(var(--spacing)*5)!important;height:calc(var(--spacing)*5)!important}stagewise-companion-anchor .size-6{width:calc(var(--spacing)*6)!important;height:calc(var(--spacing)*6)!important}stagewise-companion-anchor .size-8{width:calc(var(--spacing)*8)!important;height:calc(var(--spacing)*8)!important}stagewise-companion-anchor .size-9{width:calc(var(--spacing)*9)!important;height:calc(var(--spacing)*9)!important}stagewise-companion-anchor .size-9\\/12{width:75%!important;height:75%!important}stagewise-companion-anchor .size-12{width:calc(var(--spacing)*12)!important;height:calc(var(--spacing)*12)!important}stagewise-companion-anchor .size-\\[120\\%\\]{width:120%!important;height:120%!important}stagewise-companion-anchor .size-full{width:100%!important;height:100%!important}stagewise-companion-anchor .h-0{height:calc(var(--spacing)*0)!important}stagewise-companion-anchor .h-3{height:calc(var(--spacing)*3)!important}stagewise-companion-anchor .h-5{height:calc(var(--spacing)*5)!important}stagewise-companion-anchor .h-8{height:calc(var(--spacing)*8)!important}stagewise-companion-anchor .h-9\\.5{height:calc(var(--spacing)*9.5)!important}stagewise-companion-anchor .h-12{height:calc(var(--spacing)*12)!important}stagewise-companion-anchor .h-16{height:calc(var(--spacing)*16)!important}stagewise-companion-anchor .h-24{height:calc(var(--spacing)*24)!important}stagewise-companion-anchor .h-\\[50\\%\\]{height:50%!important}stagewise-companion-anchor .h-\\[120\\%\\]{height:120%!important}stagewise-companion-anchor .h-\\[calc\\(100vh-32px\\)\\]{height:calc(100vh - 32px)!important}stagewise-companion-anchor .h-\\[calc-size\\(auto\\)\\]{height:calc-size(auto)!important}stagewise-companion-anchor .h-\\[calc-size\\(auto\\,size\\)\\]{height:calc-size(auto,size)!important}stagewise-companion-anchor .h-auto{height:auto!important}stagewise-companion-anchor .h-full{height:100%!important}stagewise-companion-anchor .h-screen{height:100vh!important}stagewise-companion-anchor .max-h-full{max-height:100%!important}stagewise-companion-anchor .min-h-0{min-height:calc(var(--spacing)*0)!important}stagewise-companion-anchor .min-h-48{min-height:calc(var(--spacing)*48)!important}stagewise-companion-anchor .w-8{width:calc(var(--spacing)*8)!important}stagewise-companion-anchor .w-9\\.5{width:calc(var(--spacing)*9.5)!important}stagewise-companion-anchor .w-96{width:calc(var(--spacing)*96)!important}stagewise-companion-anchor .w-\\[50\\%\\]{width:50%!important}stagewise-companion-anchor .w-auto{width:auto!important}stagewise-companion-anchor .w-fit{width:-moz-fit-content!important;width:fit-content!important}stagewise-companion-anchor .w-full{width:100%!important}stagewise-companion-anchor .w-max{width:-moz-max-content!important;width:max-content!important}stagewise-companion-anchor .w-screen{width:100vw!important}stagewise-companion-anchor .max-w-8{max-width:calc(var(--spacing)*8)!important}stagewise-companion-anchor .max-w-90{max-width:calc(var(--spacing)*90)!important}stagewise-companion-anchor .max-w-\\[40vw\\]{max-width:40vw!important}stagewise-companion-anchor .max-w-full{max-width:100%!important}stagewise-companion-anchor .min-w-0{min-width:calc(var(--spacing)*0)!important}stagewise-companion-anchor .min-w-3{min-width:calc(var(--spacing)*3)!important}stagewise-companion-anchor .min-w-24{min-width:calc(var(--spacing)*24)!important}stagewise-companion-anchor .flex-1{flex:1!important}stagewise-companion-anchor .flex-shrink-0,stagewise-companion-anchor .shrink-0{flex-shrink:0!important}stagewise-companion-anchor .origin-bottom{transform-origin:bottom!important}stagewise-companion-anchor .origin-bottom-left{transform-origin:0 100%!important}stagewise-companion-anchor .origin-bottom-right{transform-origin:100% 100%!important}stagewise-companion-anchor .origin-center{transform-origin:50%!important}stagewise-companion-anchor .origin-top{transform-origin:top!important}stagewise-companion-anchor .origin-top-left{transform-origin:0 0!important}stagewise-companion-anchor .origin-top-right{transform-origin:100% 0!important}stagewise-companion-anchor .scale-25{--tw-scale-x:25%!important;--tw-scale-y:25%!important;--tw-scale-z:25%!important;scale:var(--tw-scale-x)var(--tw-scale-y)!important}stagewise-companion-anchor .scale-50{--tw-scale-x:50%!important;--tw-scale-y:50%!important;--tw-scale-z:50%!important;scale:var(--tw-scale-x)var(--tw-scale-y)!important}stagewise-companion-anchor .scale-100{--tw-scale-x:100%!important;--tw-scale-y:100%!important;--tw-scale-z:100%!important;scale:var(--tw-scale-x)var(--tw-scale-y)!important}stagewise-companion-anchor .transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)!important}stagewise-companion-anchor .animate-pulse{animation:var(--animate-pulse)!important}stagewise-companion-anchor .animate-spin{animation:var(--animate-spin)!important}stagewise-companion-anchor .cursor-copy{cursor:copy!important}stagewise-companion-anchor .cursor-not-allowed{cursor:not-allowed!important}stagewise-companion-anchor .cursor-pointer{cursor:pointer!important}stagewise-companion-anchor .resize{resize:both!important}stagewise-companion-anchor .resize-none{resize:none!important}stagewise-companion-anchor .snap-start{scroll-snap-align:start!important}stagewise-companion-anchor .list-inside{list-style-position:inside!important}stagewise-companion-anchor .list-decimal{list-style-type:decimal!important}stagewise-companion-anchor .flex-col{flex-direction:column!important}stagewise-companion-anchor .flex-col-reverse{flex-direction:column-reverse!important}stagewise-companion-anchor .flex-row{flex-direction:row!important}stagewise-companion-anchor .flex-wrap{flex-wrap:wrap!important}stagewise-companion-anchor .items-center{align-items:center!important}stagewise-companion-anchor .items-end{align-items:flex-end!important}stagewise-companion-anchor .items-start{align-items:flex-start!important}stagewise-companion-anchor .items-stretch{align-items:stretch!important}stagewise-companion-anchor .justify-between{justify-content:space-between!important}stagewise-companion-anchor .justify-center{justify-content:center!important}stagewise-companion-anchor .justify-end{justify-content:flex-end!important}stagewise-companion-anchor .justify-start{justify-content:flex-start!important}stagewise-companion-anchor .gap-0\\.5{gap:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .gap-1{gap:calc(var(--spacing)*1)!important}stagewise-companion-anchor .gap-2{gap:calc(var(--spacing)*2)!important}stagewise-companion-anchor .gap-3{gap:calc(var(--spacing)*3)!important}stagewise-companion-anchor :where(.space-y-1>:not(:last-child)){--tw-space-y-reverse:0!important;margin-block-start:calc(calc(var(--spacing)*1)*var(--tw-space-y-reverse))!important;margin-block-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-y-reverse)))!important}stagewise-companion-anchor :where(.space-y-2>:not(:last-child)){--tw-space-y-reverse:0!important;margin-block-start:calc(calc(var(--spacing)*2)*var(--tw-space-y-reverse))!important;margin-block-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-y-reverse)))!important}stagewise-companion-anchor :where(.space-y-3>:not(:last-child)){--tw-space-y-reverse:0!important;margin-block-start:calc(calc(var(--spacing)*3)*var(--tw-space-y-reverse))!important;margin-block-end:calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-y-reverse)))!important}stagewise-companion-anchor :where(.space-y-4>:not(:last-child)){--tw-space-y-reverse:0!important;margin-block-start:calc(calc(var(--spacing)*4)*var(--tw-space-y-reverse))!important;margin-block-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-y-reverse)))!important}stagewise-companion-anchor :where(.space-x-2>:not(:last-child)){--tw-space-x-reverse:0!important;margin-inline-start:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse))!important;margin-inline-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))!important}stagewise-companion-anchor :where(.divide-y>:not(:last-child)){--tw-divide-y-reverse:0!important;border-bottom-style:var(--tw-border-style)!important;border-top-style:var(--tw-border-style)!important;border-top-width:calc(1px*var(--tw-divide-y-reverse))!important;border-bottom-width:calc(1px*calc(1 - var(--tw-divide-y-reverse)))!important}stagewise-companion-anchor :where(.divide-y-reverse>:not(:last-child)){--tw-divide-y-reverse:1!important}stagewise-companion-anchor :where(.divide-blue-200>:not(:last-child)){border-color:var(--color-blue-200)!important}stagewise-companion-anchor :where(.divide-border\\/20>:not(:last-child)){border-color:#71717b33!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor :where(.divide-border\\/20>:not(:last-child)){border-color:color-mix(in oklab,var(--color-border)20%,transparent)!important}}stagewise-companion-anchor :where(.divide-orange-200>:not(:last-child)){border-color:var(--color-orange-200)!important}stagewise-companion-anchor .truncate{text-overflow:ellipsis!important;white-space:nowrap!important;overflow:hidden!important}stagewise-companion-anchor .overflow-hidden{overflow:hidden!important}stagewise-companion-anchor .overflow-visible{overflow:visible!important}stagewise-companion-anchor .overflow-y-auto{overflow-y:auto!important}stagewise-companion-anchor .rounded{border-radius:.25rem!important}stagewise-companion-anchor .rounded-2xl{border-radius:var(--radius-2xl)!important}stagewise-companion-anchor .rounded-full{border-radius:3.40282e38px!important}stagewise-companion-anchor .rounded-lg{border-radius:var(--radius-lg)!important}stagewise-companion-anchor .rounded-md{border-radius:var(--radius-md)!important}stagewise-companion-anchor .rounded-t-3xl{border-top-left-radius:var(--radius-3xl)!important;border-top-right-radius:var(--radius-3xl)!important}stagewise-companion-anchor .rounded-t-lg{border-top-left-radius:var(--radius-lg)!important;border-top-right-radius:var(--radius-lg)!important}stagewise-companion-anchor .rounded-b-3xl{border-bottom-right-radius:var(--radius-3xl)!important;border-bottom-left-radius:var(--radius-3xl)!important}stagewise-companion-anchor .rounded-b-lg{border-bottom-right-radius:var(--radius-lg)!important;border-bottom-left-radius:var(--radius-lg)!important}stagewise-companion-anchor .border{border-style:var(--tw-border-style)!important;border-width:1px!important}stagewise-companion-anchor .border-2{border-style:var(--tw-border-style)!important;border-width:2px!important}stagewise-companion-anchor .border-t{border-top-style:var(--tw-border-style)!important;border-top-width:1px!important}stagewise-companion-anchor .border-solid{--tw-border-style:solid!important;border-style:solid!important}stagewise-companion-anchor .border-blue-200{border-color:var(--color-blue-200)!important}stagewise-companion-anchor .border-blue-300{border-color:var(--color-blue-300)!important}stagewise-companion-anchor .border-blue-500{border-color:var(--color-blue-500)!important}stagewise-companion-anchor .border-blue-600\\/80{border-color:#155dfccc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .border-blue-600\\/80{border-color:color-mix(in oklab,var(--color-blue-600)80%,transparent)!important}}stagewise-companion-anchor .border-border\\/30{border-color:#71717b4d!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .border-border\\/30{border-color:color-mix(in oklab,var(--color-border)30%,transparent)!important}}stagewise-companion-anchor .border-green-500{border-color:var(--color-green-500)!important}stagewise-companion-anchor .border-green-600\\/80{border-color:#00a544cc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .border-green-600\\/80{border-color:color-mix(in oklab,var(--color-green-600)80%,transparent)!important}}stagewise-companion-anchor .border-orange-200{border-color:var(--color-orange-200)!important}stagewise-companion-anchor .border-orange-300{border-color:var(--color-orange-300)!important}stagewise-companion-anchor .border-orange-500{border-color:var(--color-orange-500)!important}stagewise-companion-anchor .border-pink-500{border-color:var(--color-pink-500)!important}stagewise-companion-anchor .border-purple-500{border-color:var(--color-purple-500)!important}stagewise-companion-anchor .border-red-200{border-color:var(--color-red-200)!important}stagewise-companion-anchor .border-red-500{border-color:var(--color-red-500)!important}stagewise-companion-anchor .border-transparent{border-color:#0000!important}stagewise-companion-anchor .border-yellow-500{border-color:var(--color-yellow-500)!important}stagewise-companion-anchor .border-zinc-300{border-color:var(--color-zinc-300)!important}stagewise-companion-anchor .border-zinc-500{border-color:var(--color-zinc-500)!important}stagewise-companion-anchor .bg-amber-50{background-color:var(--color-amber-50)!important}stagewise-companion-anchor .bg-background\\/60{background-color:#fff9!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-background\\/60{background-color:color-mix(in oklab,var(--color-background)60%,transparent)!important}}stagewise-companion-anchor .bg-blue-50{background-color:var(--color-blue-50)!important}stagewise-companion-anchor .bg-blue-50\\/90{background-color:#eff6ffe6!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-blue-50\\/90{background-color:color-mix(in oklab,var(--color-blue-50)90%,transparent)!important}}stagewise-companion-anchor .bg-blue-100\\/80{background-color:#dbeafecc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-blue-100\\/80{background-color:color-mix(in oklab,var(--color-blue-100)80%,transparent)!important}}stagewise-companion-anchor .bg-blue-500{background-color:var(--color-blue-500)!important}stagewise-companion-anchor .bg-blue-600{background-color:var(--color-blue-600)!important}stagewise-companion-anchor .bg-blue-600\\/20{background-color:#155dfc33!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-blue-600\\/20{background-color:color-mix(in oklab,var(--color-blue-600)20%,transparent)!important}}stagewise-companion-anchor .bg-green-500{background-color:var(--color-green-500)!important}stagewise-companion-anchor .bg-green-600\\/5{background-color:#00a5440d!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-green-600\\/5{background-color:color-mix(in oklab,var(--color-green-600)5%,transparent)!important}}stagewise-companion-anchor .bg-orange-50\\/90{background-color:#fff7ede6!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-orange-50\\/90{background-color:color-mix(in oklab,var(--color-orange-50)90%,transparent)!important}}stagewise-companion-anchor .bg-orange-100\\/80{background-color:#ffedd5cc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-orange-100\\/80{background-color:color-mix(in oklab,var(--color-orange-100)80%,transparent)!important}}stagewise-companion-anchor .bg-orange-500{background-color:var(--color-orange-500)!important}stagewise-companion-anchor .bg-orange-600{background-color:var(--color-orange-600)!important}stagewise-companion-anchor .bg-pink-500{background-color:var(--color-pink-500)!important}stagewise-companion-anchor .bg-purple-500{background-color:var(--color-purple-500)!important}stagewise-companion-anchor .bg-red-100{background-color:var(--color-red-100)!important}stagewise-companion-anchor .bg-red-500{background-color:var(--color-red-500)!important}stagewise-companion-anchor .bg-rose-600{background-color:var(--color-rose-600)!important}stagewise-companion-anchor .bg-transparent{background-color:#0000!important}stagewise-companion-anchor .bg-white{background-color:var(--color-white)!important}stagewise-companion-anchor .bg-white\\/40{background-color:#fff6!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-white\\/40{background-color:color-mix(in oklab,var(--color-white)40%,transparent)!important}}stagewise-companion-anchor .bg-white\\/80{background-color:#fffc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-white\\/80{background-color:color-mix(in oklab,var(--color-white)80%,transparent)!important}}stagewise-companion-anchor .bg-white\\/90{background-color:#ffffffe6!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-white\\/90{background-color:color-mix(in oklab,var(--color-white)90%,transparent)!important}}stagewise-companion-anchor .bg-yellow-500{background-color:var(--color-yellow-500)!important}stagewise-companion-anchor .bg-zinc-50\\/80{background-color:#fafafacc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-zinc-50\\/80{background-color:color-mix(in oklab,var(--color-zinc-50)80%,transparent)!important}}stagewise-companion-anchor .bg-zinc-300{background-color:var(--color-zinc-300)!important}stagewise-companion-anchor .bg-zinc-500{background-color:var(--color-zinc-500)!important}stagewise-companion-anchor .bg-zinc-500\\/10{background-color:#71717b1a!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-zinc-500\\/10{background-color:color-mix(in oklab,var(--color-zinc-500)10%,transparent)!important}}stagewise-companion-anchor .bg-zinc-500\\/40{background-color:#71717b66!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-zinc-500\\/40{background-color:color-mix(in oklab,var(--color-zinc-500)40%,transparent)!important}}stagewise-companion-anchor .bg-zinc-700\\/80{background-color:#3f3f46cc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-zinc-700\\/80{background-color:color-mix(in oklab,var(--color-zinc-700)80%,transparent)!important}}stagewise-companion-anchor .bg-gradient-to-tr{--tw-gradient-position:to top right in oklab!important;background-image:linear-gradient(var(--tw-gradient-stops))!important}stagewise-companion-anchor .bg-\\[radial-gradient\\(circle\\,rgba\\(55\\,48\\,163\\,0\\)_55\\%\\,rgba\\(55\\,48\\,163\\,0\\.35\\)_73\\%\\)\\]{background-image:radial-gradient(circle,#3730a300 55%,#3730a359 73%)!important}stagewise-companion-anchor .bg-\\[radial-gradient\\(circle\\,rgba\\(219\\,39\\,119\\,0\\.2\\)_0\\%\\,rgba\\(219\\,39\\,119\\,0\\)_100\\%\\)\\]{background-image:radial-gradient(circle,#db277733,#db277700)!important}stagewise-companion-anchor .bg-\\[radial-gradient\\(circle\\,rgba\\(255\\,255\\,255\\,0\\)_60\\%\\,rgba\\(255\\,255\\,255\\,0\\.2\\)_70\\%\\)\\]{background-image:radial-gradient(circle,#fff0 60%,#fff3 70%)!important}stagewise-companion-anchor .from-blue-600{--tw-gradient-from:var(--color-blue-600)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .from-indigo-700{--tw-gradient-from:var(--color-indigo-700)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .from-orange-600{--tw-gradient-from:var(--color-orange-600)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .from-sky-700{--tw-gradient-from:var(--color-sky-700)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .via-blue-500{--tw-gradient-via:var(--color-blue-500)!important;--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position)!important;--tw-gradient-stops:var(--tw-gradient-via-stops)!important}stagewise-companion-anchor .to-fuchsia-700{--tw-gradient-to:var(--color-fuchsia-700)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .to-red-600{--tw-gradient-to:var(--color-red-600)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .to-sky-600{--tw-gradient-to:var(--color-sky-600)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .to-teal-500{--tw-gradient-to:var(--color-teal-500)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .fill-current{fill:currentColor!important}stagewise-companion-anchor .fill-white{fill:var(--color-white)!important}stagewise-companion-anchor .fill-zinc-500\\/50{fill:#71717b80!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .fill-zinc-500\\/50{fill:color-mix(in oklab,var(--color-zinc-500)50%,transparent)!important}}stagewise-companion-anchor .fill-zinc-950{fill:var(--color-zinc-950)!important}stagewise-companion-anchor .stroke-black\\/30{stroke:#0000004d!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .stroke-black\\/30{stroke:color-mix(in oklab,var(--color-black)30%,transparent)!important}}stagewise-companion-anchor .stroke-none{stroke:none!important}stagewise-companion-anchor .stroke-white{stroke:var(--color-white)!important}stagewise-companion-anchor .stroke-zinc-950{stroke:var(--color-zinc-950)!important}stagewise-companion-anchor .stroke-1{stroke-width:1px!important}stagewise-companion-anchor .p-0\\.5{padding:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .p-1{padding:calc(var(--spacing)*1)!important}stagewise-companion-anchor .p-2{padding:calc(var(--spacing)*2)!important}stagewise-companion-anchor .p-3{padding:calc(var(--spacing)*3)!important}stagewise-companion-anchor .p-4{padding:calc(var(--spacing)*4)!important}stagewise-companion-anchor .px-0\\.5{padding-inline:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .px-1{padding-inline:calc(var(--spacing)*1)!important}stagewise-companion-anchor .px-2{padding-inline:calc(var(--spacing)*2)!important}stagewise-companion-anchor .px-3{padding-inline:calc(var(--spacing)*3)!important}stagewise-companion-anchor .px-4{padding-inline:calc(var(--spacing)*4)!important}stagewise-companion-anchor .py-0{padding-block:calc(var(--spacing)*0)!important}stagewise-companion-anchor .py-0\\.5{padding-block:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .py-2{padding-block:calc(var(--spacing)*2)!important}stagewise-companion-anchor .pt-2{padding-top:calc(var(--spacing)*2)!important}stagewise-companion-anchor .pt-4{padding-top:calc(var(--spacing)*4)!important}stagewise-companion-anchor .pr-6{padding-right:calc(var(--spacing)*6)!important}stagewise-companion-anchor .pb-4{padding-bottom:calc(var(--spacing)*4)!important}stagewise-companion-anchor .pl-2{padding-left:calc(var(--spacing)*2)!important}stagewise-companion-anchor .text-base{font-size:var(--text-base)!important;line-height:var(--tw-leading,var(--text-base--line-height))!important}stagewise-companion-anchor .text-lg{font-size:var(--text-lg)!important;line-height:var(--tw-leading,var(--text-lg--line-height))!important}stagewise-companion-anchor .text-sm{font-size:var(--text-sm)!important;line-height:var(--tw-leading,var(--text-sm--line-height))!important}stagewise-companion-anchor .text-xs{font-size:var(--text-xs)!important;line-height:var(--tw-leading,var(--text-xs--line-height))!important}stagewise-companion-anchor .text-\\[0\\.5em\\]{font-size:.5em!important}stagewise-companion-anchor .font-bold{--tw-font-weight:var(--font-weight-bold)!important;font-weight:var(--font-weight-bold)!important}stagewise-companion-anchor .font-medium{--tw-font-weight:var(--font-weight-medium)!important;font-weight:var(--font-weight-medium)!important}stagewise-companion-anchor .font-normal{--tw-font-weight:var(--font-weight-normal)!important;font-weight:var(--font-weight-normal)!important}stagewise-companion-anchor .font-semibold{--tw-font-weight:var(--font-weight-semibold)!important;font-weight:var(--font-weight-semibold)!important}stagewise-companion-anchor .text-amber-800{color:var(--color-amber-800)!important}stagewise-companion-anchor .text-blue-500{color:var(--color-blue-500)!important}stagewise-companion-anchor .text-blue-600{color:var(--color-blue-600)!important}stagewise-companion-anchor .text-blue-700{color:var(--color-blue-700)!important}stagewise-companion-anchor .text-blue-800{color:var(--color-blue-800)!important}stagewise-companion-anchor .text-foreground{color:var(--color-foreground)!important}stagewise-companion-anchor .text-indigo-700{color:var(--color-indigo-700)!important}stagewise-companion-anchor .text-orange-600{color:var(--color-orange-600)!important}stagewise-companion-anchor .text-orange-700{color:var(--color-orange-700)!important}stagewise-companion-anchor .text-orange-800{color:var(--color-orange-800)!important}stagewise-companion-anchor .text-red-600{color:var(--color-red-600)!important}stagewise-companion-anchor .text-red-700{color:var(--color-red-700)!important}stagewise-companion-anchor .text-transparent{color:#0000!important}stagewise-companion-anchor .text-violet-700{color:var(--color-violet-700)!important}stagewise-companion-anchor .text-white{color:var(--color-white)!important}stagewise-companion-anchor .text-zinc-500{color:var(--color-zinc-500)!important}stagewise-companion-anchor .text-zinc-600{color:var(--color-zinc-600)!important}stagewise-companion-anchor .text-zinc-700{color:var(--color-zinc-700)!important}stagewise-companion-anchor .text-zinc-950{color:var(--color-zinc-950)!important}stagewise-companion-anchor .opacity-0{opacity:0!important}stagewise-companion-anchor .opacity-20{opacity:.2!important}stagewise-companion-anchor .opacity-30{opacity:.3!important}stagewise-companion-anchor .opacity-80{opacity:.8!important}stagewise-companion-anchor .opacity-100{opacity:1!important}stagewise-companion-anchor .shadow{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .shadow-lg{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .shadow-md{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .shadow-sm{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .ring{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .shadow-black\\/50{--tw-shadow-color:#00000080!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .shadow-black\\/50{--tw-shadow-color:color-mix(in oklab,color-mix(in oklab,var(--color-black)50%,transparent)var(--tw-shadow-alpha),transparent)!important}}stagewise-companion-anchor .ring-transparent{--tw-ring-color:transparent!important}stagewise-companion-anchor .ring-zinc-950\\/20{--tw-ring-color:#09090b33!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .ring-zinc-950\\/20{--tw-ring-color:color-mix(in oklab,var(--color-zinc-950)20%,transparent)!important}}stagewise-companion-anchor .outline{outline-style:var(--tw-outline-style)!important;outline-width:1px!important}stagewise-companion-anchor .blur{--tw-blur:blur(8px)!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .blur-md{--tw-blur:blur(var(--blur-md))!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .blur-none{--tw-blur: !important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .drop-shadow-md{--tw-drop-shadow-size:drop-shadow(0 3px 3px var(--tw-drop-shadow-color,#0000001f))!important;--tw-drop-shadow:drop-shadow(var(--drop-shadow-md))!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .drop-shadow-xl{--tw-drop-shadow-size:drop-shadow(0 9px 7px var(--tw-drop-shadow-color,#0000001a))!important;--tw-drop-shadow:drop-shadow(var(--drop-shadow-xl))!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .drop-shadow-xs{--tw-drop-shadow-size:drop-shadow(0 1px 1px var(--tw-drop-shadow-color,#0000000d))!important;--tw-drop-shadow:drop-shadow(var(--drop-shadow-xs))!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .drop-shadow-black{--tw-drop-shadow-color:#000!important;--tw-drop-shadow:var(--tw-drop-shadow-size)!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .drop-shadow-black{--tw-drop-shadow-color:color-mix(in oklab,var(--color-black)var(--tw-drop-shadow-alpha),transparent)!important}}stagewise-companion-anchor .drop-shadow-indigo-950{--tw-drop-shadow-color:oklch(25.7% .09 281.288)!important;--tw-drop-shadow:var(--tw-drop-shadow-size)!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .drop-shadow-indigo-950{--tw-drop-shadow-color:color-mix(in oklab,var(--color-indigo-950)var(--tw-drop-shadow-alpha),transparent)!important}}stagewise-companion-anchor .filter{filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .backdrop-blur{--tw-backdrop-blur:blur(8px)!important;-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important;backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important}stagewise-companion-anchor .backdrop-blur-md{--tw-backdrop-blur:blur(var(--blur-md))!important;-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important;backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important}stagewise-companion-anchor .backdrop-saturate-150{--tw-backdrop-saturate:saturate(150%)!important;-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important;backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important}stagewise-companion-anchor .transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events!important;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function))!important;transition-duration:var(--tw-duration,var(--default-transition-duration))!important}stagewise-companion-anchor .transition-all{transition-property:all!important;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function))!important;transition-duration:var(--tw-duration,var(--default-transition-duration))!important}stagewise-companion-anchor .transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to!important;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function))!important;transition-duration:var(--tw-duration,var(--default-transition-duration))!important}stagewise-companion-anchor .duration-0{--tw-duration:0s!important;transition-duration:0s!important}stagewise-companion-anchor .duration-100{--tw-duration:.1s!important;transition-duration:.1s!important}stagewise-companion-anchor .duration-150{--tw-duration:.15s!important;transition-duration:.15s!important}stagewise-companion-anchor .duration-300{--tw-duration:.3s!important;transition-duration:.3s!important}stagewise-companion-anchor .duration-500{--tw-duration:.5s!important;transition-duration:.5s!important}stagewise-companion-anchor .ease-out{--tw-ease:var(--ease-out)!important;transition-timing-function:var(--ease-out)!important}stagewise-companion-anchor .outline-none{--tw-outline-style:none!important;outline-style:none!important}stagewise-companion-anchor .select-none{-webkit-user-select:none!important;-moz-user-select:none!important;user-select:none!important}stagewise-companion-anchor :is(.\\*\\:size-full>*){width:100%!important;height:100%!important}stagewise-companion-anchor .placeholder\\:text-zinc-400::-moz-placeholder{color:var(--color-zinc-400)!important}stagewise-companion-anchor .placeholder\\:text-zinc-400::placeholder{color:var(--color-zinc-400)!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/50::-moz-placeholder{color:#09090b80!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/50::placeholder{color:#09090b80!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .placeholder\\:text-zinc-950\\/50::-moz-placeholder{color:color-mix(in oklab,var(--color-zinc-950)50%,transparent)!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/50::placeholder{color:color-mix(in oklab,var(--color-zinc-950)50%,transparent)!important}}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/70::-moz-placeholder{color:#09090bb3!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/70::placeholder{color:#09090bb3!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .placeholder\\:text-zinc-950\\/70::-moz-placeholder{color:color-mix(in oklab,var(--color-zinc-950)70%,transparent)!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/70::placeholder{color:color-mix(in oklab,var(--color-zinc-950)70%,transparent)!important}}@media (hover:hover){stagewise-companion-anchor .hover\\:border-red-600\\/80:hover{border-color:#e40014cc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:border-red-600\\/80:hover{border-color:color-mix(in oklab,var(--color-red-600)80%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-blue-200\\/80:hover{background-color:#bedbffcc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:bg-blue-200\\/80:hover{background-color:color-mix(in oklab,var(--color-blue-200)80%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-orange-200:hover{background-color:var(--color-orange-200)!important}stagewise-companion-anchor .hover\\:bg-orange-700:hover{background-color:var(--color-orange-700)!important}stagewise-companion-anchor .hover\\:bg-red-600\\/20:hover{background-color:#e4001433!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:bg-red-600\\/20:hover{background-color:color-mix(in oklab,var(--color-red-600)20%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-zinc-500\\/20:hover{background-color:#71717b33!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:bg-zinc-500\\/20:hover{background-color:color-mix(in oklab,var(--color-zinc-500)20%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-zinc-950\\/5:hover{background-color:#09090b0d!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:bg-zinc-950\\/5:hover{background-color:color-mix(in oklab,var(--color-zinc-950)5%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-zinc-950\\/10:hover{background-color:#09090b1a!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:bg-zinc-950\\/10:hover{background-color:color-mix(in oklab,var(--color-zinc-950)10%,transparent)!important}}stagewise-companion-anchor .hover\\:text-orange-800:hover{color:var(--color-orange-800)!important}stagewise-companion-anchor .hover\\:text-white:hover{color:var(--color-white)!important}stagewise-companion-anchor .hover\\:underline:hover{text-decoration-line:underline!important}stagewise-companion-anchor .hover\\:opacity-100:hover{opacity:1!important}stagewise-companion-anchor .hover\\:shadow-lg:hover{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}}stagewise-companion-anchor .focus\\:border-blue-500:focus{border-color:var(--color-blue-500)!important}stagewise-companion-anchor .focus\\:border-zinc-500:focus{border-color:var(--color-zinc-500)!important}stagewise-companion-anchor .focus\\:text-zinc-900:focus{color:var(--color-zinc-900)!important}stagewise-companion-anchor .focus\\:outline-none:focus{--tw-outline-style:none!important;outline-style:none!important}stagewise-companion-anchor .disabled\\:opacity-50:disabled{opacity:.5!important}stagewise-companion-anchor .data-focus\\:outline-none[data-focus]{--tw-outline-style:none!important;outline-style:none!important}stagewise-companion-anchor .animate-shake{animation:.5s ease-in-out 2 shake}}@keyframes shake{0%,to{transform:translate(0)}10%,30%,50%,70%,90%{transform:translate(-2px)}20%,40%,60%,80%{transform:translate(2px)}}@keyframes gradient-animation{0%{background-position:0%}50%{background-position:100%}to{background-position:0%}}stagewise-companion-anchor stagewise-companion-anchor{all:initial;interpolate-size:allow-keywords;transform:translate(0);color:var(--color-zinc-950)!important;letter-spacing:normal!important;text-rendering:auto!important;font-family:Inter,Noto Color Emoji,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,SF Compact,SF Pro,Helvetica Neue,sans-serif!important;font-weight:400!important;line-height:normal!important}@supports (font-variation-settings:normal){stagewise-companion-anchor stagewise-companion-anchor{font-optical-sizing:auto!important;font-family:InterVariable,Noto Color Emoji,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,SF Compact,SF Pro,Helvetica Neue,sans-serif!important}}stagewise-companion-anchor #headlessui-portal-root{z-index:50!important;width:100vw!important;height:100vh!important;position:fixed!important}stagewise-companion-anchor #headlessui-portal-root>*{pointer-events:auto!important}@property --tw-scale-x{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-y{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-z{syntax:"*";inherits:false;initial-value:1}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-space-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-space-x-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-divide-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-gradient-position{syntax:"*";inherits:false}@property --tw-gradient-from{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-via{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-to{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-stops{syntax:"*";inherits:false}@property --tw-gradient-via-stops{syntax:"*";inherits:false}@property --tw-gradient-from-position{syntax:"<length-percentage>";inherits:false;initial-value:0%}@property --tw-gradient-via-position{syntax:"<length-percentage>";inherits:false;initial-value:50%}@property --tw-gradient-to-position{syntax:"<length-percentage>";inherits:false;initial-value:100%}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-blur{syntax:"*";inherits:false}@property --tw-brightness{syntax:"*";inherits:false}@property --tw-contrast{syntax:"*";inherits:false}@property --tw-grayscale{syntax:"*";inherits:false}@property --tw-hue-rotate{syntax:"*";inherits:false}@property --tw-invert{syntax:"*";inherits:false}@property --tw-opacity{syntax:"*";inherits:false}@property --tw-saturate{syntax:"*";inherits:false}@property --tw-sepia{syntax:"*";inherits:false}@property --tw-drop-shadow{syntax:"*";inherits:false}@property --tw-drop-shadow-color{syntax:"*";inherits:false}@property --tw-drop-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:"*";inherits:false}@property --tw-backdrop-blur{syntax:"*";inherits:false}@property --tw-backdrop-brightness{syntax:"*";inherits:false}@property --tw-backdrop-contrast{syntax:"*";inherits:false}@property --tw-backdrop-grayscale{syntax:"*";inherits:false}@property --tw-backdrop-hue-rotate{syntax:"*";inherits:false}@property --tw-backdrop-invert{syntax:"*";inherits:false}@property --tw-backdrop-opacity{syntax:"*";inherits:false}@property --tw-backdrop-saturate{syntax:"*";inherits:false}@property --tw-backdrop-sepia{syntax:"*";inherits:false}@property --tw-duration{syntax:"*";inherits:false}@property --tw-ease{syntax:"*";inherits:false}@keyframes spin{to{transform:rotate(360deg)}}@keyframes pulse{50%{opacity:.5}}';function getElementAttributes(element){const attrs={},priorityAttrs=["id","class","name","type","href","src","alt","for","placeholder"],dataAttrs=[];for(let i2=0;i2<element.attributes.length;i2++){const attr=element.attributes[i2];attr.name.startsWith("data-")?dataAttrs.push({name:attr.name,value:attr.value}):(priorityAttrs.includes(attr.name.toLowerCase())||attr.name.toLowerCase()!=="style")&&(attrs[attr.name]=attr.value)}return dataAttrs.forEach(da=>{attrs[da.name]=da.value}),attrs}function generateElementContext(element,index){var _a;let context=`<element index="${index+1}">
`;context+=`  <tag>${element.tagName.toLowerCase()}</tag>
`;const id=element.id;id&&(context+=`  <id>${id}</id>
`);const classes=Array.from(element.classList).join(", ");classes&&(context+=`  <classes>${classes}</classes>
`);const attributes=getElementAttributes(element);if(Object.keys(attributes).length>0){context+=`  <attributes>
`;for(const[key,value]of Object.entries(attributes))(key.toLowerCase()!=="class"||!classes)&&(context+=`    <${key}>${value}</${key}>
`);context+=`  </attributes>
`}const text=(_a=element.innerText)==null?void 0:_a.trim();if(text&&(context+=`  <text>${text.length>100?`${text.substring(0,100)}...`:text}</text>
`),context+=`  <structural_context>
`,element.parentElement){const parent=element.parentElement;context+=`    <parent>
`,context+=`      <tag>${parent.tagName.toLowerCase()}</tag>
`,parent.id&&(context+=`      <id>${parent.id}</id>
`);const parentClasses=Array.from(parent.classList).join(", ");parentClasses&&(context+=`      <classes>${parentClasses}</classes>
`),context+=`    </parent>
`}else context+=`    <parent>No parent element found (likely root or disconnected)</parent>
`;context+=`  </structural_context>
`;try{const styles=window.getComputedStyle(element),relevantStyles={color:styles.color,backgroundColor:styles.backgroundColor,fontSize:styles.fontSize,fontWeight:styles.fontWeight,display:styles.display};context+=`  <styles>
`;for(const[key,value]of Object.entries(relevantStyles))context+=`    <${key}>${value}</${key}>
`;context+=`  </styles>
`}catch{context+=`  <styles>Could not retrieve computed styles</styles>
`}return context+=`</element>
`,context}function createPrompt(selectedElements,userPrompt,url,contextSnippets){const pluginContext=contextSnippets.map(snippet=>`
      <plugin_contexts>
<${snippet.pluginName}>
${snippet.contextSnippets.map(snippet2=>`    <${snippet2.promptContextName}>${snippet2.content}</${snippet2.promptContextName}>`).join(`
`)}
</${snippet.pluginName}>
</plugin_contexts>
`.trim()).join(`
`);if(!selectedElements||selectedElements.length===0)return`
    <request>
      <user_goal>${userPrompt}</user_goal>
      <url>${url}</url>
  <context>No specific element was selected on the page. Please analyze the page code in general or ask for clarification.</context>
  ${pluginContext}
</request>`.trim();let detailedContext="";return selectedElements.forEach((element,index)=>{detailedContext+=generateElementContext(element,index)}),`
<request>
  <user_goal>${userPrompt}</user_goal>
  <url>${url}</url>
  <selected_elements>
    ${detailedContext.trim()}
  </selected_elements>
  ${pluginContext}
</request>`.trim()}const AppContext=pluginUi_jsxRuntime.K(null),STORAGE_KEY="stgws:companion";function loadStateFromStorage(){try{const stored=sessionStorage.getItem(STORAGE_KEY);return stored?JSON.parse(stored):{}}catch(error){return console.error("Failed to load state from storage:",error),{}}}function saveStateToStorage(state){try{sessionStorage.setItem(STORAGE_KEY,JSON.stringify(state))}catch(error){console.error("Failed to save state to storage:",error)}}function AppStateProvider({children}){const[state,setState]=panel.d(()=>{const storedState=loadStateFromStorage();return{appBlockRequestList:[],appUnblockRequestList:[],lastBlockRequestNumber:0,lastUnblockRequestNumber:0,isMainAppBlocked:!1,toolbarBoxRef:pluginUi_jsxRuntime.b(),minimized:storedState.minimized??!1,requestMainAppBlock:()=>0,requestMainAppUnblock:()=>0,discardMainAppBlock:()=>{},discardMainAppUnblock:()=>{},setToolbarBoxRef:()=>{},unsetToolbarBoxRef:()=>{},minimize:()=>{},expand:()=>{}}});panel.y(()=>{saveStateToStorage({minimized:state.minimized})},[state.minimized]);const requestMainAppBlock=panel.q(()=>{let newHandleValue=0;return setState(prev=>(newHandleValue=prev.lastBlockRequestNumber+1,{...prev,appBlockRequestList:[...prev.appBlockRequestList,newHandleValue],lastBlockRequestNumber:newHandleValue,isMainAppBlocked:prev.appUnblockRequestList.length===0})),newHandleValue},[]),requestMainAppUnblock=panel.q(()=>{let newHandleValue=0;return setState(prev=>(newHandleValue=prev.lastUnblockRequestNumber+1,{...prev,appUnblockRequestList:[...prev.appUnblockRequestList,newHandleValue],lastUnblockRequestNumber:newHandleValue,isMainAppBlocked:!1})),newHandleValue},[]),discardMainAppBlock=panel.q(handle=>{setState(prev=>{const newBlockRequestList=prev.appBlockRequestList.filter(h2=>h2!==handle);return{...prev,appBlockRequestList:newBlockRequestList,isMainAppBlocked:newBlockRequestList.length>0&&prev.appUnblockRequestList.length===0}})},[]),discardMainAppUnblock=panel.q(handle=>{setState(prev=>{const newUnblockRequestList=prev.appUnblockRequestList.filter(h2=>h2!==handle);return{...prev,appUnblockRequestList:newUnblockRequestList,isMainAppBlocked:prev.appBlockRequestList.length>0&&newUnblockRequestList.length===0}})},[]),setToolbarBoxRef=panel.q(ref=>{setState(prev=>({...prev,toolbarBoxRef:ref}))},[]),unsetToolbarBoxRef=panel.q(()=>{setState(prev=>({...prev,toolbarBoxRef:pluginUi_jsxRuntime.b()}))},[]),minimize=panel.q(()=>{setState(prev=>({...prev,minimized:!0}))},[]),expand=panel.q(()=>{setState(prev=>({...prev,minimized:!1}))},[]),value={requestMainAppBlock,requestMainAppUnblock,discardMainAppBlock,discardMainAppUnblock,isMainAppBlocked:state.isMainAppBlocked,toolbarBoxRef:state.toolbarBoxRef,setToolbarBoxRef,unsetToolbarBoxRef,minimized:state.minimized,minimize,expand};return pluginUi_jsxRuntime.u(AppContext.Provider,{value,children})}function useAppState(){const context=panel.x(AppContext);if(!context)throw new Error("useAppState must be used within an AppStateProvider");return context}const ChatContext=pluginUi_jsxRuntime.K({chats:[],currentChatId:null,createChat:()=>"",deleteChat:()=>{},setCurrentChat:()=>{},setChatInput:()=>{},addChatDomContext:()=>{},removeChatDomContext:()=>{},addMessage:()=>{},chatAreaState:"hidden",setChatAreaState:()=>{},isPromptCreationActive:!1,startPromptCreation:()=>{},stopPromptCreation:()=>{},promptState:"idle",resetPromptState:()=>{}}),ChatStateProvider=({children})=>{const[chats,setChats]=panel.d([{id:"new_chat",messages:[],title:"New chat",inputValue:"",domContextElements:[]}]),[currentChatId,setCurrentChatId]=panel.d("new_chat"),[chatAreaState,internalSetChatAreaState]=panel.d("hidden"),[isPromptCreationMode,setIsPromptCreationMode]=panel.d(!1),[promptState,setPromptState]=panel.d("idle"),resetPromptState=panel.q(()=>{setPromptState("idle")},[]),{minimized}=useAppState(),{selectedSession,setShouldPromptWindowSelection,windows}=panel.useVSCode();panel.y(()=>{minimized&&(setIsPromptCreationMode(!1),internalSetChatAreaState("hidden"))},[minimized]);const{bridge}=panel.useSRPCBridge(),createChat=panel.q(()=>{const newChatId=panel.generateId(),newChat={id:newChatId,title:null,messages:[],inputValue:"",domContextElements:[]};return setChats(prev=>[...prev,newChat]),setCurrentChatId(newChatId),newChatId},[]),deleteChat=panel.q(chatId=>{setChats(prev=>{const filteredChats=prev.filter(chat=>chat.id!==chatId);return filteredChats.length===0?[{id:"new_chat",messages:[],title:"New chat",inputValue:"",domContextElements:[]}]:filteredChats}),currentChatId===chatId&&setChats(prev=>(setCurrentChatId(prev[0].id),prev))},[currentChatId]),setCurrentChat=panel.q(chatId=>{setCurrentChatId(chatId)},[]),setChatInput=panel.q((chatId,value2)=>{setChats(prev=>prev.map(chat=>chat.id===chatId?{...chat,inputValue:value2}:chat))},[]),{plugins}=panel.usePlugins(),startPromptCreation=panel.q(()=>{setIsPromptCreationMode(!0),chatAreaState==="hidden"&&internalSetChatAreaState("compact"),plugins.forEach(plugin=>{var _a;(_a=plugin.onPromptingStart)==null||_a.call(plugin)})},[chatAreaState]),stopPromptCreation=panel.q(()=>{setIsPromptCreationMode(!1),setPromptState("idle"),setChats(prev=>prev.map(chat=>chat.id===currentChatId?{...chat,domContextElements:[]}:chat)),chatAreaState==="compact"&&internalSetChatAreaState("hidden"),plugins.forEach(plugin=>{var _a;(_a=plugin.onPromptingAbort)==null||_a.call(plugin)})},[currentChatId,chatAreaState]),setChatAreaState=panel.q(state=>{internalSetChatAreaState(state),state==="hidden"&&stopPromptCreation()},[internalSetChatAreaState,stopPromptCreation]),addChatDomContext=panel.q((chatId,element)=>{const pluginsWithContextGetters=plugins.filter(plugin=>plugin.onContextElementSelect);setChats(prev=>prev.map(chat=>chat.id===chatId?{...chat,domContextElements:[...chat.domContextElements,{element,pluginContext:pluginsWithContextGetters.map(plugin=>{var _a;return{pluginName:plugin.pluginName,context:(_a=plugin.onContextElementSelect)==null?void 0:_a.call(plugin,element)}})}]}:chat))},[plugins]),removeChatDomContext=panel.q((chatId,element)=>{setChats(prev=>prev.map(chat=>chat.id===chatId?{...chat,domContextElements:chat.domContextElements.filter(e2=>e2.element!==element)}:chat))},[]),addMessage=panel.q(async(chatId,content,pluginTriggered=!1)=>{if(!content.trim()||promptState==="loading")return;const chat=chats.find(chat2=>chat2.id===chatId);setPromptState("loading");const pluginContextSnippets=[],pluginProcessingPromises=plugins.map(async plugin=>{var _a;const userMessagePayload={id:panel.generateId(),text:content,contextElements:(chat==null?void 0:chat.domContextElements.map(el=>el.element))||[],sentByPlugin:pluginTriggered},handlerResult=await((_a=plugin.onPromptSend)==null?void 0:_a.call(plugin,userMessagePayload));if(!handlerResult||!handlerResult.contextSnippets||handlerResult.contextSnippets.length===0)return null;const snippetPromises=handlerResult.contextSnippets.map(async snippet=>{const resolvedContent=typeof snippet.content=="string"?snippet.content:await snippet.content();return{promptContextName:snippet.promptContextName,content:resolvedContent}}),resolvedSnippets=await Promise.all(snippetPromises);return resolvedSnippets.length>0?{pluginName:plugin.pluginName,contextSnippets:resolvedSnippets}:null});(await Promise.all(pluginProcessingPromises)).forEach(pluginCtx=>{pluginCtx&&pluginContextSnippets.push(pluginCtx)});const prompt=createPrompt(chat==null?void 0:chat.domContextElements.map(e2=>e2.element),content,window.location.href,pluginContextSnippets),newMessage={id:panel.generateId(),content:content.trim(),sender:"user",type:"regular",timestamp:new Date};async function triggerAgentPrompt(){if(bridge)try{const result=await bridge.call.triggerAgentPrompt({prompt,sessionId:selectedSession==null?void 0:selectedSession.sessionId},{onUpdate:update=>{}});result.result.success?(setTimeout(()=>{setPromptState("success")},1e3),setChats(prev=>prev.map(chat2=>chat2.id===chatId?{...chat2,inputValue:""}:chat2))):(result.result.errorCode&&result.result.errorCode==="session_mismatch"&&setShouldPromptWindowSelection(!0),setPromptState("error"),setTimeout(()=>{setPromptState("idle"),setIsPromptCreationMode(!1),setChats(prev=>prev.map(chat2=>chat2.id===chatId?{...chat2,inputValue:""}:chat2))},300))}catch{setPromptState("error"),setTimeout(()=>{setPromptState("idle"),setIsPromptCreationMode(!1),setChats(prev=>prev.map(chat2=>chat2.id===chatId?{...chat2,inputValue:""}:chat2))},300)}else setShouldPromptWindowSelection(!0),setPromptState("error"),setTimeout(()=>{setPromptState("idle"),setIsPromptCreationMode(!1),setChats(prev=>prev.map(chat2=>chat2.id===chatId?{...chat2,inputValue:""}:chat2))},300)}triggerAgentPrompt(),chatAreaState==="hidden"&&internalSetChatAreaState("compact"),setChats(prev=>prev.map(chat2=>chat2.id===chatId?{...chat2,messages:[...chat2.messages,newMessage],inputValue:content.trim(),domContextElements:[]}:chat2))},[chatAreaState,bridge,chats,setIsPromptCreationMode,internalSetChatAreaState,selectedSession,promptState,setPromptState,plugins]),value={chats,currentChatId,createChat,deleteChat,setCurrentChat,setChatInput,addMessage,chatAreaState,setChatAreaState,isPromptCreationActive:isPromptCreationMode,startPromptCreation,stopPromptCreation,addChatDomContext,removeChatDomContext,promptState,resetPromptState};return pluginUi_jsxRuntime.u(ChatContext.Provider,{value,children})};function useChatState(){const context=panel.x(ChatContext);if(!context)throw new Error("useChatState must be used within a ChatStateProvider");return context}function ContextProviders({children,config}){return pluginUi_jsxRuntime.u(panel.ConfigProvider,{config,children:pluginUi_jsxRuntime.u(panel.VSCodeProvider,{children:pluginUi_jsxRuntime.u(panel.SRPCBridgeProvider,{children:pluginUi_jsxRuntime.u(panel.PluginProvider,{children:pluginUi_jsxRuntime.u(ChatStateProvider,{children})})})})})}function useEventListener(eventName,handler,options,element=window){panel.y(()=>{if(!(typeof window>"u")&&element)return element.addEventListener(eventName,handler,options),()=>element.removeEventListener(eventName,handler,options)},[eventName,handler,element,options])}function HotkeyListener(){const{startPromptCreation,stopPromptCreation,isPromptCreationActive}=useChatState(),hotKeyHandlerMap=panel.T(()=>({[panel.HotkeyActions.CTRL_ALT_C]:()=>isPromptCreationActive?!1:(startPromptCreation(),!0),[panel.HotkeyActions.ESC]:()=>isPromptCreationActive?(stopPromptCreation(),!0):!1}),[startPromptCreation,stopPromptCreation,isPromptCreationActive]),hotKeyListener=panel.q(ev=>{for(const[action,definition]of Object.entries(panel.hotkeyActionDefinitions))if(definition.isEventMatching(ev)){hotKeyHandlerMap[action]()&&(ev.preventDefault(),ev.stopPropagation());break}},[hotKeyHandlerMap]);return useEventListener("keydown",hotKeyListener,{capture:!0}),null}const $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c=typeof document<"u"?panel.Rn.useLayoutEffect:()=>{};function $8ae05eaa5c114e9c$export$7f54fc3180508a52(fn){const ref=panel.A(null);return $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c(()=>{ref.current=fn},[fn]),panel.q((...args)=>{const f2=ref.current;return f2==null?void 0:f2(...args)},[])}const $431fbd86ca7dc216$export$b204af158042fbac=el=>{var _el_ownerDocument;return(_el_ownerDocument=el==null?void 0:el.ownerDocument)!==null&&_el_ownerDocument!==void 0?_el_ownerDocument:document},$431fbd86ca7dc216$export$f21a1ffae260145a=el=>el&&"window"in el&&el.window===el?el:$431fbd86ca7dc216$export$b204af158042fbac(el).defaultView||window;function $431fbd86ca7dc216$var$isNode(value){return value!==null&&typeof value=="object"&&"nodeType"in value&&typeof value.nodeType=="number"}function $431fbd86ca7dc216$export$af51f0f06c0f328a(node){return $431fbd86ca7dc216$var$isNode(node)&&node.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&"host"in node}let $f4e2df6bd15f8569$var$_shadowDOM=!1;function $f4e2df6bd15f8569$export$98658e8c59125e6a(){return $f4e2df6bd15f8569$var$_shadowDOM}function $d4ee10de306f2510$export$4282f70798064fe0(node,otherNode){if(!$f4e2df6bd15f8569$export$98658e8c59125e6a())return otherNode&&node?node.contains(otherNode):!1;if(!node||!otherNode)return!1;let currentNode=otherNode;for(;currentNode!==null;){if(currentNode===node)return!0;currentNode.tagName==="SLOT"&&currentNode.assignedSlot?currentNode=currentNode.assignedSlot.parentNode:$431fbd86ca7dc216$export$af51f0f06c0f328a(currentNode)?currentNode=currentNode.host:currentNode=currentNode.parentNode}return!1}const $d4ee10de306f2510$export$cd4e5573fbe2b576=(doc=document)=>{var _activeElement_shadowRoot;if(!$f4e2df6bd15f8569$export$98658e8c59125e6a())return doc.activeElement;let activeElement=doc.activeElement;for(;activeElement&&"shadowRoot"in activeElement&&(!((_activeElement_shadowRoot=activeElement.shadowRoot)===null||_activeElement_shadowRoot===void 0)&&_activeElement_shadowRoot.activeElement);)activeElement=activeElement.shadowRoot.activeElement;return activeElement};function $d4ee10de306f2510$export$e58f029f0fbfdb29(event){return $f4e2df6bd15f8569$export$98658e8c59125e6a()&&event.target.shadowRoot&&event.composedPath?event.composedPath()[0]:event.target}function $c87311424ea30a05$var$testUserAgent(re){var _window_navigator_userAgentData;return typeof window>"u"||window.navigator==null?!1:((_window_navigator_userAgentData=window.navigator.userAgentData)===null||_window_navigator_userAgentData===void 0?void 0:_window_navigator_userAgentData.brands.some(brand=>re.test(brand.brand)))||re.test(window.navigator.userAgent)}function $c87311424ea30a05$var$testPlatform(re){var _window_navigator_userAgentData;return typeof window<"u"&&window.navigator!=null?re.test(((_window_navigator_userAgentData=window.navigator.userAgentData)===null||_window_navigator_userAgentData===void 0?void 0:_window_navigator_userAgentData.platform)||window.navigator.platform):!1}function $c87311424ea30a05$var$cached(fn){if(process.env.NODE_ENV==="test")return fn;let res=null;return()=>(res==null&&(res=fn()),res)}const $c87311424ea30a05$export$9ac100e40613ea10=$c87311424ea30a05$var$cached(function(){return $c87311424ea30a05$var$testPlatform(/^Mac/i)}),$c87311424ea30a05$export$186c6964ca17d99=$c87311424ea30a05$var$cached(function(){return $c87311424ea30a05$var$testPlatform(/^iPhone/i)}),$c87311424ea30a05$export$7bef049ce92e4224=$c87311424ea30a05$var$cached(function(){return $c87311424ea30a05$var$testPlatform(/^iPad/i)||$c87311424ea30a05$export$9ac100e40613ea10()&&navigator.maxTouchPoints>1}),$c87311424ea30a05$export$fedb369cb70207f1=$c87311424ea30a05$var$cached(function(){return $c87311424ea30a05$export$186c6964ca17d99()||$c87311424ea30a05$export$7bef049ce92e4224()});$c87311424ea30a05$var$cached(function(){return $c87311424ea30a05$export$9ac100e40613ea10()||$c87311424ea30a05$export$fedb369cb70207f1()});$c87311424ea30a05$var$cached(function(){return $c87311424ea30a05$var$testUserAgent(/AppleWebKit/i)&&!$c87311424ea30a05$export$6446a186d09e379e()});const $c87311424ea30a05$export$6446a186d09e379e=$c87311424ea30a05$var$cached(function(){return $c87311424ea30a05$var$testUserAgent(/Chrome/i)}),$c87311424ea30a05$export$a11b0059900ceec8=$c87311424ea30a05$var$cached(function(){return $c87311424ea30a05$var$testUserAgent(/Android/i)});$c87311424ea30a05$var$cached(function(){return $c87311424ea30a05$var$testUserAgent(/Firefox/i)});function $03deb23ff14920c4$export$4eaf04e54aa8eed6(){let globalListeners=panel.A(new Map),addGlobalListener=panel.q((eventTarget,type,listener,options)=>{let fn=options!=null&&options.once?(...args)=>{globalListeners.current.delete(listener),listener(...args)}:listener;globalListeners.current.set(listener,{type,eventTarget,fn,options}),eventTarget.addEventListener(type,fn,options)},[]),removeGlobalListener=panel.q((eventTarget,type,listener,options)=>{var _globalListeners_current_get;let fn=((_globalListeners_current_get=globalListeners.current.get(listener))===null||_globalListeners_current_get===void 0?void 0:_globalListeners_current_get.fn)||listener;eventTarget.removeEventListener(type,fn,options),globalListeners.current.delete(listener)},[]),removeAllGlobalListeners=panel.q(()=>{globalListeners.current.forEach((value,key)=>{removeGlobalListener(value.eventTarget,value.type,key,value.options)})},[removeGlobalListener]);return panel.y(()=>removeAllGlobalListeners,[removeAllGlobalListeners]),{addGlobalListener,removeGlobalListener,removeAllGlobalListeners}}function $6a7db85432448f7f$export$60278871457622de(event){return event.mozInputSource===0&&event.isTrusted?!0:$c87311424ea30a05$export$a11b0059900ceec8()&&event.pointerType?event.type==="click"&&event.buttons===1:event.detail===0&&!event.pointerType}function $8a9cb279dc87e130$export$525bc4921d56d4a(nativeEvent){let event=nativeEvent;return event.nativeEvent=nativeEvent,event.isDefaultPrevented=()=>event.defaultPrevented,event.isPropagationStopped=()=>event.cancelBubble,event.persist=()=>{},event}function $8a9cb279dc87e130$export$c2b7abe5d61ec696(event,target){Object.defineProperty(event,"target",{value:target}),Object.defineProperty(event,"currentTarget",{value:target})}function $8a9cb279dc87e130$export$715c682d09d639cc(onBlur){let stateRef=panel.A({isFocused:!1,observer:null});$f0a04ccd8dbdd83b$export$e5c5a5f917a5871c(()=>{const state=stateRef.current;return()=>{state.observer&&(state.observer.disconnect(),state.observer=null)}},[]);let dispatchBlur=$8ae05eaa5c114e9c$export$7f54fc3180508a52(e2=>{onBlur==null||onBlur(e2)});return panel.q(e2=>{if(e2.target instanceof HTMLButtonElement||e2.target instanceof HTMLInputElement||e2.target instanceof HTMLTextAreaElement||e2.target instanceof HTMLSelectElement){stateRef.current.isFocused=!0;let target=e2.target,onBlurHandler=e3=>{if(stateRef.current.isFocused=!1,target.disabled){let event=$8a9cb279dc87e130$export$525bc4921d56d4a(e3);dispatchBlur(event)}stateRef.current.observer&&(stateRef.current.observer.disconnect(),stateRef.current.observer=null)};target.addEventListener("focusout",onBlurHandler,{once:!0}),stateRef.current.observer=new MutationObserver(()=>{if(stateRef.current.isFocused&&target.disabled){var _stateRef_current_observer;(_stateRef_current_observer=stateRef.current.observer)===null||_stateRef_current_observer===void 0||_stateRef_current_observer.disconnect();let relatedTargetEl=target===document.activeElement?null:document.activeElement;target.dispatchEvent(new FocusEvent("blur",{relatedTarget:relatedTargetEl})),target.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:relatedTargetEl}))}}),stateRef.current.observer.observe(target,{attributes:!0,attributeFilter:["disabled"]})}},[dispatchBlur])}let $8a9cb279dc87e130$export$fda7da73ab5d4c48=!1,$507fabe10e71c6fb$var$currentModality=null,$507fabe10e71c6fb$var$changeHandlers=new Set,$507fabe10e71c6fb$export$d90243b58daecda7=new Map,$507fabe10e71c6fb$var$hasEventBeforeFocus=!1,$507fabe10e71c6fb$var$hasBlurredWindowRecently=!1;const $507fabe10e71c6fb$var$FOCUS_VISIBLE_INPUT_KEYS={Tab:!0,Escape:!0};function $507fabe10e71c6fb$var$triggerChangeHandlers(modality,e2){for(let handler of $507fabe10e71c6fb$var$changeHandlers)handler(modality,e2)}function $507fabe10e71c6fb$var$isValidKey(e2){return!(e2.metaKey||!$c87311424ea30a05$export$9ac100e40613ea10()&&e2.altKey||e2.ctrlKey||e2.key==="Control"||e2.key==="Shift"||e2.key==="Meta")}function $507fabe10e71c6fb$var$handleKeyboardEvent(e2){$507fabe10e71c6fb$var$hasEventBeforeFocus=!0,$507fabe10e71c6fb$var$isValidKey(e2)&&($507fabe10e71c6fb$var$currentModality="keyboard",$507fabe10e71c6fb$var$triggerChangeHandlers("keyboard",e2))}function $507fabe10e71c6fb$var$handlePointerEvent(e2){$507fabe10e71c6fb$var$currentModality="pointer",(e2.type==="mousedown"||e2.type==="pointerdown")&&($507fabe10e71c6fb$var$hasEventBeforeFocus=!0,$507fabe10e71c6fb$var$triggerChangeHandlers("pointer",e2))}function $507fabe10e71c6fb$var$handleClickEvent(e2){$6a7db85432448f7f$export$60278871457622de(e2)&&($507fabe10e71c6fb$var$hasEventBeforeFocus=!0,$507fabe10e71c6fb$var$currentModality="virtual")}function $507fabe10e71c6fb$var$handleFocusEvent(e2){e2.target===window||e2.target===document||$8a9cb279dc87e130$export$fda7da73ab5d4c48||!e2.isTrusted||(!$507fabe10e71c6fb$var$hasEventBeforeFocus&&!$507fabe10e71c6fb$var$hasBlurredWindowRecently&&($507fabe10e71c6fb$var$currentModality="virtual",$507fabe10e71c6fb$var$triggerChangeHandlers("virtual",e2)),$507fabe10e71c6fb$var$hasEventBeforeFocus=!1,$507fabe10e71c6fb$var$hasBlurredWindowRecently=!1)}function $507fabe10e71c6fb$var$handleWindowBlur(){$507fabe10e71c6fb$var$hasEventBeforeFocus=!1,$507fabe10e71c6fb$var$hasBlurredWindowRecently=!0}function $507fabe10e71c6fb$var$setupGlobalFocusEvents(element){if(typeof window>"u"||$507fabe10e71c6fb$export$d90243b58daecda7.get($431fbd86ca7dc216$export$f21a1ffae260145a(element)))return;const windowObject=$431fbd86ca7dc216$export$f21a1ffae260145a(element),documentObject=$431fbd86ca7dc216$export$b204af158042fbac(element);let focus=windowObject.HTMLElement.prototype.focus;windowObject.HTMLElement.prototype.focus=function(){$507fabe10e71c6fb$var$hasEventBeforeFocus=!0,focus.apply(this,arguments)},documentObject.addEventListener("keydown",$507fabe10e71c6fb$var$handleKeyboardEvent,!0),documentObject.addEventListener("keyup",$507fabe10e71c6fb$var$handleKeyboardEvent,!0),documentObject.addEventListener("click",$507fabe10e71c6fb$var$handleClickEvent,!0),windowObject.addEventListener("focus",$507fabe10e71c6fb$var$handleFocusEvent,!0),windowObject.addEventListener("blur",$507fabe10e71c6fb$var$handleWindowBlur,!1),typeof PointerEvent<"u"?(documentObject.addEventListener("pointerdown",$507fabe10e71c6fb$var$handlePointerEvent,!0),documentObject.addEventListener("pointermove",$507fabe10e71c6fb$var$handlePointerEvent,!0),documentObject.addEventListener("pointerup",$507fabe10e71c6fb$var$handlePointerEvent,!0)):process.env.NODE_ENV==="test"&&(documentObject.addEventListener("mousedown",$507fabe10e71c6fb$var$handlePointerEvent,!0),documentObject.addEventListener("mousemove",$507fabe10e71c6fb$var$handlePointerEvent,!0),documentObject.addEventListener("mouseup",$507fabe10e71c6fb$var$handlePointerEvent,!0)),windowObject.addEventListener("beforeunload",()=>{$507fabe10e71c6fb$var$tearDownWindowFocusTracking(element)},{once:!0}),$507fabe10e71c6fb$export$d90243b58daecda7.set(windowObject,{focus})}const $507fabe10e71c6fb$var$tearDownWindowFocusTracking=(element,loadListener)=>{const windowObject=$431fbd86ca7dc216$export$f21a1ffae260145a(element),documentObject=$431fbd86ca7dc216$export$b204af158042fbac(element);loadListener&&documentObject.removeEventListener("DOMContentLoaded",loadListener),$507fabe10e71c6fb$export$d90243b58daecda7.has(windowObject)&&(windowObject.HTMLElement.prototype.focus=$507fabe10e71c6fb$export$d90243b58daecda7.get(windowObject).focus,documentObject.removeEventListener("keydown",$507fabe10e71c6fb$var$handleKeyboardEvent,!0),documentObject.removeEventListener("keyup",$507fabe10e71c6fb$var$handleKeyboardEvent,!0),documentObject.removeEventListener("click",$507fabe10e71c6fb$var$handleClickEvent,!0),windowObject.removeEventListener("focus",$507fabe10e71c6fb$var$handleFocusEvent,!0),windowObject.removeEventListener("blur",$507fabe10e71c6fb$var$handleWindowBlur,!1),typeof PointerEvent<"u"?(documentObject.removeEventListener("pointerdown",$507fabe10e71c6fb$var$handlePointerEvent,!0),documentObject.removeEventListener("pointermove",$507fabe10e71c6fb$var$handlePointerEvent,!0),documentObject.removeEventListener("pointerup",$507fabe10e71c6fb$var$handlePointerEvent,!0)):process.env.NODE_ENV==="test"&&(documentObject.removeEventListener("mousedown",$507fabe10e71c6fb$var$handlePointerEvent,!0),documentObject.removeEventListener("mousemove",$507fabe10e71c6fb$var$handlePointerEvent,!0),documentObject.removeEventListener("mouseup",$507fabe10e71c6fb$var$handlePointerEvent,!0)),$507fabe10e71c6fb$export$d90243b58daecda7.delete(windowObject))};function $507fabe10e71c6fb$export$2f1888112f558a7d(element){const documentObject=$431fbd86ca7dc216$export$b204af158042fbac(element);let loadListener;return documentObject.readyState!=="loading"?$507fabe10e71c6fb$var$setupGlobalFocusEvents(element):(loadListener=()=>{$507fabe10e71c6fb$var$setupGlobalFocusEvents(element)},documentObject.addEventListener("DOMContentLoaded",loadListener)),()=>$507fabe10e71c6fb$var$tearDownWindowFocusTracking(element,loadListener)}typeof document<"u"&&$507fabe10e71c6fb$export$2f1888112f558a7d();function $507fabe10e71c6fb$export$b9b3dfddab17db27(){return $507fabe10e71c6fb$var$currentModality!=="pointer"}const $507fabe10e71c6fb$var$nonTextInputTypes=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function $507fabe10e71c6fb$var$isKeyboardFocusEvent(isTextInput,modality,e2){let document1=$431fbd86ca7dc216$export$b204af158042fbac(e2==null?void 0:e2.target);const IHTMLInputElement=typeof window<"u"?$431fbd86ca7dc216$export$f21a1ffae260145a(e2==null?void 0:e2.target).HTMLInputElement:HTMLInputElement,IHTMLTextAreaElement=typeof window<"u"?$431fbd86ca7dc216$export$f21a1ffae260145a(e2==null?void 0:e2.target).HTMLTextAreaElement:HTMLTextAreaElement,IHTMLElement=typeof window<"u"?$431fbd86ca7dc216$export$f21a1ffae260145a(e2==null?void 0:e2.target).HTMLElement:HTMLElement,IKeyboardEvent=typeof window<"u"?$431fbd86ca7dc216$export$f21a1ffae260145a(e2==null?void 0:e2.target).KeyboardEvent:KeyboardEvent;return isTextInput=isTextInput||document1.activeElement instanceof IHTMLInputElement&&!$507fabe10e71c6fb$var$nonTextInputTypes.has(document1.activeElement.type)||document1.activeElement instanceof IHTMLTextAreaElement||document1.activeElement instanceof IHTMLElement&&document1.activeElement.isContentEditable,!(isTextInput&&modality==="keyboard"&&e2 instanceof IKeyboardEvent&&!$507fabe10e71c6fb$var$FOCUS_VISIBLE_INPUT_KEYS[e2.key])}function $507fabe10e71c6fb$export$ec71b4b83ac08ec3(fn,deps,opts){$507fabe10e71c6fb$var$setupGlobalFocusEvents(),panel.y(()=>{let handler=(modality,e2)=>{$507fabe10e71c6fb$var$isKeyboardFocusEvent(!!(opts!=null&&opts.isTextInput),modality,e2)&&fn($507fabe10e71c6fb$export$b9b3dfddab17db27())};return $507fabe10e71c6fb$var$changeHandlers.add(handler),()=>{$507fabe10e71c6fb$var$changeHandlers.delete(handler)}},deps)}function $a1ea59d68270f0dd$export$f8168d8dd8fd66e6(props){let{isDisabled,onFocus:onFocusProp,onBlur:onBlurProp,onFocusChange}=props;const onBlur=panel.q(e2=>{if(e2.target===e2.currentTarget)return onBlurProp&&onBlurProp(e2),onFocusChange&&onFocusChange(!1),!0},[onBlurProp,onFocusChange]),onSyntheticFocus=$8a9cb279dc87e130$export$715c682d09d639cc(onBlur),onFocus=panel.q(e2=>{const ownerDocument=$431fbd86ca7dc216$export$b204af158042fbac(e2.target),activeElement=ownerDocument?$d4ee10de306f2510$export$cd4e5573fbe2b576(ownerDocument):$d4ee10de306f2510$export$cd4e5573fbe2b576();e2.target===e2.currentTarget&&activeElement===$d4ee10de306f2510$export$e58f029f0fbfdb29(e2.nativeEvent)&&(onFocusProp&&onFocusProp(e2),onFocusChange&&onFocusChange(!0),onSyntheticFocus(e2))},[onFocusChange,onFocusProp,onSyntheticFocus]);return{focusProps:{onFocus:!isDisabled&&(onFocusProp||onFocusChange||onBlurProp)?onFocus:void 0,onBlur:!isDisabled&&(onBlurProp||onFocusChange)?onBlur:void 0}}}function $9ab94262bd0047c7$export$420e68273165f4ec(props){let{isDisabled,onBlurWithin,onFocusWithin,onFocusWithinChange}=props,state=panel.A({isFocusWithin:!1}),{addGlobalListener,removeAllGlobalListeners}=$03deb23ff14920c4$export$4eaf04e54aa8eed6(),onBlur=panel.q(e2=>{e2.currentTarget.contains(e2.target)&&state.current.isFocusWithin&&!e2.currentTarget.contains(e2.relatedTarget)&&(state.current.isFocusWithin=!1,removeAllGlobalListeners(),onBlurWithin&&onBlurWithin(e2),onFocusWithinChange&&onFocusWithinChange(!1))},[onBlurWithin,onFocusWithinChange,state,removeAllGlobalListeners]),onSyntheticFocus=$8a9cb279dc87e130$export$715c682d09d639cc(onBlur),onFocus=panel.q(e2=>{if(!e2.currentTarget.contains(e2.target))return;const ownerDocument=$431fbd86ca7dc216$export$b204af158042fbac(e2.target),activeElement=$d4ee10de306f2510$export$cd4e5573fbe2b576(ownerDocument);if(!state.current.isFocusWithin&&activeElement===$d4ee10de306f2510$export$e58f029f0fbfdb29(e2.nativeEvent)){onFocusWithin&&onFocusWithin(e2),onFocusWithinChange&&onFocusWithinChange(!0),state.current.isFocusWithin=!0,onSyntheticFocus(e2);let currentTarget=e2.currentTarget;addGlobalListener(ownerDocument,"focus",e3=>{if(state.current.isFocusWithin&&!$d4ee10de306f2510$export$4282f70798064fe0(currentTarget,e3.target)){let nativeEvent=new ownerDocument.defaultView.FocusEvent("blur",{relatedTarget:e3.target});$8a9cb279dc87e130$export$c2b7abe5d61ec696(nativeEvent,currentTarget);let event=$8a9cb279dc87e130$export$525bc4921d56d4a(nativeEvent);onBlur(event)}},{capture:!0})}},[onFocusWithin,onFocusWithinChange,onSyntheticFocus,addGlobalListener,onBlur]);return isDisabled?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus,onBlur}}}let $6179b936705e76d3$var$globalIgnoreEmulatedMouseEvents=!1,$6179b936705e76d3$var$hoverCount=0;function $6179b936705e76d3$var$setGlobalIgnoreEmulatedMouseEvents(){$6179b936705e76d3$var$globalIgnoreEmulatedMouseEvents=!0,setTimeout(()=>{$6179b936705e76d3$var$globalIgnoreEmulatedMouseEvents=!1},50)}function $6179b936705e76d3$var$handleGlobalPointerEvent(e2){e2.pointerType==="touch"&&$6179b936705e76d3$var$setGlobalIgnoreEmulatedMouseEvents()}function $6179b936705e76d3$var$setupGlobalTouchEvents(){if(!(typeof document>"u"))return typeof PointerEvent<"u"?document.addEventListener("pointerup",$6179b936705e76d3$var$handleGlobalPointerEvent):process.env.NODE_ENV==="test"&&document.addEventListener("touchend",$6179b936705e76d3$var$setGlobalIgnoreEmulatedMouseEvents),$6179b936705e76d3$var$hoverCount++,()=>{$6179b936705e76d3$var$hoverCount--,!($6179b936705e76d3$var$hoverCount>0)&&(typeof PointerEvent<"u"?document.removeEventListener("pointerup",$6179b936705e76d3$var$handleGlobalPointerEvent):process.env.NODE_ENV==="test"&&document.removeEventListener("touchend",$6179b936705e76d3$var$setGlobalIgnoreEmulatedMouseEvents))}}function $6179b936705e76d3$export$ae780daf29e6d456(props){let{onHoverStart,onHoverChange,onHoverEnd,isDisabled}=props,[isHovered,setHovered]=panel.d(!1),state=panel.A({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;panel.y($6179b936705e76d3$var$setupGlobalTouchEvents,[]);let{addGlobalListener,removeAllGlobalListeners}=$03deb23ff14920c4$export$4eaf04e54aa8eed6(),{hoverProps,triggerHoverEnd}=panel.T(()=>{let triggerHoverStart=(event,pointerType)=>{if(state.pointerType=pointerType,isDisabled||pointerType==="touch"||state.isHovered||!event.currentTarget.contains(event.target))return;state.isHovered=!0;let target=event.currentTarget;state.target=target,addGlobalListener($431fbd86ca7dc216$export$b204af158042fbac(event.target),"pointerover",e2=>{state.isHovered&&state.target&&!$d4ee10de306f2510$export$4282f70798064fe0(state.target,e2.target)&&triggerHoverEnd2(e2,e2.pointerType)},{capture:!0}),onHoverStart&&onHoverStart({type:"hoverstart",target,pointerType}),onHoverChange&&onHoverChange(!0),setHovered(!0)},triggerHoverEnd2=(event,pointerType)=>{let target=state.target;state.pointerType="",state.target=null,!(pointerType==="touch"||!state.isHovered||!target)&&(state.isHovered=!1,removeAllGlobalListeners(),onHoverEnd&&onHoverEnd({type:"hoverend",target,pointerType}),onHoverChange&&onHoverChange(!1),setHovered(!1))},hoverProps2={};return typeof PointerEvent<"u"?(hoverProps2.onPointerEnter=e2=>{$6179b936705e76d3$var$globalIgnoreEmulatedMouseEvents&&e2.pointerType==="mouse"||triggerHoverStart(e2,e2.pointerType)},hoverProps2.onPointerLeave=e2=>{!isDisabled&&e2.currentTarget.contains(e2.target)&&triggerHoverEnd2(e2,e2.pointerType)}):process.env.NODE_ENV==="test"&&(hoverProps2.onTouchStart=()=>{state.ignoreEmulatedMouseEvents=!0},hoverProps2.onMouseEnter=e2=>{!state.ignoreEmulatedMouseEvents&&!$6179b936705e76d3$var$globalIgnoreEmulatedMouseEvents&&triggerHoverStart(e2,"mouse"),state.ignoreEmulatedMouseEvents=!1},hoverProps2.onMouseLeave=e2=>{!isDisabled&&e2.currentTarget.contains(e2.target)&&triggerHoverEnd2(e2,"mouse")}),{hoverProps:hoverProps2,triggerHoverEnd:triggerHoverEnd2}},[onHoverStart,onHoverChange,onHoverEnd,isDisabled,state,addGlobalListener,removeAllGlobalListeners]);return panel.y(()=>{isDisabled&&triggerHoverEnd({currentTarget:state.target},state.pointerType)},[isDisabled]),{hoverProps,isHovered}}function $f7dceffc5ad7768b$export$4e328f61c538687f(props={}){let{autoFocus=!1,isTextInput,within}=props,state=panel.A({isFocused:!1,isFocusVisible:autoFocus||$507fabe10e71c6fb$export$b9b3dfddab17db27()}),[isFocused,setFocused]=panel.d(!1),[isFocusVisibleState,setFocusVisible]=panel.d(()=>state.current.isFocused&&state.current.isFocusVisible),updateState=panel.q(()=>setFocusVisible(state.current.isFocused&&state.current.isFocusVisible),[]),onFocusChange=panel.q(isFocused2=>{state.current.isFocused=isFocused2,setFocused(isFocused2),updateState()},[updateState]);$507fabe10e71c6fb$export$ec71b4b83ac08ec3(isFocusVisible=>{state.current.isFocusVisible=isFocusVisible,updateState()},[],{isTextInput});let{focusProps}=$a1ea59d68270f0dd$export$f8168d8dd8fd66e6({isDisabled:within,onFocusChange}),{focusWithinProps}=$9ab94262bd0047c7$export$420e68273165f4ec({isDisabled:!within,onFocusWithinChange:onFocusChange});return{isFocused,isFocusVisible:isFocusVisibleState,focusProps:within?focusWithinProps:focusProps}}var i=Object.defineProperty,d=(t2,e2,n2)=>e2 in t2?i(t2,e2,{enumerable:!0,configurable:!0,writable:!0,value:n2}):t2[e2]=n2,r=(t2,e2,n2)=>(d(t2,typeof e2!="symbol"?e2+"":e2,n2),n2);let o$3=class{constructor(){r(this,"current",this.detect()),r(this,"handoffState","pending"),r(this,"currentId",0)}set(e2){this.current!==e2&&(this.handoffState="pending",this.currentId=0,this.current=e2)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window>"u"||typeof document>"u"?"server":"client"}handoff(){this.handoffState==="pending"&&(this.handoffState="complete")}get isHandoffComplete(){return this.handoffState==="complete"}},s$1=new o$3;function o$2(n2){var e2,r2;return s$1.isServer?null:n2?"ownerDocument"in n2?n2.ownerDocument:"current"in n2?(r2=(e2=n2.current)==null?void 0:e2.ownerDocument)!=null?r2:document:null:document}function t$1(e2){typeof queueMicrotask=="function"?queueMicrotask(e2):Promise.resolve().then(e2).catch(o3=>setTimeout(()=>{throw o3}))}function o$1(){let n2=[],r2={addEventListener(e2,t2,s2,a2){return e2.addEventListener(t2,s2,a2),r2.add(()=>e2.removeEventListener(t2,s2,a2))},requestAnimationFrame(...e2){let t2=requestAnimationFrame(...e2);return r2.add(()=>cancelAnimationFrame(t2))},nextFrame(...e2){return r2.requestAnimationFrame(()=>r2.requestAnimationFrame(...e2))},setTimeout(...e2){let t2=setTimeout(...e2);return r2.add(()=>clearTimeout(t2))},microTask(...e2){let t2={current:!0};return t$1(()=>{t2.current&&e2[0]()}),r2.add(()=>{t2.current=!1})},style(e2,t2,s2){let a2=e2.style.getPropertyValue(t2);return Object.assign(e2.style,{[t2]:s2}),this.add(()=>{Object.assign(e2.style,{[t2]:a2})})},group(e2){let t2=o$1();return e2(t2),this.add(()=>t2.dispose())},add(e2){return n2.includes(e2)||n2.push(e2),()=>{let t2=n2.indexOf(e2);if(t2>=0)for(let s2 of n2.splice(t2,1))s2()}},dispose(){for(let e2 of n2.splice(0))e2()}};return r2}function p(){let[e2]=panel.d(o$1);return panel.y(()=>()=>e2.dispose(),[e2]),e2}let n=(e2,t2)=>{s$1.isServer?panel.y(e2,t2):panel._(e2,t2)};function s(e2){let r2=panel.A(e2);return n(()=>{r2.current=e2},[e2]),r2}let o2=function(t2){let e2=s(t2);return panel.Rn.useCallback((...r2)=>e2.current(...r2),[e2])};function E(e2){let t2=e2.width/2,n2=e2.height/2;return{top:e2.clientY-n2,right:e2.clientX+t2,bottom:e2.clientY+n2,left:e2.clientX-t2}}function P$2(e2,t2){return!(!e2||!t2||e2.right<t2.left||e2.left>t2.right||e2.bottom<t2.top||e2.top>t2.bottom)}function w({disabled:e2=!1}={}){let t2=panel.A(null),[n2,l]=panel.d(!1),r2=p(),o$12=o2(()=>{t2.current=null,l(!1),r2.dispose()}),f2=o2(s2=>{if(r2.dispose(),t2.current===null){t2.current=s2.currentTarget,l(!0);{let i2=o$2(s2.currentTarget);r2.addEventListener(i2,"pointerup",o$12,!1),r2.addEventListener(i2,"pointermove",c2=>{if(t2.current){let p2=E(c2);l(P$2(p2,t2.current.getBoundingClientRect()))}},!1),r2.addEventListener(i2,"pointercancel",o$12,!1)}}});return{pressed:n2,pressProps:e2?{}:{onPointerDown:f2,onPointerUp:o$12,onClick:o$12}}}let e$1=pluginUi_jsxRuntime.K(void 0);function a$1(){return panel.x(e$1)}function t(...r2){return Array.from(new Set(r2.flatMap(n2=>typeof n2=="string"?n2.split(" "):[]))).filter(Boolean).join(" ")}function u$2(r2,n2,...a2){if(r2 in n2){let e2=n2[r2];return typeof e2=="function"?e2(...a2):e2}let t2=new Error(`Tried to handle "${r2}" but there is no handler defined. Only defined handlers are: ${Object.keys(n2).map(e2=>`"${e2}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(t2,u$2),t2}var O=(a2=>(a2[a2.None=0]="None",a2[a2.RenderStrategy=1]="RenderStrategy",a2[a2.Static=2]="Static",a2))(O||{}),A=(e2=>(e2[e2.Unmount=0]="Unmount",e2[e2.Hidden=1]="Hidden",e2))(A||{});function L$1(){let n2=U$2();return panel.q(r2=>C$1({mergeRefs:n2,...r2}),[n2])}function C$1({ourProps:n2,theirProps:r2,slot:e2,defaultTag:a2,features:s2,visible:t2=!0,name:l,mergeRefs:i2}){i2=i2??$;let o3=P$1(r2,n2);if(t2)return F(o3,e2,a2,l,i2);let y2=s2??0;if(y2&2){let{static:f2=!1,...u2}=o3;if(f2)return F(u2,e2,a2,l,i2)}if(y2&1){let{unmount:f2=!0,...u2}=o3;return u$2(f2?0:1,{0(){return null},1(){return F({...u2,hidden:!0,style:{display:"none"}},e2,a2,l,i2)}})}return F(o3,e2,a2,l,i2)}function F(n2,r2={},e2,a2,s2){let{as:t$12=e2,children:l,refName:i2="ref",...o3}=h(n2,["unmount","static"]),y2=n2.ref!==void 0?{[i2]:n2.ref}:{},f2=typeof l=="function"?l(r2):l;"className"in o3&&o3.className&&typeof o3.className=="function"&&(o3.className=o3.className(r2)),o3["aria-labelledby"]&&o3["aria-labelledby"]===o3.id&&(o3["aria-labelledby"]=void 0);let u2={};if(r2){let d2=!1,p2=[];for(let[c2,T]of Object.entries(r2))typeof T=="boolean"&&(d2=!0),T===!0&&p2.push(c2.replace(/([A-Z])/g,g=>`-${g.toLowerCase()}`));if(d2){u2["data-headlessui-state"]=p2.join(" ");for(let c2 of p2)u2[`data-${c2}`]=""}}if(t$12===pluginUi_jsxRuntime.k&&(Object.keys(m(o3)).length>0||Object.keys(m(u2)).length>0))if(!panel.mn(f2)||Array.isArray(f2)&&f2.length>1){if(Object.keys(m(o3)).length>0)throw new Error(['Passing props on "Fragment"!',"",`The current component <${a2} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(m(o3)).concat(Object.keys(m(u2))).map(d2=>`  - ${d2}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(d2=>`  - ${d2}`).join(`
`)].join(`
`))}else{let d2=f2.props,p2=d2==null?void 0:d2.className,c2=typeof p2=="function"?(...R2)=>t(p2(...R2),o3.className):t(p2,o3.className),T=c2?{className:c2}:{},g=P$1(f2.props,m(h(o3,["ref"])));for(let R2 in u2)R2 in g&&delete u2[R2];return panel._n(f2,Object.assign({},g,u2,y2,{ref:s2(H$2(f2),y2.ref)},T))}return pluginUi_jsxRuntime._(t$12,Object.assign({},h(o3,["ref"]),t$12!==pluginUi_jsxRuntime.k&&y2,t$12!==pluginUi_jsxRuntime.k&&u2),f2)}function U$2(){let n2=panel.A([]),r2=panel.q(e2=>{for(let a2 of n2.current)a2!=null&&(typeof a2=="function"?a2(e2):a2.current=e2)},[]);return(...e2)=>{if(!e2.every(a2=>a2==null))return n2.current=e2,r2}}function $(...n2){return n2.every(r2=>r2==null)?void 0:r2=>{for(let e2 of n2)e2!=null&&(typeof e2=="function"?e2(r2):e2.current=r2)}}function P$1(...n2){if(n2.length===0)return{};if(n2.length===1)return n2[0];let r2={},e2={};for(let s2 of n2)for(let t2 in s2)t2.startsWith("on")&&typeof s2[t2]=="function"?(e2[t2]!=null||(e2[t2]=[]),e2[t2].push(s2[t2])):r2[t2]=s2[t2];if(r2.disabled||r2["aria-disabled"])for(let s2 in e2)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(s2)&&(e2[s2]=[t2=>{var l;return(l=t2==null?void 0:t2.preventDefault)==null?void 0:l.call(t2)}]);for(let s2 in e2)Object.assign(r2,{[s2](t2,...l){let i2=e2[s2];for(let o3 of i2){if((t2 instanceof Event||(t2==null?void 0:t2.nativeEvent)instanceof Event)&&t2.defaultPrevented)return;o3(t2,...l)}}});return r2}function _$1(...n2){if(n2.length===0)return{};if(n2.length===1)return n2[0];let r2={},e2={};for(let s2 of n2)for(let t2 in s2)t2.startsWith("on")&&typeof s2[t2]=="function"?(e2[t2]!=null||(e2[t2]=[]),e2[t2].push(s2[t2])):r2[t2]=s2[t2];for(let s2 in e2)Object.assign(r2,{[s2](...t2){let l=e2[s2];for(let i2 of l)i2==null||i2(...t2)}});return r2}function K(n2){var r2;return Object.assign(panel.D(n2),{displayName:(r2=n2.displayName)!=null?r2:n2.name})}function m(n2){let r2=Object.assign({},n2);for(let e2 in r2)r2[e2]===void 0&&delete r2[e2];return r2}function h(n2,r2=[]){let e2=Object.assign({},n2);for(let a2 of r2)a2 in e2&&delete e2[a2];return e2}function H$2(n2){return panel.Rn.version.split(".")[0]>="19"?n2.props.ref:n2.ref}let R="button";function v(a2,u2){var p2;let l=a$1(),{disabled:e2=l||!1,autoFocus:t2=!1,...o3}=a2,{isFocusVisible:r2,focusProps:i2}=$f7dceffc5ad7768b$export$4e328f61c538687f({autoFocus:t2}),{isHovered:s2,hoverProps:T}=$6179b936705e76d3$export$ae780daf29e6d456({isDisabled:e2}),{pressed:n2,pressProps:d2}=w({disabled:e2}),f2=_$1({ref:u2,type:(p2=o3.type)!=null?p2:"button",disabled:e2||void 0,autoFocus:t2},i2,T,d2),m2=panel.T(()=>({disabled:e2,hover:s2,focus:r2,active:n2,autofocus:t2}),[e2,s2,r2,n2,t2]);return L$1()({ourProps:f2,theirProps:o3,slot:m2,defaultTag:R,name:"Button"})}let H$1=K(v),e=pluginUi_jsxRuntime.K(void 0);function u$1(){return panel.x(e)}let u=Symbol();function y(...t2){let n2=panel.A(t2);panel.y(()=>{n2.current=t2},[t2]);let c2=o2(e2=>{for(let o3 of n2.current)o3!=null&&(typeof o3=="function"?o3(e2):o3.current=e2)});return t2.every(e2=>e2==null||(e2==null?void 0:e2[u]))?void 0:c2}let a=pluginUi_jsxRuntime.K(null);a.displayName="DescriptionContext";function f(){let r2=panel.x(a);if(r2===null){let e2=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e2,f),e2}return r2}function U$1(){var r2,e2;return(e2=(r2=panel.x(a))==null?void 0:r2.value)!=null?e2:void 0}let S="p";function C(r2,e2){let d2=panel.g(),t2=a$1(),{id:i2=`headlessui-description-${d2}`,...l}=r2,n$1=f(),s2=y(e2);n(()=>n$1.register(i2),[i2,n$1.register]);let o3=t2||!1,p2=panel.T(()=>({...n$1.slot,disabled:o3}),[n$1.slot,o3]),D={ref:s2,...n$1.props,id:i2};return L$1()({ourProps:D,theirProps:l,slot:p2,defaultTag:S,name:n$1.name||"Description"})}let _=K(C);Object.assign(_,{});let c=pluginUi_jsxRuntime.K(null);c.displayName="LabelContext";function P(){let r2=panel.x(c);if(r2===null){let l=new Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(l,P),l}return r2}function I(r2){var a2,e2,o3;let l=(e2=(a2=panel.x(c))==null?void 0:a2.value)!=null?e2:void 0;return((o3=void 0)!=null?o3:0)>0?[l,...r2].filter(Boolean).join(" "):l}let N="label";function G(r2,l){var y$1;let a2=panel.g(),e2=P(),o$12=u$1(),g=a$1(),{id:t2=`headlessui-label-${a2}`,htmlFor:s2=o$12??((y$1=e2.props)==null?void 0:y$1.htmlFor),passive:m2=!1,...i2}=r2,p2=y(l);n(()=>e2.register(t2),[t2,e2.register]);let u2=o2(L2=>{let b=L2.currentTarget;if(b instanceof HTMLLabelElement&&L2.preventDefault(),e2.props&&"onClick"in e2.props&&typeof e2.props.onClick=="function"&&e2.props.onClick(L2),b instanceof HTMLLabelElement){let n2=document.getElementById(b.htmlFor);if(n2){let E2=n2.getAttribute("disabled");if(E2==="true"||E2==="")return;let x=n2.getAttribute("aria-disabled");if(x==="true"||x==="")return;(n2 instanceof HTMLInputElement&&(n2.type==="radio"||n2.type==="checkbox")||n2.role==="radio"||n2.role==="checkbox"||n2.role==="switch")&&n2.click(),n2.focus({preventScroll:!0})}}}),d2=g||!1,C2=panel.T(()=>({...e2.slot,disabled:d2}),[e2.slot,d2]),f2={ref:p2,...e2.props,id:t2,htmlFor:s2,onClick:u2};return m2&&("onClick"in f2&&(delete f2.htmlFor,delete f2.onClick),"onClick"in i2&&delete i2.onClick),L$1()({ourProps:f2,theirProps:i2,slot:C2,defaultTag:s2?N:"div",name:e2.name||"Label"})}let U=K(G);Object.assign(U,{});let L="textarea";function H(s2,l){let i2=panel.g(),d2=u$1(),n2=a$1(),{id:p2=d2||`headlessui-textarea-${i2}`,disabled:e2=n2||!1,autoFocus:r2=!1,invalid:a2=!1,...T}=s2,f2=I(),m2=U$1(),{isFocused:o3,focusProps:u2}=$f7dceffc5ad7768b$export$4e328f61c538687f({autoFocus:r2}),{isHovered:t2,hoverProps:b}=$6179b936705e76d3$export$ae780daf29e6d456({isDisabled:e2}),y2=_$1({ref:l,id:p2,"aria-labelledby":f2,"aria-describedby":m2,"aria-invalid":a2?"true":void 0,disabled:e2||void 0,autoFocus:r2},u2,b),x=panel.T(()=>({disabled:e2,invalid:a2,hover:t2,focus:o3,autofocus:r2}),[e2,a2,t2,o3,r2]);return L$1()({ourProps:y2,theirProps:T,slot:x,defaultTag:L,name:"Textarea"})}let J=K(H);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const toKebabCase=string=>string.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),toCamelCase=string=>string.replace(/^([A-Z])|[\s-_]+(\w)/g,(match,p1,p2)=>p2?p2.toUpperCase():p1.toLowerCase()),toPascalCase=string=>{const camelCase=toCamelCase(string);return camelCase.charAt(0).toUpperCase()+camelCase.slice(1)},mergeClasses=(...classes)=>classes.filter((className,index,array)=>!!className&&className.trim()!==""&&array.indexOf(className)===index).join(" ").trim(),hasA11yProp=props=>{for(const prop in props)if(prop.startsWith("aria-")||prop==="role"||prop==="title")return!0};/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var defaultAttributes={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Icon=panel.D(({color="currentColor",size=24,strokeWidth=2,absoluteStrokeWidth,className="",children,iconNode,...rest},ref)=>pluginUi_jsxRuntime._("svg",{ref,...defaultAttributes,width:size,height:size,stroke:color,strokeWidth:absoluteStrokeWidth?Number(strokeWidth)*24/Number(size):strokeWidth,className:mergeClasses("lucide",className),...!children&&!hasA11yProp(rest)&&{"aria-hidden":"true"},...rest},[...iconNode.map(([tag,attrs])=>pluginUi_jsxRuntime._(tag,attrs)),...Array.isArray(children)?children:[children]]));/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const createLucideIcon=(iconName,iconNode)=>{const Component=panel.D(({className,...props},ref)=>pluginUi_jsxRuntime._(Icon,{ref,iconNode,className:mergeClasses(`lucide-${toKebabCase(toPascalCase(iconName))}`,`lucide-${iconName}`,className),...props}));return Component.displayName=toPascalCase(iconName),Component};/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const __iconNode$9=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],ChevronDown=createLucideIcon("chevron-down",__iconNode$9);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const __iconNode$8=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],ChevronUp=createLucideIcon("chevron-up",__iconNode$8);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const __iconNode$7=[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]],MessageCircle=createLucideIcon("message-circle",__iconNode$7);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const __iconNode$6=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],Plus=createLucideIcon("plus",__iconNode$6);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const __iconNode$5=[["path",{d:"M15.39 4.39a1 1 0 0 0 1.68-.474 2.5 2.5 0 1 1 3.014 3.015 1 1 0 0 0-.474 1.68l1.683 1.682a2.414 2.414 0 0 1 0 3.414L19.61 15.39a1 1 0 0 1-1.68-.474 2.5 2.5 0 1 0-3.014 3.015 1 1 0 0 1 .474 1.68l-1.683 1.682a2.414 2.414 0 0 1-3.414 0L8.61 19.61a1 1 0 0 0-1.68.474 2.5 2.5 0 1 1-3.014-3.015 1 1 0 0 0 .474-1.68l-1.683-1.682a2.414 2.414 0 0 1 0-3.414L4.39 8.61a1 1 0 0 1 1.68.474 2.5 2.5 0 1 0 3.014-3.015 1 1 0 0 1-.474-1.68l1.683-1.682a2.414 2.414 0 0 1 3.414 0z",key:"w46dr5"}]],Puzzle=createLucideIcon("puzzle",__iconNode$5);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const __iconNode$4=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],RefreshCw=createLucideIcon("refresh-cw",__iconNode$4);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const __iconNode$3=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],Send=createLucideIcon("send",__iconNode$3);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const __iconNode$2=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Settings=createLucideIcon("settings",__iconNode$2);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const __iconNode$1=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],Trash2=createLucideIcon("trash-2",__iconNode$1);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const __iconNode=[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],WifiOff=createLucideIcon("wifi-off",__iconNode),BROWSER_ALIASES_MAP={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},BROWSER_MAP={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},PLATFORMS_MAP={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},OS_MAP={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},ENGINE_MAP={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"};class Utils{static getFirstMatch(regexp,ua){const match=ua.match(regexp);return match&&match.length>0&&match[1]||""}static getSecondMatch(regexp,ua){const match=ua.match(regexp);return match&&match.length>1&&match[2]||""}static matchAndReturnConst(regexp,ua,_const){if(regexp.test(ua))return _const}static getWindowsVersionName(version){switch(version){case"NT":return"NT";case"XP":return"XP";case"NT 5.0":return"2000";case"NT 5.1":return"XP";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}}static getMacOSVersionName(version){const v2=version.split(".").splice(0,2).map(s2=>parseInt(s2,10)||0);if(v2.push(0),v2[0]===10)switch(v2[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}}static getAndroidVersionName(version){const v2=version.split(".").splice(0,2).map(s2=>parseInt(s2,10)||0);if(v2.push(0),!(v2[0]===1&&v2[1]<5)){if(v2[0]===1&&v2[1]<6)return"Cupcake";if(v2[0]===1&&v2[1]>=6)return"Donut";if(v2[0]===2&&v2[1]<2)return"Eclair";if(v2[0]===2&&v2[1]===2)return"Froyo";if(v2[0]===2&&v2[1]>2)return"Gingerbread";if(v2[0]===3)return"Honeycomb";if(v2[0]===4&&v2[1]<1)return"Ice Cream Sandwich";if(v2[0]===4&&v2[1]<4)return"Jelly Bean";if(v2[0]===4&&v2[1]>=4)return"KitKat";if(v2[0]===5)return"Lollipop";if(v2[0]===6)return"Marshmallow";if(v2[0]===7)return"Nougat";if(v2[0]===8)return"Oreo";if(v2[0]===9)return"Pie"}}static getVersionPrecision(version){return version.split(".").length}static compareVersions(versionA,versionB,isLoose=!1){const versionAPrecision=Utils.getVersionPrecision(versionA),versionBPrecision=Utils.getVersionPrecision(versionB);let precision=Math.max(versionAPrecision,versionBPrecision),lastPrecision=0;const chunks=Utils.map([versionA,versionB],version=>{const delta=precision-Utils.getVersionPrecision(version),_version=version+new Array(delta+1).join(".0");return Utils.map(_version.split("."),chunk=>new Array(20-chunk.length).join("0")+chunk).reverse()});for(isLoose&&(lastPrecision=precision-Math.min(versionAPrecision,versionBPrecision)),precision-=1;precision>=lastPrecision;){if(chunks[0][precision]>chunks[1][precision])return 1;if(chunks[0][precision]===chunks[1][precision]){if(precision===lastPrecision)return 0;precision-=1}else if(chunks[0][precision]<chunks[1][precision])return-1}}static map(arr,iterator){const result=[];let i2;if(Array.prototype.map)return Array.prototype.map.call(arr,iterator);for(i2=0;i2<arr.length;i2+=1)result.push(iterator(arr[i2]));return result}static find(arr,predicate){let i2,l;if(Array.prototype.find)return Array.prototype.find.call(arr,predicate);for(i2=0,l=arr.length;i2<l;i2+=1){const value=arr[i2];if(predicate(value,i2))return value}}static assign(obj,...assigners){const result=obj;let i2,l;if(Object.assign)return Object.assign(obj,...assigners);for(i2=0,l=assigners.length;i2<l;i2+=1){const assigner=assigners[i2];typeof assigner=="object"&&assigner!==null&&Object.keys(assigner).forEach(key=>{result[key]=assigner[key]})}return obj}static getBrowserAlias(browserName){return BROWSER_ALIASES_MAP[browserName]}static getBrowserTypeByAlias(browserAlias){return BROWSER_MAP[browserAlias]||""}}const commonVersionIdentifier=/version\/(\d+(\.?_?\d+)+)/i,browsersList=[{test:[/googlebot/i],describe(ua){const browser={name:"Googlebot"},version=Utils.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,ua)||Utils.getFirstMatch(commonVersionIdentifier,ua);return version&&(browser.version=version),browser}},{test:[/opera/i],describe(ua){const browser={name:"Opera"},version=Utils.getFirstMatch(commonVersionIdentifier,ua)||Utils.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/opr\/|opios/i],describe(ua){const browser={name:"Opera"},version=Utils.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,ua)||Utils.getFirstMatch(commonVersionIdentifier,ua);return version&&(browser.version=version),browser}},{test:[/SamsungBrowser/i],describe(ua){const browser={name:"Samsung Internet for Android"},version=Utils.getFirstMatch(commonVersionIdentifier,ua)||Utils.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/Whale/i],describe(ua){const browser={name:"NAVER Whale Browser"},version=Utils.getFirstMatch(commonVersionIdentifier,ua)||Utils.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/MZBrowser/i],describe(ua){const browser={name:"MZ Browser"},version=Utils.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,ua)||Utils.getFirstMatch(commonVersionIdentifier,ua);return version&&(browser.version=version),browser}},{test:[/focus/i],describe(ua){const browser={name:"Focus"},version=Utils.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,ua)||Utils.getFirstMatch(commonVersionIdentifier,ua);return version&&(browser.version=version),browser}},{test:[/swing/i],describe(ua){const browser={name:"Swing"},version=Utils.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,ua)||Utils.getFirstMatch(commonVersionIdentifier,ua);return version&&(browser.version=version),browser}},{test:[/coast/i],describe(ua){const browser={name:"Opera Coast"},version=Utils.getFirstMatch(commonVersionIdentifier,ua)||Utils.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe(ua){const browser={name:"Opera Touch"},version=Utils.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,ua)||Utils.getFirstMatch(commonVersionIdentifier,ua);return version&&(browser.version=version),browser}},{test:[/yabrowser/i],describe(ua){const browser={name:"Yandex Browser"},version=Utils.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,ua)||Utils.getFirstMatch(commonVersionIdentifier,ua);return version&&(browser.version=version),browser}},{test:[/ucbrowser/i],describe(ua){const browser={name:"UC Browser"},version=Utils.getFirstMatch(commonVersionIdentifier,ua)||Utils.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/Maxthon|mxios/i],describe(ua){const browser={name:"Maxthon"},version=Utils.getFirstMatch(commonVersionIdentifier,ua)||Utils.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/epiphany/i],describe(ua){const browser={name:"Epiphany"},version=Utils.getFirstMatch(commonVersionIdentifier,ua)||Utils.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/puffin/i],describe(ua){const browser={name:"Puffin"},version=Utils.getFirstMatch(commonVersionIdentifier,ua)||Utils.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/sleipnir/i],describe(ua){const browser={name:"Sleipnir"},version=Utils.getFirstMatch(commonVersionIdentifier,ua)||Utils.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/k-meleon/i],describe(ua){const browser={name:"K-Meleon"},version=Utils.getFirstMatch(commonVersionIdentifier,ua)||Utils.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/micromessenger/i],describe(ua){const browser={name:"WeChat"},version=Utils.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,ua)||Utils.getFirstMatch(commonVersionIdentifier,ua);return version&&(browser.version=version),browser}},{test:[/qqbrowser/i],describe(ua){const browser={name:/qqbrowserlite/i.test(ua)?"QQ Browser Lite":"QQ Browser"},version=Utils.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,ua)||Utils.getFirstMatch(commonVersionIdentifier,ua);return version&&(browser.version=version),browser}},{test:[/msie|trident/i],describe(ua){const browser={name:"Internet Explorer"},version=Utils.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/\sedg\//i],describe(ua){const browser={name:"Microsoft Edge"},version=Utils.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/edg([ea]|ios)/i],describe(ua){const browser={name:"Microsoft Edge"},version=Utils.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/vivaldi/i],describe(ua){const browser={name:"Vivaldi"},version=Utils.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/seamonkey/i],describe(ua){const browser={name:"SeaMonkey"},version=Utils.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/sailfish/i],describe(ua){const browser={name:"Sailfish"},version=Utils.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,ua);return version&&(browser.version=version),browser}},{test:[/silk/i],describe(ua){const browser={name:"Amazon Silk"},version=Utils.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/phantom/i],describe(ua){const browser={name:"PhantomJS"},version=Utils.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/slimerjs/i],describe(ua){const browser={name:"SlimerJS"},version=Utils.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(ua){const browser={name:"BlackBerry"},version=Utils.getFirstMatch(commonVersionIdentifier,ua)||Utils.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/(web|hpw)[o0]s/i],describe(ua){const browser={name:"WebOS Browser"},version=Utils.getFirstMatch(commonVersionIdentifier,ua)||Utils.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/bada/i],describe(ua){const browser={name:"Bada"},version=Utils.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/tizen/i],describe(ua){const browser={name:"Tizen"},version=Utils.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,ua)||Utils.getFirstMatch(commonVersionIdentifier,ua);return version&&(browser.version=version),browser}},{test:[/qupzilla/i],describe(ua){const browser={name:"QupZilla"},version=Utils.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,ua)||Utils.getFirstMatch(commonVersionIdentifier,ua);return version&&(browser.version=version),browser}},{test:[/firefox|iceweasel|fxios/i],describe(ua){const browser={name:"Firefox"},version=Utils.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/electron/i],describe(ua){const browser={name:"Electron"},version=Utils.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/MiuiBrowser/i],describe(ua){const browser={name:"Miui"},version=Utils.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/chromium/i],describe(ua){const browser={name:"Chromium"},version=Utils.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,ua)||Utils.getFirstMatch(commonVersionIdentifier,ua);return version&&(browser.version=version),browser}},{test:[/chrome|crios|crmo/i],describe(ua){const browser={name:"Chrome"},version=Utils.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test:[/GSA/i],describe(ua){const browser={name:"Google Search"},version=Utils.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,ua);return version&&(browser.version=version),browser}},{test(parser){const notLikeAndroid=!parser.test(/like android/i),butAndroid=parser.test(/android/i);return notLikeAndroid&&butAndroid},describe(ua){const browser={name:"Android Browser"},version=Utils.getFirstMatch(commonVersionIdentifier,ua);return version&&(browser.version=version),browser}},{test:[/playstation 4/i],describe(ua){const browser={name:"PlayStation 4"},version=Utils.getFirstMatch(commonVersionIdentifier,ua);return version&&(browser.version=version),browser}},{test:[/safari|applewebkit/i],describe(ua){const browser={name:"Safari"},version=Utils.getFirstMatch(commonVersionIdentifier,ua);return version&&(browser.version=version),browser}},{test:[/.*/i],describe(ua){const regexpWithoutDeviceSpec=/^(.*)\/(.*) /,regexpWithDeviceSpec=/^(.*)\/(.*)[ \t]\((.*)/,regexp=ua.search("\\(")!==-1?regexpWithDeviceSpec:regexpWithoutDeviceSpec;return{name:Utils.getFirstMatch(regexp,ua),version:Utils.getSecondMatch(regexp,ua)}}}],osParsersList=[{test:[/Roku\/DVP/],describe(ua){const version=Utils.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,ua);return{name:OS_MAP.Roku,version}}},{test:[/windows phone/i],describe(ua){const version=Utils.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,ua);return{name:OS_MAP.WindowsPhone,version}}},{test:[/windows /i],describe(ua){const version=Utils.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,ua),versionName=Utils.getWindowsVersionName(version);return{name:OS_MAP.Windows,version,versionName}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(ua){const result={name:OS_MAP.iOS},version=Utils.getSecondMatch(/(Version\/)(\d[\d.]+)/,ua);return version&&(result.version=version),result}},{test:[/macintosh/i],describe(ua){const version=Utils.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,ua).replace(/[_\s]/g,"."),versionName=Utils.getMacOSVersionName(version),os={name:OS_MAP.MacOS,version};return versionName&&(os.versionName=versionName),os}},{test:[/(ipod|iphone|ipad)/i],describe(ua){const version=Utils.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,ua).replace(/[_\s]/g,".");return{name:OS_MAP.iOS,version}}},{test(parser){const notLikeAndroid=!parser.test(/like android/i),butAndroid=parser.test(/android/i);return notLikeAndroid&&butAndroid},describe(ua){const version=Utils.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,ua),versionName=Utils.getAndroidVersionName(version),os={name:OS_MAP.Android,version};return versionName&&(os.versionName=versionName),os}},{test:[/(web|hpw)[o0]s/i],describe(ua){const version=Utils.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,ua),os={name:OS_MAP.WebOS};return version&&version.length&&(os.version=version),os}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(ua){const version=Utils.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,ua)||Utils.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,ua)||Utils.getFirstMatch(/\bbb(\d+)/i,ua);return{name:OS_MAP.BlackBerry,version}}},{test:[/bada/i],describe(ua){const version=Utils.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,ua);return{name:OS_MAP.Bada,version}}},{test:[/tizen/i],describe(ua){const version=Utils.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,ua);return{name:OS_MAP.Tizen,version}}},{test:[/linux/i],describe(){return{name:OS_MAP.Linux}}},{test:[/CrOS/],describe(){return{name:OS_MAP.ChromeOS}}},{test:[/PlayStation 4/],describe(ua){const version=Utils.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,ua);return{name:OS_MAP.PlayStation4,version}}}],platformParsersList=[{test:[/googlebot/i],describe(){return{type:"bot",vendor:"Google"}}},{test:[/huawei/i],describe(ua){const model=Utils.getFirstMatch(/(can-l01)/i,ua)&&"Nova",platform={type:PLATFORMS_MAP.mobile,vendor:"Huawei"};return model&&(platform.model=model),platform}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe(){return{type:PLATFORMS_MAP.tablet,vendor:"Nexus"}}},{test:[/ipad/i],describe(){return{type:PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(){return{type:PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/kftt build/i],describe(){return{type:PLATFORMS_MAP.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"}}},{test:[/silk/i],describe(){return{type:PLATFORMS_MAP.tablet,vendor:"Amazon"}}},{test:[/tablet(?! pc)/i],describe(){return{type:PLATFORMS_MAP.tablet}}},{test(parser){const iDevice=parser.test(/ipod|iphone/i),likeIDevice=parser.test(/like (ipod|iphone)/i);return iDevice&&!likeIDevice},describe(ua){const model=Utils.getFirstMatch(/(ipod|iphone)/i,ua);return{type:PLATFORMS_MAP.mobile,vendor:"Apple",model}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe(){return{type:PLATFORMS_MAP.mobile,vendor:"Nexus"}}},{test:[/[^-]mobi/i],describe(){return{type:PLATFORMS_MAP.mobile}}},{test(parser){return parser.getBrowserName(!0)==="blackberry"},describe(){return{type:PLATFORMS_MAP.mobile,vendor:"BlackBerry"}}},{test(parser){return parser.getBrowserName(!0)==="bada"},describe(){return{type:PLATFORMS_MAP.mobile}}},{test(parser){return parser.getBrowserName()==="windows phone"},describe(){return{type:PLATFORMS_MAP.mobile,vendor:"Microsoft"}}},{test(parser){const osMajorVersion=Number(String(parser.getOSVersion()).split(".")[0]);return parser.getOSName(!0)==="android"&&osMajorVersion>=3},describe(){return{type:PLATFORMS_MAP.tablet}}},{test(parser){return parser.getOSName(!0)==="android"},describe(){return{type:PLATFORMS_MAP.mobile}}},{test(parser){return parser.getOSName(!0)==="macos"},describe(){return{type:PLATFORMS_MAP.desktop,vendor:"Apple"}}},{test(parser){return parser.getOSName(!0)==="windows"},describe(){return{type:PLATFORMS_MAP.desktop}}},{test(parser){return parser.getOSName(!0)==="linux"},describe(){return{type:PLATFORMS_MAP.desktop}}},{test(parser){return parser.getOSName(!0)==="playstation 4"},describe(){return{type:PLATFORMS_MAP.tv}}},{test(parser){return parser.getOSName(!0)==="roku"},describe(){return{type:PLATFORMS_MAP.tv}}}],enginesParsersList=[{test(parser){return parser.getBrowserName(!0)==="microsoft edge"},describe(ua){if(/\sedg\//i.test(ua))return{name:ENGINE_MAP.Blink};const version=Utils.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,ua);return{name:ENGINE_MAP.EdgeHTML,version}}},{test:[/trident/i],describe(ua){const engine={name:ENGINE_MAP.Trident},version=Utils.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,ua);return version&&(engine.version=version),engine}},{test(parser){return parser.test(/presto/i)},describe(ua){const engine={name:ENGINE_MAP.Presto},version=Utils.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,ua);return version&&(engine.version=version),engine}},{test(parser){const isGecko=parser.test(/gecko/i),likeGecko=parser.test(/like gecko/i);return isGecko&&!likeGecko},describe(ua){const engine={name:ENGINE_MAP.Gecko},version=Utils.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,ua);return version&&(engine.version=version),engine}},{test:[/(apple)?webkit\/537\.36/i],describe(){return{name:ENGINE_MAP.Blink}}},{test:[/(apple)?webkit/i],describe(ua){const engine={name:ENGINE_MAP.WebKit},version=Utils.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,ua);return version&&(engine.version=version),engine}}];class Parser{constructor(UA,skipParsing=!1){if(UA==null||UA==="")throw new Error("UserAgent parameter can't be empty");this._ua=UA,this.parsedResult={},skipParsing!==!0&&this.parse()}getUA(){return this._ua}test(regex){return regex.test(this._ua)}parseBrowser(){this.parsedResult.browser={};const browserDescriptor=Utils.find(browsersList,_browser=>{if(typeof _browser.test=="function")return _browser.test(this);if(_browser.test instanceof Array)return _browser.test.some(condition=>this.test(condition));throw new Error("Browser's test function is not valid")});return browserDescriptor&&(this.parsedResult.browser=browserDescriptor.describe(this.getUA())),this.parsedResult.browser}getBrowser(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()}getBrowserName(toLowerCase){return toLowerCase?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""}getBrowserVersion(){return this.getBrowser().version}getOS(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()}parseOS(){this.parsedResult.os={};const os=Utils.find(osParsersList,_os=>{if(typeof _os.test=="function")return _os.test(this);if(_os.test instanceof Array)return _os.test.some(condition=>this.test(condition));throw new Error("Browser's test function is not valid")});return os&&(this.parsedResult.os=os.describe(this.getUA())),this.parsedResult.os}getOSName(toLowerCase){const{name}=this.getOS();return toLowerCase?String(name).toLowerCase()||"":name||""}getOSVersion(){return this.getOS().version}getPlatform(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()}getPlatformType(toLowerCase=!1){const{type}=this.getPlatform();return toLowerCase?String(type).toLowerCase()||"":type||""}parsePlatform(){this.parsedResult.platform={};const platform=Utils.find(platformParsersList,_platform=>{if(typeof _platform.test=="function")return _platform.test(this);if(_platform.test instanceof Array)return _platform.test.some(condition=>this.test(condition));throw new Error("Browser's test function is not valid")});return platform&&(this.parsedResult.platform=platform.describe(this.getUA())),this.parsedResult.platform}getEngine(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()}getEngineName(toLowerCase){return toLowerCase?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""}parseEngine(){this.parsedResult.engine={};const engine=Utils.find(enginesParsersList,_engine=>{if(typeof _engine.test=="function")return _engine.test(this);if(_engine.test instanceof Array)return _engine.test.some(condition=>this.test(condition));throw new Error("Browser's test function is not valid")});return engine&&(this.parsedResult.engine=engine.describe(this.getUA())),this.parsedResult.engine}parse(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this}getResult(){return Utils.assign({},this.parsedResult)}satisfies(checkTree){const platformsAndOSes={};let platformsAndOSCounter=0;const browsers={};let browsersCounter=0;if(Object.keys(checkTree).forEach(key=>{const currentDefinition=checkTree[key];typeof currentDefinition=="string"?(browsers[key]=currentDefinition,browsersCounter+=1):typeof currentDefinition=="object"&&(platformsAndOSes[key]=currentDefinition,platformsAndOSCounter+=1)}),platformsAndOSCounter>0){const platformsAndOSNames=Object.keys(platformsAndOSes),OSMatchingDefinition=Utils.find(platformsAndOSNames,name=>this.isOS(name));if(OSMatchingDefinition){const osResult=this.satisfies(platformsAndOSes[OSMatchingDefinition]);if(osResult!==void 0)return osResult}const platformMatchingDefinition=Utils.find(platformsAndOSNames,name=>this.isPlatform(name));if(platformMatchingDefinition){const platformResult=this.satisfies(platformsAndOSes[platformMatchingDefinition]);if(platformResult!==void 0)return platformResult}}if(browsersCounter>0){const browserNames=Object.keys(browsers),matchingDefinition=Utils.find(browserNames,name=>this.isBrowser(name,!0));if(matchingDefinition!==void 0)return this.compareVersion(browsers[matchingDefinition])}}isBrowser(browserName,includingAlias=!1){const defaultBrowserName=this.getBrowserName().toLowerCase();let browserNameLower=browserName.toLowerCase();const alias=Utils.getBrowserTypeByAlias(browserNameLower);return includingAlias&&alias&&(browserNameLower=alias.toLowerCase()),browserNameLower===defaultBrowserName}compareVersion(version){let expectedResults=[0],comparableVersion=version,isLoose=!1;const currentBrowserVersion=this.getBrowserVersion();if(typeof currentBrowserVersion=="string")return version[0]===">"||version[0]==="<"?(comparableVersion=version.substr(1),version[1]==="="?(isLoose=!0,comparableVersion=version.substr(2)):expectedResults=[],version[0]===">"?expectedResults.push(1):expectedResults.push(-1)):version[0]==="="?comparableVersion=version.substr(1):version[0]==="~"&&(isLoose=!0,comparableVersion=version.substr(1)),expectedResults.indexOf(Utils.compareVersions(currentBrowserVersion,comparableVersion,isLoose))>-1}isOS(osName){return this.getOSName(!0)===String(osName).toLowerCase()}isPlatform(platformType){return this.getPlatformType(!0)===String(platformType).toLowerCase()}isEngine(engineName){return this.getEngineName(!0)===String(engineName).toLowerCase()}is(anything,includingAlias=!1){return this.isBrowser(anything,includingAlias)||this.isOS(anything)||this.isPlatform(anything)}some(anythings=[]){return anythings.some(anything=>this.is(anything))}}/*!
 * Bowser - a browser detector
 * https://github.com/lancedikson/bowser
 * MIT License | (c) Dustin Diaz 2012-2015
 * MIT License | (c) Denis Demchenko 2015-2019
 */class Bowser{static getParser(UA,skipParsing=!1){if(typeof UA!="string")throw new Error("UserAgent should be a string");return new Parser(UA,skipParsing)}static parse(UA){return new Parser(UA).getResult()}static get BROWSER_MAP(){return BROWSER_MAP}static get ENGINE_MAP(){return ENGINE_MAP}static get OS_MAP(){return OS_MAP}static get PLATFORMS_MAP(){return PLATFORMS_MAP}}const useBrowserInfo=()=>panel.T(()=>{{const result=Bowser.parse(window.navigator.userAgent);return{browser:result.browser,engine:result.engine,os:result.os}}},[]);function useHotkeyListenerComboText(action){return useBrowserInfo().os.name.toLowerCase().includes("mac")?panel.hotkeyActionDefinitions[action].keyComboMac:panel.hotkeyActionDefinitions[action].keyComboDefault}function ToolbarChatArea(){const chatState=useChatState(),[isComposing,setIsComposing]=panel.d(!1),currentChat=panel.T(()=>chatState.chats.find(c2=>c2.id===chatState.currentChatId),[chatState.chats,chatState.currentChatId]),currentInput=panel.T(()=>(currentChat==null?void 0:currentChat.inputValue)||"",[currentChat==null?void 0:currentChat.inputValue]),handleInputChange=panel.q(value=>{chatState.setChatInput(chatState.currentChatId,value)},[chatState.setChatInput,chatState.currentChatId]),handleSubmit=panel.q(()=>{!currentChat||!currentInput.trim()||chatState.addMessage(currentChat.id,currentInput)},[currentChat,currentInput,chatState.addMessage]),handleKeyDown=panel.q(e2=>{e2.key==="Enter"&&!e2.shiftKey&&!isComposing&&(e2.preventDefault(),handleSubmit())},[handleSubmit,isComposing]),handleCompositionStart=panel.q(()=>{setIsComposing(!0)},[]),handleCompositionEnd=panel.q(()=>{setIsComposing(!1)},[]),inputRef=panel.A(null);panel.y(()=>{var _a,_b,_c;const blurHandler=()=>{var _a2;return(_a2=inputRef.current)==null?void 0:_a2.focus()};return chatState.isPromptCreationActive?((_a=inputRef.current)==null||_a.focus(),(_b=inputRef.current)==null||_b.addEventListener("blur",blurHandler)):(_c=inputRef.current)==null||_c.blur(),()=>{var _a2;(_a2=inputRef.current)==null||_a2.removeEventListener("blur",blurHandler)}},[chatState.isPromptCreationActive]);const buttonClassName=panel.T(()=>panel.cn("flex size-8 items-center justify-center rounded-full bg-transparent p-1 text-zinc-950 opacity-20 transition-all duration-150",currentInput.length>0&&"bg-blue-600 text-white opacity-100",chatState.promptState==="loading"&&"cursor-not-allowed bg-zinc-300 text-zinc-500 opacity-30"),[currentInput.length,chatState.promptState]),textareaClassName=panel.T(()=>panel.cn("h-full w-full flex-1 resize-none bg-transparent text-zinc-950 transition-all duration-150 placeholder:text-zinc-950/50 focus:outline-none",chatState.promptState==="loading"&&"text-zinc-500 placeholder:text-zinc-400"),[chatState.promptState]),containerClassName=panel.T(()=>{const baseClasses="flex h-24 w-full flex-1 flex-row items-end gap-1 rounded-2xl p-4 text-sm text-zinc-950 shadow-md backdrop-blur transition-all duration-150 placeholder:text-zinc-950/70";switch(chatState.promptState){case"loading":return panel.cn(baseClasses,"border-2 border-transparent bg-zinc-50/80","chat-loading-gradient");case"success":return panel.cn(baseClasses,"border-2 border-transparent bg-zinc-50/80","chat-success-border");case"error":return panel.cn(baseClasses,"border-2 border-transparent bg-zinc-50/80","chat-error-border animate-shake");default:return panel.cn(baseClasses,"border border-border/30 bg-zinc-50/80")}},[chatState.promptState]),ctrlAltCText=useHotkeyListenerComboText(panel.HotkeyActions.CTRL_ALT_C);return pluginUi_jsxRuntime.u("div",{className:containerClassName,onClick:()=>chatState.startPromptCreation(),role:"button",tabIndex:0,children:[pluginUi_jsxRuntime.u(J,{ref:inputRef,className:textareaClassName,value:currentInput,onChange:e2=>handleInputChange(e2.currentTarget.value),onKeyDown:handleKeyDown,onCompositionStart:handleCompositionStart,onCompositionEnd:handleCompositionEnd,placeholder:chatState.isPromptCreationActive?chatState.promptState==="loading"?"Processing...":"Enter prompt...":`What do you want to change? (${ctrlAltCText})`,disabled:chatState.promptState==="loading"}),pluginUi_jsxRuntime.u(H$1,{className:buttonClassName,disabled:currentInput.length===0||chatState.promptState==="loading",onClick:handleSubmit,children:pluginUi_jsxRuntime.u(Send,{className:"size-4"})})]})}const DraggableContext=pluginUi_jsxRuntime.K(null),DraggableProvider=({containerRef,children,snapAreas,onDragStart,onDragEnd})=>{const[borderLocation,setBorderLocation]=panel.d({top:0,left:0,right:0,bottom:0});panel.y(()=>{if(!containerRef.current)return;const updateBorderLocation=()=>{if(containerRef.current){const rect=containerRef.current.getBoundingClientRect();setBorderLocation({top:rect.top,left:rect.left,right:rect.right,bottom:rect.bottom})}};updateBorderLocation();const resizeObserver=new ResizeObserver(updateBorderLocation);return resizeObserver.observe(containerRef.current),window.addEventListener("resize",updateBorderLocation),()=>{containerRef.current&&resizeObserver.unobserve(containerRef.current),resizeObserver.disconnect(),window.removeEventListener("resize",updateBorderLocation)}},[containerRef]);const dragStartListeners=panel.A(new Set),dragEndListeners=panel.A(new Set),registerDragStart=panel.q(cb=>(dragStartListeners.current.add(cb),()=>dragStartListeners.current.delete(cb)),[]),registerDragEnd=panel.q(cb=>(dragEndListeners.current.add(cb),()=>dragEndListeners.current.delete(cb)),[]),emitDragStart=panel.q(()=>{onDragStart&&onDragStart(),dragStartListeners.current.forEach(cb=>cb())},[onDragStart]),emitDragEnd=panel.q(()=>{onDragEnd&&onDragEnd(),dragEndListeners.current.forEach(cb=>cb())},[onDragEnd]),contextValue={borderLocation,snapAreas,registerDragStart,registerDragEnd,emitDragStart,emitDragEnd};return pluginUi_jsxRuntime.u(DraggableContext.Provider,{value:contextValue,children})};function useDraggable(config){const providerData=panel.x(DraggableContext),latestProviderDataRef=panel.A(providerData);panel.y(()=>{latestProviderDataRef.current=providerData},[providerData]);const movingElementRef=panel.A(null),dragInitiatorRef=panel.A(null),[movingElementNode,setMovingElementNode]=panel.d(null),[dragInitiatorNode,setDragInitiatorNode]=panel.d(null),mouseToDraggableCenterOffsetRef=panel.A(null),mouseDownPosRef=panel.A(null),currentMousePosRef=panel.A(null),isDraggingRef=panel.A(!1),persistedRelativeCenterRef=panel.A(config.initialRelativeCenter),[currentSnapArea,setCurrentSnapArea]=panel.d(null),{startThreshold=3,areaSnapThreshold=60,onDragStart,onDragEnd,initialSnapArea,springStiffness=.2,springDampness=.55}=config,animatedPositionRef=panel.A(null),velocityRef=panel.A({x:0,y:0}),hasAnimatedOnceRef=panel.A(!1);panel.y(()=>{if(initialSnapArea&&providerData&&providerData.borderLocation&&providerData.snapAreas&&providerData.snapAreas[initialSnapArea]&&!isDraggingRef.current){const{top,left,right,bottom}=providerData.borderLocation,width=right-left,height=bottom-top,center={topLeft:{x:left,y:top},topRight:{x:right,y:top},bottomLeft:{x:left,y:bottom},bottomRight:{x:right,y:bottom}}[initialSnapArea];if(center&&width>0&&height>0){const relX=(center.x-left)/width,relY=(center.y-top)/height;persistedRelativeCenterRef.current={x:relX,y:relY}}else center&&console.warn("useDraggable: Container for initialSnapArea has zero width or height. Cannot calculate relative center from snap area. Falling back to initialRelativeCenter or undefined.")}},[initialSnapArea,providerData]);function getSnapAreaCenters(borderLocation){const{top,left,right,bottom}=borderLocation,centerX=(left+right)/2;return{topLeft:{x:left,y:top},topCenter:{x:centerX,y:top},topRight:{x:right,y:top},bottomLeft:{x:left,y:bottom},bottomCenter:{x:centerX,y:bottom},bottomRight:{x:right,y:bottom}}}const updateDraggablePosition=panel.q(()=>{var _a,_b;const draggableEl=movingElementRef.current;if(!draggableEl)return;const draggableWidth=draggableEl.offsetWidth,draggableHeight=draggableEl.offsetHeight,offsetParent=draggableEl.offsetParent;let parentViewportLeft=0,parentViewportTop=0,parentWidth=window.innerWidth,parentHeight=window.innerHeight;if(offsetParent){const opRect=offsetParent.getBoundingClientRect();parentViewportLeft=opRect.left,parentViewportTop=opRect.top,parentWidth=offsetParent.offsetWidth||window.innerWidth,parentHeight=offsetParent.offsetHeight||window.innerHeight}let targetViewportCenterX=null,targetViewportCenterY=null;const currentDesiredRelativeCenter=persistedRelativeCenterRef.current;let snapArea=null,snapTarget=null;const provider=latestProviderDataRef.current;let isTopHalf=!0,isLeftHalf=!0;if(isDraggingRef.current&&mouseToDraggableCenterOffsetRef.current&&currentMousePosRef.current&&provider&&provider.borderLocation&&provider.snapAreas){const dragCenter={x:currentMousePosRef.current.x-mouseToDraggableCenterOffsetRef.current.x,y:currentMousePosRef.current.y-mouseToDraggableCenterOffsetRef.current.y},areaCenters=getSnapAreaCenters(provider.borderLocation);let minDist=Number.POSITIVE_INFINITY,closestArea=null,closestCenter=null;for(const area in provider.snapAreas)if(provider.snapAreas[area]){const center=areaCenters[area];if(!center)continue;const dist=Math.hypot(center.x-dragCenter.x,center.y-dragCenter.y);dist<minDist&&(minDist=dist,closestArea=area,closestCenter=center)}closestArea&&closestCenter&&minDist<=areaSnapThreshold&&(snapArea=closestArea,snapTarget=closestCenter),isLeftHalf=(dragCenter.x-parentViewportLeft)/parentWidth<=.5,isTopHalf=(dragCenter.y-parentViewportTop)/parentHeight<=.5}if(isDraggingRef.current&&snapTarget)targetViewportCenterX=snapTarget.x,targetViewportCenterY=snapTarget.y,setCurrentSnapArea(snapArea),isLeftHalf=(snapTarget.x-parentViewportLeft)/parentWidth<=.5,isTopHalf=(snapTarget.y-parentViewportTop)/parentHeight<=.5;else if(isDraggingRef.current&&mouseToDraggableCenterOffsetRef.current&&currentMousePosRef.current)targetViewportCenterX=currentMousePosRef.current.x-mouseToDraggableCenterOffsetRef.current.x,targetViewportCenterY=currentMousePosRef.current.y-mouseToDraggableCenterOffsetRef.current.y,setCurrentSnapArea(null),isLeftHalf=(targetViewportCenterX-parentViewportLeft)/parentWidth<=.5,isTopHalf=(targetViewportCenterY-parentViewportTop)/parentHeight<=.5;else{if(currentDesiredRelativeCenter&&parentWidth>0&&parentHeight>0){if(isTopHalf=currentDesiredRelativeCenter.y<=.5,isLeftHalf=currentDesiredRelativeCenter.x<=.5,isLeftHalf){const targetCenterXInParent=parentWidth*currentDesiredRelativeCenter.x;targetViewportCenterX=parentViewportLeft+targetCenterXInParent}else{const targetCenterXInParent=parentWidth*(1-currentDesiredRelativeCenter.x);targetViewportCenterX=parentViewportLeft+parentWidth-targetCenterXInParent}if(isTopHalf){const targetCenterYInParent=parentHeight*currentDesiredRelativeCenter.y;targetViewportCenterY=parentViewportTop+targetCenterYInParent}else{const targetCenterYInParent=parentHeight*(1-currentDesiredRelativeCenter.y);targetViewportCenterY=parentViewportTop+parentHeight-targetCenterYInParent}}else{!((_a=movingElementRef.current)!=null&&_a.style.left)&&!((_b=movingElementRef.current)!=null&&_b.style.top)&&console.warn("useDraggable: Cannot determine position. Parent has no dimensions or initialRelativeCenter was not effectively set.");return}setCurrentSnapArea(null)}if(targetViewportCenterX===null||targetViewportCenterY===null)return;const{borderLocation}=latestProviderDataRef.current||{borderLocation:void 0};if(borderLocation&&draggableWidth>0&&draggableHeight>0){const providerRectWidth=borderLocation.right-borderLocation.left,providerRectHeight=borderLocation.bottom-borderLocation.top;let clampedCenterX=targetViewportCenterX,clampedCenterY=targetViewportCenterY;if(draggableWidth>=providerRectWidth)clampedCenterX=borderLocation.left+providerRectWidth/2;else{const minX=borderLocation.left+draggableWidth/2,maxX=borderLocation.right-draggableWidth/2;clampedCenterX=Math.max(minX,Math.min(clampedCenterX,maxX))}if(draggableHeight>=providerRectHeight)clampedCenterY=borderLocation.top+providerRectHeight/2;else{const minY=borderLocation.top+draggableHeight/2,maxY=borderLocation.bottom-draggableHeight/2;clampedCenterY=Math.max(minY,Math.min(clampedCenterY,maxY))}targetViewportCenterX=clampedCenterX,targetViewportCenterY=clampedCenterY}if(!animatedPositionRef.current){animatedPositionRef.current={x:targetViewportCenterX,y:targetViewportCenterY},velocityRef.current={x:0,y:0};const targetElementStyleX2=targetViewportCenterX-draggableWidth/2,targetElementStyleY2=targetViewportCenterY-draggableHeight/2,elStyle2=draggableEl.style;if(elStyle2.right="",elStyle2.bottom="",elStyle2.left="",elStyle2.top="",isLeftHalf){const styleLeftPx=targetElementStyleX2-parentViewportLeft;elStyle2.left=parentWidth>0?`${(styleLeftPx/parentWidth*100).toFixed(2)}%`:"0px",elStyle2.right=""}else{const styleRightPx=parentViewportLeft+parentWidth-(targetElementStyleX2+draggableWidth);elStyle2.right=parentWidth>0?`${(styleRightPx/parentWidth*100).toFixed(2)}%`:"0px",elStyle2.left=""}if(isTopHalf){const styleTopPx=targetElementStyleY2-parentViewportTop;elStyle2.top=parentHeight>0?`${(styleTopPx/parentHeight*100).toFixed(2)}%`:"0px",elStyle2.bottom=""}else{const styleBottomPx=parentViewportTop+parentHeight-(targetElementStyleY2+draggableHeight);elStyle2.bottom=parentHeight>0?`${(styleBottomPx/parentHeight*100).toFixed(2)}%`:"0px",elStyle2.top=""}hasAnimatedOnceRef.current=!0;return}if(!hasAnimatedOnceRef.current){hasAnimatedOnceRef.current=!0;return}const pos=animatedPositionRef.current,vel=velocityRef.current,dx=targetViewportCenterX-pos.x,dy=targetViewportCenterY-pos.y,ax=springStiffness*dx-springDampness*vel.x,ay=springStiffness*dy-springDampness*vel.y;vel.x+=ax,vel.y+=ay,pos.x+=vel.x,pos.y+=vel.y;const threshold=.5;Math.abs(dx)<threshold&&Math.abs(dy)<threshold&&Math.abs(vel.x)<threshold&&Math.abs(vel.y)<threshold&&(pos.x=targetViewportCenterX,pos.y=targetViewportCenterY,vel.x=0,vel.y=0),animatedPositionRef.current={...pos},velocityRef.current={...vel};const targetElementStyleX=pos.x-draggableWidth/2,targetElementStyleY=pos.y-draggableHeight/2,elStyle=draggableEl.style;if(elStyle.right="",elStyle.bottom="",elStyle.left="",elStyle.top="",isLeftHalf){const styleLeftPx=targetElementStyleX-parentViewportLeft;elStyle.left=parentWidth>0?`${(styleLeftPx/parentWidth*100).toFixed(2)}%`:"0px",elStyle.right=""}else{const styleRightPx=parentViewportLeft+parentWidth-(targetElementStyleX+draggableWidth);elStyle.right=parentWidth>0?`${(styleRightPx/parentWidth*100).toFixed(2)}%`:"0px",elStyle.left=""}if(isTopHalf){const styleTopPx=targetElementStyleY-parentViewportTop;elStyle.top=parentHeight>0?`${(styleTopPx/parentHeight*100).toFixed(2)}%`:"0px",elStyle.bottom=""}else{const styleBottomPx=parentViewportTop+parentHeight-(targetElementStyleY+draggableHeight);elStyle.bottom=parentHeight>0?`${(styleBottomPx/parentHeight*100).toFixed(2)}%`:"0px",elStyle.top=""}(Math.abs(pos.x-targetViewportCenterX)>threshold||Math.abs(pos.y-targetViewportCenterY)>threshold||Math.abs(vel.x)>threshold||Math.abs(vel.y)>threshold||isDraggingRef.current)&&requestAnimationFrame(updateDraggablePosition)},[areaSnapThreshold,springStiffness,springDampness]),[wasDragged,setWasDragged]=panel.d(!1),mouseUpHandler=panel.q(e2=>{var _a;if(isDraggingRef.current){onDragEnd&&onDragEnd(),(_a=latestProviderDataRef.current)!=null&&_a.emitDragEnd&&latestProviderDataRef.current.emitDragEnd(),setWasDragged(!0),setTimeout(()=>setWasDragged(!1),0);const draggableEl=movingElementRef.current,provider=latestProviderDataRef.current;if(draggableEl&&provider&&provider.borderLocation){const draggableWidth=draggableEl.offsetWidth,draggableHeight=draggableEl.offsetHeight,offsetParent=draggableEl.offsetParent;let parentViewportLeft=0,parentViewportTop=0,parentWidth=window.innerWidth,parentHeight=window.innerHeight;if(offsetParent){const opRect=offsetParent.getBoundingClientRect();parentViewportLeft=opRect.left,parentViewportTop=opRect.top,parentWidth=offsetParent.offsetWidth||window.innerWidth,parentHeight=offsetParent.offsetHeight||window.innerHeight}let releasedCenterX=0,releasedCenterY=0;currentMousePosRef.current&&mouseToDraggableCenterOffsetRef.current?(releasedCenterX=currentMousePosRef.current.x-mouseToDraggableCenterOffsetRef.current.x,releasedCenterY=currentMousePosRef.current.y-mouseToDraggableCenterOffsetRef.current.y):animatedPositionRef.current&&(releasedCenterX=animatedPositionRef.current.x,releasedCenterY=animatedPositionRef.current.y);const borderLocation=provider.borderLocation,minX=borderLocation.left+draggableWidth/2,maxX=borderLocation.right-draggableWidth/2,minY=borderLocation.top+draggableHeight/2,maxY=borderLocation.bottom-draggableHeight/2;releasedCenterX=Math.max(minX,Math.min(releasedCenterX,maxX)),releasedCenterY=Math.max(minY,Math.min(releasedCenterY,maxY));const areaCenters=getSnapAreaCenters(borderLocation);let minDist=Number.POSITIVE_INFINITY,closestArea=null,closestCenter=null;for(const area in provider.snapAreas)if(provider.snapAreas[area]){const center=areaCenters[area];if(!center)continue;const dist=Math.hypot(center.x-releasedCenterX,center.y-releasedCenterY);dist<minDist&&(minDist=dist,closestArea=area,closestCenter=center)}if(closestArea&&closestCenter){setCurrentSnapArea(closestArea);const relX=(closestCenter.x-parentViewportLeft)/parentWidth,relY=(closestCenter.y-parentViewportTop)/parentHeight;persistedRelativeCenterRef.current={x:relX,y:relY}}else{setCurrentSnapArea(null);const relX=(releasedCenterX-parentViewportLeft)/parentWidth,relY=(releasedCenterY-parentViewportTop)/parentHeight;persistedRelativeCenterRef.current={x:relX,y:relY}}}}mouseDownPosRef.current=null,isDraggingRef.current=!1,window.removeEventListener("mousemove",mouseMoveHandler,{capture:!0}),window.removeEventListener("mouseup",mouseUpHandler,{capture:!0}),movingElementRef.current&&(movingElementRef.current.style.userSelect=""),document.body.style.userSelect="",document.body.style.cursor=""},[onDragEnd]),mouseMoveHandler=panel.q(e2=>{var _a;if(!mouseDownPosRef.current)return;Math.hypot(e2.clientX-mouseDownPosRef.current.x,e2.clientY-mouseDownPosRef.current.y)>startThreshold&&!isDraggingRef.current&&(isDraggingRef.current=!0,movingElementRef.current&&(movingElementRef.current.style.userSelect="none"),document.body.style.userSelect="none",document.body.style.cursor="grabbing",onDragStart&&onDragStart(),(_a=latestProviderDataRef.current)!=null&&_a.emitDragStart&&latestProviderDataRef.current.emitDragStart(),requestAnimationFrame(updateDraggablePosition)),currentMousePosRef.current={x:e2.clientX,y:e2.clientY}},[startThreshold,onDragStart,updateDraggablePosition]),mouseDownHandler=panel.q(e2=>{if(e2.button!==0)return;const handleNode=dragInitiatorRef.current,draggableItemNode=movingElementRef.current;if(handleNode){if(!handleNode.contains(e2.target)&&e2.target!==handleNode)return}else if(draggableItemNode){if(!draggableItemNode.contains(e2.target)&&e2.target!==draggableItemNode)return}else{console.error("Draggable element or handle ref not set in mouseDownHandler");return}if(mouseDownPosRef.current={x:e2.clientX,y:e2.clientY},!movingElementRef.current){console.error("Draggable element ref not set in mouseDownHandler");return}const rect=movingElementRef.current.getBoundingClientRect(),currentDraggableCenterX=rect.left+rect.width/2,currentDraggableCenterY=rect.top+rect.height/2;mouseToDraggableCenterOffsetRef.current={x:e2.clientX-currentDraggableCenterX,y:e2.clientY-currentDraggableCenterY},window.addEventListener("mousemove",mouseMoveHandler,{capture:!0}),window.addEventListener("mouseup",mouseUpHandler,{capture:!0})},[mouseMoveHandler,mouseUpHandler]);panel.y(()=>{const elementToListenOn=dragInitiatorNode||movingElementNode;return elementToListenOn&&elementToListenOn.addEventListener("mousedown",mouseDownHandler),()=>{elementToListenOn&&elementToListenOn.removeEventListener("mousedown",mouseDownHandler),isDraggingRef.current&&(onDragEnd&&onDragEnd(),isDraggingRef.current=!1,movingElementNode&&(movingElementNode.style.userSelect=""),document.body.style.userSelect="",document.body.style.cursor="",window.removeEventListener("mousemove",mouseMoveHandler,{capture:!0}),window.removeEventListener("mouseup",mouseUpHandler,{capture:!0}))}},[movingElementNode,dragInitiatorNode,mouseDownHandler,onDragEnd,mouseMoveHandler,mouseUpHandler]),panel.y(()=>{movingElementRef.current&&providerData&&providerData.borderLocation&&persistedRelativeCenterRef.current&&!isDraggingRef.current&&!hasAnimatedOnceRef.current&&requestAnimationFrame(()=>{movingElementRef.current&&updateDraggablePosition()})},[movingElementNode,providerData,config.initialRelativeCenter,initialSnapArea,updateDraggablePosition]);const draggableRefCallback=panel.q(node=>{setMovingElementNode(node),movingElementRef.current=node},[]),handleRefCallback=panel.q(node=>{setDragInitiatorNode(node),dragInitiatorRef.current=node},[]);return{draggableRef:draggableRefCallback,handleRef:handleRefCallback,position:{snapArea:currentSnapArea,isTopHalf:persistedRelativeCenterRef.current?persistedRelativeCenterRef.current.y<=.5:!0,isLeftHalf:persistedRelativeCenterRef.current?persistedRelativeCenterRef.current.x<=.5:!0},wasDragged}}function ToolbarSection({children}){return pluginUi_jsxRuntime.u("div",{className:"fade-in slide-in-from-right-2 flex max-h-sm max-w-full animate-in snap-start flex-col items-center justify-between gap-1 py-0.5",children})}function ToolbarItem(props){return pluginUi_jsxRuntime.u("div",{className:"relative flex w-full shrink-0 items-center justify-center",children:[props.children,props.badgeContent&&pluginUi_jsxRuntime.u("div",{className:panel.cn("bg-blue-600 text-white",props.badgeClassName,"pointer-events-none absolute right-0 bottom-0 flex h-3 w-max min-w-3 max-w-8 select-none items-center justify-center truncate rounded-full px-0.5 font-semibold text-[0.5em]"),children:props.badgeContent}),props.statusDot&&pluginUi_jsxRuntime.u("div",{className:panel.cn("bg-rose-600",props.statusDotClassName,"pointer-events-none absolute top-0 right-0 size-1.5 rounded-full")})]})}const ToolbarButton=panel.D(({badgeContent,badgeClassName,statusDot,statusDotClassName,tooltipHint,variant="default",active,...props},ref)=>{const button=pluginUi_jsxRuntime.u(H$1,{ref,...props,className:panel.cn("flex items-center justify-center rounded-full p-1 text-zinc-950 ring ring-transparent transition-all duration-150 hover:bg-zinc-950/5",variant==="default"?"size-8":"h-8 rounded-full",active&&"bg-white/40 ring-zinc-950/20",props.className)});return pluginUi_jsxRuntime.u(ToolbarItem,{badgeContent,badgeClassName,statusDot,statusDotClassName,children:button})});ToolbarButton.displayName="ToolbarButton";const Logo=({color="default",loading=!1,loadingSpeed="slow",...props})=>{const colorStyle={default:"fill-stagewise-700 stroke-none",black:"fill-zinc-950 stroke-none",white:"fill-white stroke-none",zinc:"fill-zinc-500/50 stroke-none",current:"fill-current stroke-none",gradient:"fill-white stroke-black/30 stroke-1"};return pluginUi_jsxRuntime.u("div",{className:`relative ${color==="gradient"?"overflow-hidden rounded-full":"overflow-visible"} ${props.className||""} ${loading?"drop-shadow-xl":""} aspect-square`,children:[color==="gradient"&&pluginUi_jsxRuntime.u("div",{className:"absolute inset-0",children:[pluginUi_jsxRuntime.u("div",{className:"absolute inset-0 size-full bg-gradient-to-tr from-indigo-700 via-blue-500 to-teal-500"}),pluginUi_jsxRuntime.u("div",{className:"absolute top-1/2 left-1/2 size-9/12 bg-[radial-gradient(circle,rgba(219,39,119,0.2)_0%,rgba(219,39,119,0)_100%)]"}),pluginUi_jsxRuntime.u("div",{className:"absolute right-1/2 bottom-1/2 size-full bg-[radial-gradient(circle,rgba(219,39,119,0.2)_0%,rgba(219,39,119,0)_100%)]"}),pluginUi_jsxRuntime.u("div",{className:"absolute top-0 left-[-10%] size-[120%] bg-[radial-gradient(circle,rgba(255,255,255,0)_60%,rgba(255,255,255,0.2)_70%)]"}),pluginUi_jsxRuntime.u("div",{className:"absolute top-[-20%] left-0 h-[120%] w-full bg-[radial-gradient(circle,rgba(55,48,163,0)_55%,rgba(55,48,163,0.35)_73%)]"})]}),pluginUi_jsxRuntime.u("svg",{className:`absolute overflow-visible ${color==="gradient"?"top-[25%] left-[25%] h-[50%] w-[50%] drop-shadow-indigo-950 drop-shadow-xs":"top-0 left-0 h-full w-full"}`,viewBox:"0 0 2048 2048",children:[pluginUi_jsxRuntime.u("title",{children:"stagewise"}),pluginUi_jsxRuntime.u("ellipse",{className:colorStyle[color]+(loading?" animate-pulse":""),id:"path3",ry:"624",rx:"624",cy:"1024",cx:"1024"})]}),pluginUi_jsxRuntime.u("svg",{className:`absolute overflow-visible ${color==="gradient"?"top-[25%] left-[25%] h-[50%] w-[50%]":"top-0 left-0 h-full w-full"}`,viewBox:"0 0 2048 2048",children:pluginUi_jsxRuntime.u("path",{id:"path4",className:`origin-center ${colorStyle[color]}${loading?loadingSpeed==="fast"?" animate-spin-fast":" animate-spin-slow":""}`,d:"M 1024 0 A 1024 1024 0 0 0 0 1024 A 1024 1024 0 0 0 1024 2048 L 1736 2048 L 1848 2048 C 1958.7998 2048 2048 1958.7998 2048 1848 L 2048 1736 L 2048 1024 A 1024 1024 0 0 0 1024 0 z M 1024.9414 200 A 824 824 0 0 1 1848.9414 1024 A 824 824 0 0 1 1024.9414 1848 A 824 824 0 0 1 200.94141 1024 A 824 824 0 0 1 1024.9414 200 z "})})]})},SettingsButton=({onOpenPanel,isActive=!1})=>pluginUi_jsxRuntime.u(ToolbarSection,{children:pluginUi_jsxRuntime.u(ToolbarButton,{onClick:onOpenPanel,active:isActive,children:pluginUi_jsxRuntime.u(Settings,{className:"size-4"})})}),SettingsPanel=({onClose})=>pluginUi_jsxRuntime.u(panel.Panel,{children:[pluginUi_jsxRuntime.u(panel.Panel.Header,{title:"Settings"}),pluginUi_jsxRuntime.u(panel.Panel.Content,{children:pluginUi_jsxRuntime.u(ConnectionSettings,{})}),pluginUi_jsxRuntime.u(panel.Panel.Content,{children:pluginUi_jsxRuntime.u(ProjectInfoSection,{})})]}),ConnectionSettings=()=>{const{windows,isDiscovering,discoveryError,discover,selectedSession,selectSession}=panel.useVSCode(),handleSessionChange=e2=>{const target=e2.target,selectedSessionId=target.value===""?void 0:target.value;selectSession(selectedSessionId)},{appName}=panel.useVSCode(),handleRefresh=()=>{discover()};return pluginUi_jsxRuntime.u("div",{className:"space-y-4 pb-4",children:[pluginUi_jsxRuntime.u("div",{children:[pluginUi_jsxRuntime.u("label",{htmlFor:"session-select",className:"mb-2 block font-medium text-sm text-zinc-700",children:["IDE Window ",appName&&`(${appName})`]}),pluginUi_jsxRuntime.u("div",{className:"flex w-full items-center space-x-2",children:[pluginUi_jsxRuntime.u("select",{id:"session-select",value:(selectedSession==null?void 0:selectedSession.sessionId)||"",onChange:handleSessionChange,className:"h-8 min-w-0 flex-1 rounded-lg border border-zinc-300 bg-zinc-500/10 px-3 text-sm backdrop-saturate-150 focus:border-zinc-500 focus:outline-none",disabled:isDiscovering,children:[pluginUi_jsxRuntime.u("option",{value:"",disabled:!0,children:windows.length>0?"Select an IDE window...":"No windows available"}),windows.map(window2=>pluginUi_jsxRuntime.u("option",{value:window2.sessionId,children:[window2.displayName," - Port ",window2.port]},window2.sessionId))]}),pluginUi_jsxRuntime.u("button",{type:"button",onClick:handleRefresh,disabled:isDiscovering,className:"flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-lg bg-zinc-500/10 backdrop-saturate-150 transition-colors hover:bg-zinc-500/20 disabled:opacity-50",title:"Refresh window list",children:pluginUi_jsxRuntime.u(RefreshCw,{className:`size-4 ${isDiscovering?"animate-spin":""}`})})]}),discoveryError&&pluginUi_jsxRuntime.u("p",{className:"mt-1 text-red-600 text-sm",children:["Error discovering windows: ",discoveryError]}),!isDiscovering&&windows.length===0&&!discoveryError&&pluginUi_jsxRuntime.u("p",{className:"mt-1 text-sm text-zinc-500",children:"No IDE windows found. Make sure the Stagewise extension is installed and running."})]}),selectedSession&&pluginUi_jsxRuntime.u("div",{className:"rounded-lg bg-blue-50 p-3",children:[pluginUi_jsxRuntime.u("p",{className:"text-blue-800 text-sm",children:[pluginUi_jsxRuntime.u("strong",{children:"Selected:"})," ",selectedSession.displayName]}),pluginUi_jsxRuntime.u("p",{className:"mt-1 text-blue-600 text-xs",children:["Session ID: ",selectedSession.sessionId.substring(0,8),"..."]})]}),!selectedSession&&windows.length>0&&pluginUi_jsxRuntime.u("div",{className:"rounded-lg bg-amber-50 p-3",children:pluginUi_jsxRuntime.u("p",{className:"text-amber-800 text-sm",children:[pluginUi_jsxRuntime.u("strong",{children:"No window selected:"})," Please select an IDE window above to connect."]})})]})},ProjectInfoSection=()=>pluginUi_jsxRuntime.u("div",{className:"space-y-2 text-xs text-zinc-700",children:[pluginUi_jsxRuntime.u("div",{className:"my-2 flex flex-wrap items-center gap-3",children:[pluginUi_jsxRuntime.u("a",{href:"https://github.com/stagewise-io/stagewise",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1 text-blue-700 hover:underline",title:"GitHub Repository",children:[pluginUi_jsxRuntime.u("svg",{width:"16",height:"16",fill:"currentColor",viewBox:"0 0 24 24",children:pluginUi_jsxRuntime.u("path",{d:"M12 .5C5.73.5.5 5.73.5 12c0 5.08 3.29 9.39 7.86 10.91.58.11.79-.25.79-.56 0-.28-.01-1.02-.02-2-3.2.7-3.88-1.54-3.88-1.54-.53-1.34-1.3-1.7-1.3-1.7-1.06-.72.08-.71.08-.71 1.17.08 1.78 1.2 1.78 1.2 1.04 1.78 2.73 1.27 3.4.97.11-.75.41-1.27.74-1.56-2.56-.29-5.26-1.28-5.26-5.7 0-1.26.45-2.29 1.19-3.1-.12-.29-.52-1.46.11-3.05 0 0 .98-.31 3.2 1.18a11.1 11.1 0 0 1 2.92-.39c.99 0 1.99.13 2.92.39 2.22-1.49 3.2-1.18 3.2-1.18.63 1.59.23 2.76.11 3.05.74.81 1.19 1.84 1.19 3.1 0 4.43-2.7 5.41-5.27 5.7.42.36.79 1.08.79 2.18 0 1.57-.01 2.84-.01 3.23 0 .31.21.68.8.56C20.71 21.39 24 17.08 24 12c0-6.27-5.23-11.5-12-11.5z"})}),"GitHub"]}),pluginUi_jsxRuntime.u("a",{href:"https://discord.gg/gkdGsDYaKA",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1 text-indigo-700 hover:underline",title:"Join our Discord",children:[pluginUi_jsxRuntime.u("svg",{width:"16",height:"16",fill:"currentColor",viewBox:"0 0 24 24",children:pluginUi_jsxRuntime.u("path",{d:"M20.317 4.369A19.791 19.791 0 0 0 16.885 3.2a.117.117 0 0 0-.124.06c-.537.96-1.13 2.22-1.552 3.2a18.524 18.524 0 0 0-5.418 0c-.423-.98-1.016-2.24-1.553-3.2a.117.117 0 0 0-.124-.06A19.736 19.736 0 0 0 3.683 4.369a.105.105 0 0 0-.047.043C.533 9.043-.32 13.579.099 18.057a.12.12 0 0 0 .045.083c1.934 1.426 3.81 2.288 5.671 2.857a.116.116 0 0 0 .127-.043c.438-.602.827-1.24 1.165-1.908a.112.112 0 0 0-.062-.158c-.619-.234-1.205-.52-1.77-.853a.117.117 0 0 1-.012-.194c.119-.09.238-.183.353-.277a.112.112 0 0 1 .114-.013c3.747 1.71 7.789 1.71 11.533 0a.112.112 0 0 1 .115.012c.115.094.234.188.353.278a.117.117 0 0 1-.012.194c-.565.333-1.151.619-1.77.853a.112.112 0 0 0-.062.158c.34.668.728 1.306 1.165 1.908a.115.115 0 0 0 .127.043c1.861-.569 3.737-1.431 5.671-2.857a.12.12 0 0 0 .045-.083c.5-5.177-.838-9.673-3.636-13.645a.105.105 0 0 0-.047-.043zM8.02 15.331c-1.183 0-2.156-1.085-2.156-2.419 0-1.333.955-2.418 2.156-2.418 1.21 0 2.175 1.095 2.156 2.418 0 1.334-.955 2.419-2.156 2.419zm7.96 0c-1.183 0-2.156-1.085-2.156-2.419 0-1.333.955-2.418 2.156-2.418 1.21 0 2.175 1.095 2.156 2.418 0 1.334-.946 2.419-2.156 2.419z"})}),"Discord"]}),pluginUi_jsxRuntime.u("a",{href:"https://marketplace.visualstudio.com/items?itemName=stagewise.stagewise-vscode-extension",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1 text-violet-700 hover:underline",title:"VS Code Marketplace",children:[pluginUi_jsxRuntime.u("svg",{width:"16",height:"16",fill:"currentColor",viewBox:"0 0 24 24",children:pluginUi_jsxRuntime.u("path",{d:"M21.805 2.29a2.25 2.25 0 0 0-2.45-.49l-7.5 3.25a2.25 2.25 0 0 0-1.31 2.06v1.13l-5.13 2.22a2.25 2.25 0 0 0-1.31 2.06v3.5a2.25 2.25 0 0 0 1.31 2.06l5.13 2.22v1.13a2.25 2.25 0 0 0 1.31 2.06l7.5 3.25a2.25 2.25 0 0 0 2.45-.49A2.25 2.25 0 0 0 23 20.25V3.75a2.25 2.25 0 0 0-1.195-1.46zM12 20.25v-16.5l7.5 3.25v10l-7.5 3.25z"})}),"VS Code Marketplace"]})]}),pluginUi_jsxRuntime.u("div",{className:"mt-2",children:[pluginUi_jsxRuntime.u("span",{className:"font-semibold",children:"Contact:"})," ",pluginUi_jsxRuntime.u("a",{href:"mailto:<EMAIL>",className:"text-blue-700 hover:underline",children:"<EMAIL>"})]}),pluginUi_jsxRuntime.u("div",{className:"mt-2 text-zinc-500",children:pluginUi_jsxRuntime.u("span",{children:["Licensed under AGPL v3."," ",pluginUi_jsxRuntime.u("a",{href:"https://github.com/stagewise-io/stagewise/blob/main/LICENSE",target:"_blank",rel:"noopener noreferrer",className:"hover:underline",children:"View license"})]})})]});function DisconnectedStatePanel({discover,discoveryError}){return pluginUi_jsxRuntime.u("div",{className:"rounded-lg border border-orange-200 bg-orange-50/90 p-4 shadow-lg backdrop-blur",children:[pluginUi_jsxRuntime.u("div",{className:"mb-3 flex items-center gap-3",children:[pluginUi_jsxRuntime.u(WifiOff,{className:"size-5 text-orange-600"}),pluginUi_jsxRuntime.u("h3",{className:"font-semibold text-orange-800",children:"Not Connected"})]}),pluginUi_jsxRuntime.u("div",{className:"space-y-3 text-orange-700 text-sm",children:[pluginUi_jsxRuntime.u("p",{children:"The stagewise toolbar isn't connected to any IDE window."}),discoveryError&&pluginUi_jsxRuntime.u("div",{className:"rounded border border-red-200 bg-red-100 p-2 text-red-700",children:[pluginUi_jsxRuntime.u("strong",{children:"Error:"})," ",discoveryError]}),pluginUi_jsxRuntime.u("div",{className:"space-y-2",children:[pluginUi_jsxRuntime.u("p",{className:"font-medium",children:"To connect:"}),pluginUi_jsxRuntime.u("ol",{className:"list-inside list-decimal space-y-1 pl-2 text-xs",children:[pluginUi_jsxRuntime.u("li",{children:"Open your IDE (Cursor, Windsurf, etc.)"}),pluginUi_jsxRuntime.u("li",{children:"Install the stagewise extension"}),pluginUi_jsxRuntime.u("li",{children:"Make sure the extension is active"}),pluginUi_jsxRuntime.u("li",{children:"Click refresh below"})]})]}),pluginUi_jsxRuntime.u("button",{type:"button",onClick:discover,className:"flex w-full items-center justify-center gap-2 rounded-md bg-orange-600 px-3 py-2 font-medium text-sm text-white transition-colors hover:bg-orange-700",children:[pluginUi_jsxRuntime.u(RefreshCw,{className:"size-4"}),"Retry Connection"]}),pluginUi_jsxRuntime.u("div",{className:"border-orange-200 border-t pt-2",children:pluginUi_jsxRuntime.u("a",{href:"https://marketplace.visualstudio.com/items?itemName=stagewise.stagewise-vscode-extension",target:"_blank",rel:"noopener noreferrer",className:"text-orange-600 text-xs hover:text-orange-800 hover:underline",children:"Get VS Code Extension →"})})]})]})}function ConnectingStatePanel(){return pluginUi_jsxRuntime.u("div",{className:"rounded-lg border border-blue-200 bg-blue-50/90 p-4 shadow-lg backdrop-blur",children:[pluginUi_jsxRuntime.u("div",{className:"mb-3 flex items-center gap-3",children:[pluginUi_jsxRuntime.u(RefreshCw,{className:"size-5 animate-spin text-blue-600"}),pluginUi_jsxRuntime.u("h3",{className:"font-semibold text-blue-800",children:"Connecting..."})]}),pluginUi_jsxRuntime.u("div",{className:"text-blue-700 text-sm",children:pluginUi_jsxRuntime.u("p",{children:["Looking for active agent instances...",pluginUi_jsxRuntime.u("br",{}),pluginUi_jsxRuntime.u("span",{className:"text-blue-500 text-xs",children:"VS Code, Cursor, Windsurf ..."})]})})]})}function WindowSelectionPanel(){const{windows,isDiscovering,discoveryError,discover,selectedSession,selectSession,appName}=panel.useVSCode(),handleSessionChange=e2=>{const target=e2.target,selectedSessionId=target.value===""?void 0:target.value;selectSession(selectedSessionId)},handleRefresh=()=>{discover()};return pluginUi_jsxRuntime.u("div",{className:"rounded-lg border border-blue-200 bg-blue-50/90 p-4 shadow-lg backdrop-blur",children:[pluginUi_jsxRuntime.u("div",{className:"mb-3",children:pluginUi_jsxRuntime.u("h3",{className:"font-semibold text-blue-800",children:"Select IDE Window"})}),pluginUi_jsxRuntime.u("div",{className:"space-y-3",children:[pluginUi_jsxRuntime.u("div",{children:[pluginUi_jsxRuntime.u("label",{htmlFor:"window-selection-select",className:"mb-2 block font-medium text-blue-700 text-sm",children:["IDE Window ",appName&&`(${appName})`]}),pluginUi_jsxRuntime.u("div",{className:"flex w-full items-center space-x-2",children:[pluginUi_jsxRuntime.u("select",{id:"window-selection-select",value:(selectedSession==null?void 0:selectedSession.sessionId)||"",onChange:handleSessionChange,className:"h-8 min-w-0 flex-1 rounded-lg border border-blue-300 bg-white/80 px-3 text-sm backdrop-saturate-150 focus:border-blue-500 focus:outline-none",disabled:isDiscovering,children:[pluginUi_jsxRuntime.u("option",{value:"",disabled:!0,children:windows.length>0?"Select an IDE window...":"No windows available"}),windows.map(window2=>pluginUi_jsxRuntime.u("option",{value:window2.sessionId,children:[window2.displayName," - Port ",window2.port]},window2.sessionId))]}),pluginUi_jsxRuntime.u("button",{type:"button",onClick:handleRefresh,disabled:isDiscovering,className:"flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-lg bg-blue-100/80 backdrop-saturate-150 transition-colors hover:bg-blue-200/80 disabled:opacity-50",title:"Refresh window list",children:pluginUi_jsxRuntime.u(RefreshCw,{className:`size-4 text-blue-600 ${isDiscovering?"animate-spin":""}`})})]}),discoveryError&&pluginUi_jsxRuntime.u("p",{className:"mt-1 text-red-600 text-sm",children:["Error discovering windows: ",discoveryError]}),!isDiscovering&&windows.length===0&&!discoveryError&&pluginUi_jsxRuntime.u("p",{className:"mt-1 text-blue-600 text-sm",children:"No IDE windows found. Make sure the Stagewise extension is installed and running."})]}),selectedSession&&pluginUi_jsxRuntime.u("div",{className:"rounded-lg bg-blue-100/80 p-3",children:[pluginUi_jsxRuntime.u("p",{className:"text-blue-800 text-sm",children:[pluginUi_jsxRuntime.u("strong",{children:"Selected:"})," ",selectedSession.displayName]}),pluginUi_jsxRuntime.u("p",{className:"mt-1 text-blue-600 text-xs",children:["Session ID: ",selectedSession.sessionId.substring(0,8),"..."]})]}),!selectedSession&&pluginUi_jsxRuntime.u("div",{className:"rounded-lg border border-blue-200 bg-white/90 p-3",children:pluginUi_jsxRuntime.u("p",{className:"text-blue-800 text-sm",children:[pluginUi_jsxRuntime.u("strong",{children:"No window selected:"})," Please select an IDE window above to connect."]})})]})]})}function NormalStateButtons({handleButtonClick,pluginBox,setPluginBox,openPanel,setOpenPanel,chatState}){const pluginsWithActions=panel.usePlugins().plugins.filter(plugin=>plugin.onActionClick),handleOpenSettings=()=>setOpenPanel(openPanel==="settings"?null:"settings");return pluginUi_jsxRuntime.u(pluginUi_jsxRuntime.k,{children:[pluginUi_jsxRuntime.u(SettingsButton,{onOpenPanel:handleOpenSettings,isActive:openPanel==="settings"}),pluginsWithActions.length>0&&pluginUi_jsxRuntime.u(ToolbarSection,{children:pluginsWithActions.map(plugin=>pluginUi_jsxRuntime.u(ToolbarButton,{onClick:handleButtonClick(()=>{(pluginBox==null?void 0:pluginBox.pluginName)!==plugin.pluginName?plugin.onActionClick()&&setPluginBox({component:plugin.onActionClick(),pluginName:plugin.pluginName}):setPluginBox(null)}),active:(pluginBox==null?void 0:pluginBox.pluginName)===plugin.pluginName,children:plugin.iconSvg?pluginUi_jsxRuntime.u("span",{className:"size-4 stroke-zinc-950 text-zinc-950 *:size-full",children:plugin.iconSvg}):pluginUi_jsxRuntime.u(Puzzle,{className:"size-4"})},plugin.pluginName))}),pluginUi_jsxRuntime.u(ToolbarSection,{children:pluginUi_jsxRuntime.u(ToolbarButton,{onClick:handleButtonClick(()=>chatState.isPromptCreationActive?chatState.stopPromptCreation():chatState.startPromptCreation()),active:chatState.isPromptCreationActive,children:pluginUi_jsxRuntime.u(MessageCircle,{className:"size-4 stroke-zinc-950"})})})]})}function DisconnectedStateButtons(){const{discover,isDiscovering}=panel.useVSCode();return pluginUi_jsxRuntime.u(ToolbarSection,{children:pluginUi_jsxRuntime.u(ToolbarButton,{onClick:isDiscovering?void 0:()=>discover(),className:panel.cn(isDiscovering?"text-blue-700":"text-orange-700 hover:bg-orange-200"),children:pluginUi_jsxRuntime.u(RefreshCw,{className:panel.cn("size-4",isDiscovering&&"animate-spin")})})})}function ToolbarDraggableBox(){const provider=panel.x(DraggableContext),borderLocation=provider==null?void 0:provider.borderLocation,isReady=!!borderLocation&&borderLocation.right-borderLocation.left>0&&borderLocation.bottom-borderLocation.top>0,draggable=useDraggable({startThreshold:10,initialSnapArea:"bottomRight"}),{windows,isDiscovering,discoveryError,discover,shouldPromptWindowSelection}=panel.useVSCode(),isConnected=windows.length>0,[pluginBox,setPluginBox]=panel.d(null),[openPanel,setOpenPanel]=panel.d(null),chatState=useChatState(),{minimized,minimize,expand}=useAppState();panel.y(()=>{minimized&&(setPluginBox(null),setOpenPanel(null))},[minimized]);const handleButtonClick=handler=>e2=>{if(draggable.wasDragged){e2.preventDefault(),e2.stopPropagation();return}handler()};if(!isReady)return null;const isLoadingState=isDiscovering,isDisconnectedState=!isConnected&&!isDiscovering,isConnectedState=isConnected,shouldShowWindowSelection=shouldPromptWindowSelection&&isConnectedState,theme=isLoadingState?{border:"border-blue-300",bg:"bg-blue-100/80",divideBorder:"divide-blue-200",buttonBg:"from-blue-600 to-sky-600",buttonColor:"text-blue-700"}:isDisconnectedState?{border:"border-orange-300",bg:"bg-orange-100/80",divideBorder:"divide-orange-200",buttonBg:"from-orange-600 to-red-600",buttonColor:"text-orange-700"}:{border:"border-border/30",bg:"bg-zinc-50/80",divideBorder:"divide-border/20",buttonBg:"from-sky-700 to-fuchsia-700",buttonColor:"stroke-zinc-950"},getMinimizedIcon=()=>isLoadingState?pluginUi_jsxRuntime.u(RefreshCw,{className:"size-4 animate-spin text-white"}):isDisconnectedState?pluginUi_jsxRuntime.u(WifiOff,{className:"size-4 text-white"}):pluginUi_jsxRuntime.u(Logo,{className:"size-4.5",color:"white"});return pluginUi_jsxRuntime.u("div",{ref:draggable.draggableRef,className:"absolute p-0.5",children:[pluginUi_jsxRuntime.u("div",{className:panel.cn("absolute flex h-[calc(100vh-32px)] w-96 max-w-[40vw] items-stretch justify-end transition-all duration-300 ease-out",draggable.position.isTopHalf?"top-0 flex-col-reverse":"bottom-0 flex-col",draggable.position.isLeftHalf?"left-[100%]":"right-[100%]"),children:[pluginUi_jsxRuntime.u("div",{className:panel.cn("flex min-h-0 flex-1 origin-bottom-right flex-col items-stretch px-2 transition-all duration-300 ease-out",(pluginBox||openPanel==="settings"||!isConnectedState||shouldShowWindowSelection)&&!minimized?"pointer-events-auto scale-100 opacity-100 blur-none":"pointer-events-none h-0 scale-50 opacity-0 blur-md",draggable.position.isTopHalf?"justify-start":"justify-end",draggable.position.isTopHalf?draggable.position.isLeftHalf?"origin-top-left":"origin-top-right":draggable.position.isLeftHalf?"origin-bottom-left":"origin-bottom-right"),children:[isLoadingState&&pluginUi_jsxRuntime.u(ConnectingStatePanel,{}),isDisconnectedState&&pluginUi_jsxRuntime.u(DisconnectedStatePanel,{discover,discoveryError}),shouldShowWindowSelection&&pluginUi_jsxRuntime.u(WindowSelectionPanel,{}),isConnectedState&&openPanel==="settings"&&!shouldShowWindowSelection&&pluginUi_jsxRuntime.u(SettingsPanel,{onClose:()=>setOpenPanel(null)}),isConnectedState&&!shouldShowWindowSelection&&(pluginBox==null?void 0:pluginBox.component)]}),isConnectedState&&pluginUi_jsxRuntime.u("div",{className:panel.cn("z-20 w-full px-2 transition-all duration-300 ease-out",chatState.isPromptCreationActive&&!minimized?"pointer-events-auto scale-100 opacity-100 blur-none":"pointer-events-none h-0 scale-50 opacity-0 blur-md",draggable.position.isTopHalf?"mb-2":"mt-2",draggable.position.isTopHalf?draggable.position.isLeftHalf?"origin-top-left":"origin-top-right":draggable.position.isLeftHalf?"origin-bottom-left":"origin-bottom-right"),children:pluginUi_jsxRuntime.u(ToolbarChatArea,{})})]}),pluginUi_jsxRuntime.u("div",{ref:draggable.handleRef,className:panel.cn("pointer-events-auto z-50 rounded-full border px-0.5 shadow-md backdrop-blur transition-all duration-300 ease-out",theme.border,theme.bg,draggable.position.isTopHalf?"flex-col-reverse divide-y-reverse":"flex-col",minimized?"h-9.5 w-9.5":"h-[calc-size(auto,size)] h-auto w-auto"),children:[pluginUi_jsxRuntime.u(H$1,{onClick:()=>expand(),className:panel.cn("absolute right-0 left-0 z-50 flex size-9 origin-center cursor-pointer items-center justify-center rounded-full bg-gradient-to-tr transition-all duration-300 ease-out",theme.buttonBg,minimized?"pointer-events-auto scale-100 opacity-100 blur-none":"pointer-events-none scale-25 opacity-0 blur-md",draggable.position.isTopHalf?"top-0":"bottom-0"),children:getMinimizedIcon()}),pluginUi_jsxRuntime.u("div",{className:panel.cn("flex h-[calc-size(auto)] scale-100 items-center justify-center divide-y transition-all duration-300 ease-out",theme.divideBorder,draggable.position.isTopHalf?"origin-top flex-col-reverse divide-y-reverse":"origin-bottom flex-col",minimized&&"pointer-events-none h-0 scale-50 opacity-0 blur-md"),children:[isConnectedState?pluginUi_jsxRuntime.u(NormalStateButtons,{handleButtonClick,pluginBox,setPluginBox,openPanel,setOpenPanel,chatState}):pluginUi_jsxRuntime.u(DisconnectedStateButtons,{}),pluginUi_jsxRuntime.u(ToolbarSection,{children:pluginUi_jsxRuntime.u(ToolbarButton,{onClick:handleButtonClick(()=>minimize()),className:panel.cn("h-5",theme.buttonColor,draggable.position.isTopHalf?"rounded-t-3xl rounded-b-lg":"rounded-t-lg rounded-b-3xl"),children:draggable.position.isTopHalf?pluginUi_jsxRuntime.u(ChevronUp,{className:"size-4"}):pluginUi_jsxRuntime.u(ChevronDown,{className:"size-4"})})})]})]})]})}function ToolbarArea(){const containerRef=panel.A(null);return pluginUi_jsxRuntime.u("div",{className:"absolute size-full",children:pluginUi_jsxRuntime.u("div",{className:"absolute inset-4",ref:containerRef,children:pluginUi_jsxRuntime.u(DraggableProvider,{containerRef,snapAreas:{topLeft:!0,topRight:!0,bottomLeft:!0,bottomRight:!0,topCenter:!0,bottomCenter:!0},children:pluginUi_jsxRuntime.u(ToolbarDraggableBox,{})})})})}function ElementSelector(props){const lastHoveredElement=panel.A(null),handleMouseMove=panel.q(event=>{if(event.target.closest(".companion"))return;const refElement=panel.getElementAtPoint(event.clientX,event.clientY);props.ignoreList.includes(refElement)||lastHoveredElement.current!==refElement&&(lastHoveredElement.current=refElement,props.onElementHovered(refElement))},[props]),handleMouseLeave=panel.q(()=>{lastHoveredElement.current=null,props.onElementUnhovered()},[props]),handleMouseClick=panel.q(()=>{lastHoveredElement.current&&(props.ignoreList.includes(lastHoveredElement.current)||props.onElementSelected(lastHoveredElement.current))},[props]);return pluginUi_jsxRuntime.u("div",{className:"pointer-events-auto fixed inset-0 h-screen w-screen cursor-copy",onMouseMove:handleMouseMove,onMouseLeave:handleMouseLeave,onClick:handleMouseClick,role:"button",tabIndex:0})}function useWindowSize(){const[size,setSize]=panel.d({width:window.innerWidth,height:window.innerHeight}),handleResize=panel.q(()=>setSize({width:window.innerWidth,height:window.innerHeight}),[]);return useEventListener("resize",handleResize),size}function useCyclicUpdate(func,frameRate){const animationFrameHandle=panel.A(void 0),timeBetweenFrames=panel.T(()=>1e3/frameRate,[frameRate]),lastCallFrameTime=panel.A(0),update=panel.q(frameTime=>{frameTime-lastCallFrameTime.current>=timeBetweenFrames&&(func(),lastCallFrameTime.current=frameTime),animationFrameHandle.current=requestAnimationFrame(update)},[func,timeBetweenFrames]);panel.y(()=>(animationFrameHandle.current=requestAnimationFrame(update),()=>{animationFrameHandle.current&&(cancelAnimationFrame(animationFrameHandle.current),animationFrameHandle.current=void 0)}),[frameRate,update])}function ContextItemProposal({refElement,...props}){const boxRef=panel.A(null),windowSize=useWindowSize(),{plugins}=panel.usePlugins(),hoveredElementPluginContext=panel.T(()=>refElement?plugins.filter(plugin=>plugin.onContextElementSelect).map(plugin=>{var _a;return{pluginName:plugin.pluginName,context:(_a=plugin.onContextElementSelect)==null?void 0:_a.call(plugin,refElement)}}):[],[refElement]),updateBoxPosition=panel.q(()=>{if(boxRef.current)if(refElement){const referenceRect=refElement.getBoundingClientRect();boxRef.current.style.top=`${referenceRect.top-2}px`,boxRef.current.style.left=`${referenceRect.left-2}px`,boxRef.current.style.width=`${referenceRect.width+4}px`,boxRef.current.style.height=`${referenceRect.height+4}px`,boxRef.current.style.display=void 0}else boxRef.current.style.height="0px",boxRef.current.style.width="0px",boxRef.current.style.top=`${windowSize.height/2}px`,boxRef.current.style.left=`${windowSize.width/2}px`,boxRef.current.style.display="none"},[refElement,windowSize.height,windowSize.width]);return useCyclicUpdate(updateBoxPosition,30),pluginUi_jsxRuntime.u("div",{...props,className:"fixed flex items-center justify-center rounded-lg border-2 border-blue-600/80 bg-blue-600/20 text-white transition-all duration-100",style:{zIndex:1e3},ref:boxRef,children:[pluginUi_jsxRuntime.u("div",{className:"absolute top-0.5 left-0.5 flex w-full flex-row items-start justify-start gap-1",children:[pluginUi_jsxRuntime.u("div",{className:"flex flex-row items-center justify-center gap-0.5 overflow-hidden rounded-md bg-zinc-700/80 px-1 py-0 font-medium text-white text-xs",children:pluginUi_jsxRuntime.u("span",{className:"truncate",children:refElement.tagName.toLowerCase()})}),hoveredElementPluginContext.filter(plugin=>plugin.context.annotation).map(plugin=>{var _a;return pluginUi_jsxRuntime.u("div",{className:"flex flex-row items-center justify-center gap-0.5 overflow-hidden rounded-md bg-zinc-700/80 px-1 py-0 font-medium text-white text-xs",children:[pluginUi_jsxRuntime.u("span",{className:"size-3 shrink-0 stroke-white text-white *:size-full",children:(_a=plugins.find(p2=>p2.pluginName===plugin.pluginName))==null?void 0:_a.iconSvg}),pluginUi_jsxRuntime.u("span",{className:"truncate",children:plugin.context.annotation})]})})]}),pluginUi_jsxRuntime.u(Plus,{className:"size-6 drop-shadow-black drop-shadow-md"})]})}function ContextItem({refElement,...props}){const boxRef=panel.A(null),windowSize=useWindowSize(),updateBoxPosition=panel.q(()=>{if(boxRef.current)if(refElement){const referenceRect=refElement.getBoundingClientRect();boxRef.current.style.top=`${referenceRect.top}px`,boxRef.current.style.left=`${referenceRect.left}px`,boxRef.current.style.width=`${referenceRect.width}px`,boxRef.current.style.height=`${referenceRect.height}px`,boxRef.current.style.display=void 0}else boxRef.current.style.height="0px",boxRef.current.style.width="0px",boxRef.current.style.top=`${windowSize.height/2}px`,boxRef.current.style.left=`${windowSize.width/2}px`,boxRef.current.style.display="none"},[refElement,windowSize.height,windowSize.width]);useCyclicUpdate(updateBoxPosition,30);const chatState=useChatState(),handleDeleteClick=panel.q(()=>{chatState.removeChatDomContext(chatState.currentChatId,refElement)},[chatState,refElement]),{plugins}=panel.usePlugins();return pluginUi_jsxRuntime.u("div",{...props,className:"pointer-events-auto fixed flex cursor-pointer items-center justify-center rounded-lg border-2 border-green-600/80 bg-green-600/5 text-transparent transition-all duration-0 hover:border-red-600/80 hover:bg-red-600/20 hover:text-white",ref:boxRef,onClick:handleDeleteClick,role:"button",tabIndex:0,children:[pluginUi_jsxRuntime.u("div",{className:"absolute top-0.5 left-0.5 flex w-full flex-row items-start justify-start gap-1",children:[pluginUi_jsxRuntime.u("div",{className:"flex flex-row items-center justify-center gap-0.5 overflow-hidden rounded-md bg-zinc-700/80 px-1 py-0 font-medium text-white text-xs",children:pluginUi_jsxRuntime.u("span",{className:"truncate",children:refElement.tagName.toLowerCase()})}),props.pluginContext.filter(plugin=>plugin.context.annotation).map(plugin=>{var _a;return pluginUi_jsxRuntime.u("div",{className:"flex flex-row items-center justify-center gap-0.5 overflow-hidden rounded-md bg-zinc-700/80 px-1 py-0 font-medium text-white text-xs",children:[pluginUi_jsxRuntime.u("span",{className:"size-3 shrink-0 stroke-white text-white *:size-full",children:(_a=plugins.find(p2=>p2.pluginName===plugin.pluginName))==null?void 0:_a.iconSvg}),pluginUi_jsxRuntime.u("span",{className:"truncate",children:plugin.context.annotation})]})})]}),pluginUi_jsxRuntime.u(Trash2,{className:"size-6 drop-shadow-black drop-shadow-md"})]})}function SelectorCanvas(){const{chats,currentChatId,addChatDomContext,isPromptCreationActive,promptState}=useChatState(),currentChat=panel.T(()=>chats.find(chat=>chat.id===currentChatId),[currentChatId,chats]),shouldShow=isPromptCreationActive&&promptState!=="loading",contextElements=panel.T(()=>(currentChat==null?void 0:currentChat.domContextElements)||[],[currentChat]),[hoveredElement,setHoveredElement]=panel.d(null),addElementToContext=panel.q(el=>{addChatDomContext(currentChatId,el)},[addChatDomContext,currentChatId]);return shouldShow?pluginUi_jsxRuntime.u(pluginUi_jsxRuntime.k,{children:[hoveredElement&&pluginUi_jsxRuntime.u(ContextItemProposal,{refElement:hoveredElement}),pluginUi_jsxRuntime.u(ElementSelector,{ignoreList:contextElements.map(el=>el.element),onElementHovered:setHoveredElement,onElementSelected:addElementToContext,onElementUnhovered:()=>setHoveredElement(null)}),contextElements.map(el=>pluginUi_jsxRuntime.u(ContextItem,{refElement:el.element,pluginContext:el.pluginContext}))]}):null}function DesktopLayout(){return pluginUi_jsxRuntime.u("div",{className:panel.cn("fixed inset-0 h-screen w-screen"),children:[pluginUi_jsxRuntime.u(SelectorCanvas,{}),pluginUi_jsxRuntime.u(ToolbarArea,{})]})}function FocusLock(){const focusInCompanion=panel.A(!1);return panel.y(()=>{const originalFocus=HTMLElement.prototype.focus;return HTMLElement.prototype.focus=function(...args){const shadowRoot=this.getRootNode();!(shadowRoot instanceof ShadowRoot&&shadowRoot.host instanceof HTMLElement&&shadowRoot.host.nodeName==="STAGEWISE-COMPANION-ANCHOR")&&focusInCompanion.current||originalFocus.apply(this,args)},()=>{HTMLElement.prototype.focus=originalFocus}},[]),useEventListener("focusin",event=>{event.target.localName===panel.companionAnchorTagName&&(focusInCompanion.current=!0)},{capture:!0}),useEventListener("focusout",event=>{event.target.localName===panel.companionAnchorTagName&&(focusInCompanion.current=!1)},{capture:!0}),null}function VisibilityManager({children}){return children}function MainAppBlocker(){const{isMainAppBlocked}=useAppState();return pluginUi_jsxRuntime.u("div",{className:panel.cn("fixed inset-0 h-screen w-screen",isMainAppBlocked?"pointer-events-auto":"pointer-events-none"),role:"button",tabIndex:0})}function App(config){return pluginUi_jsxRuntime.u(AppStateProvider,{children:[pluginUi_jsxRuntime.u(FocusLock,{}),pluginUi_jsxRuntime.u(MainAppBlocker,{}),pluginUi_jsxRuntime.u(ContextProviders,{config,children:[pluginUi_jsxRuntime.u(HotkeyListener,{}),pluginUi_jsxRuntime.u(VisibilityManager,{children:pluginUi_jsxRuntime.u(DesktopLayout,{})})]})]})}function initToolbar(config){if(!document.body)throw new Error("stagewise companion cannot find document.body");if(document.body.querySelector(panel.companionAnchorTagName))throw console.warn("A stagewise companion anchor already exists. Aborting this instance."),new Error("A stagewise companion anchor already exists.");const shadowDomAnchor=document.createElement(panel.companionAnchorTagName);shadowDomAnchor.style.position="fixed",shadowDomAnchor.style.top="0px",shadowDomAnchor.style.left="0px",shadowDomAnchor.style.right="0px",shadowDomAnchor.style.bottom="0px",shadowDomAnchor.style.pointerEvents="none",shadowDomAnchor.style.zIndex="**********";const eventBlocker=ev=>{ev.stopPropagation()};shadowDomAnchor.onclick=eventBlocker,shadowDomAnchor.onmousedown=eventBlocker,shadowDomAnchor.onmouseup=eventBlocker,shadowDomAnchor.onmousemove=eventBlocker,shadowDomAnchor.ondblclick=eventBlocker,shadowDomAnchor.oncontextmenu=eventBlocker,shadowDomAnchor.onwheel=eventBlocker,shadowDomAnchor.onfocus=eventBlocker,shadowDomAnchor.onblur=eventBlocker,document.body.appendChild(shadowDomAnchor);const fontLinkNode=document.createElement("link");fontLinkNode.rel="stylesheet",fontLinkNode.href="https://rsms.me/inter/inter.css",document.head.appendChild(fontLinkNode);const styleNode=document.createElement("style");styleNode.append(document.createTextNode(appStyle)),document.head.appendChild(styleNode),pluginUi_jsxRuntime.E(pluginUi_jsxRuntime._(App,config),shadowDomAnchor)}exports.initToolbar=initToolbar;
