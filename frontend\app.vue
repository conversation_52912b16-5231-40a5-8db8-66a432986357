<!--
  三只鱼网络科技 | 韩总 | 2024-12-20
  QiyeDIY企业建站系统 - 前端展示根组件
  集成Stagewise可视化编程工具栏
-->

<template>
  <div>
    <!-- Stagewise工具栏 - 仅在开发环境显示 -->
    <ClientOnly>
      <StagewiseToolbar v-if="isDevelopment" :config="stagewiseConfig" />
    </ClientOnly>
    
    <!-- Nuxt页面内容 -->
    <NuxtPage />
  </div>
</template>

<script setup lang="ts">
import { StagewiseToolbar, type ToolbarConfig } from '@stagewise/toolbar-vue'

// 开发环境检测
const isDevelopment = computed(() => {
  return process.env.NODE_ENV === 'development' || process.dev
})

// Stagewise工具栏配置
const stagewiseConfig: ToolbarConfig = {
  plugins: [
    // 可以在这里添加自定义插件配置
  ],
  // 自定义配置选项
  theme: 'light', // 或 'dark'
  position: 'bottom-right', // 工具栏位置
  autoHide: false, // 是否自动隐藏
}

// SEO配置
useHead({
  title: 'QiyeDIY企业建站系统',
  meta: [
    { name: 'description', content: '专业的企业DIY建站系统，让建站变得简单' },
    { name: 'keywords', content: 'DIY建站,企业网站,网站建设,可视化编辑' },
    { property: 'og:title', content: 'QiyeDIY企业建站系统' },
    { property: 'og:description', content: '专业的企业DIY建站系统，让建站变得简单' },
    { property: 'og:type', content: 'website' },
  ],
  link: [
    { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
  ]
})
</script>

<style>
/* 全局样式 */
html {
  font-family: 'Inter', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

body {
  margin: 0;
  padding: 0;
  background-color: #f8fafc;
  color: #1a202c;
  line-height: 1.6;
}

/* 确保stagewise工具栏在最上层 */
.stagewise-toolbar {
  z-index: 9999 !important;
}
</style>
