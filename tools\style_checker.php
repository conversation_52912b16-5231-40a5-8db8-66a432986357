<?php
/**
 * 三只鱼网络科技 | 韩总 | 2025-06-08
 * 样式适配检查工具 - 确保新功能与现有样式协调
 */

class StyleChecker
{
    private $projectRoot;
    private $cssFiles = [];
    private $designTokens = [];

    public function __construct()
    {
        $this->projectRoot = dirname(__DIR__);
        $this->loadCSSFiles();
        $this->extractDesignTokens();
    }

    /**
     * 分析现有页面样式规范
     */
    public function analyzePageStyle($pageName = 'index')
    {
        echo "=== 页面样式分析 ===\n";
        echo "页面: {$pageName}\n\n";

        $templateFile = $this->findTemplateFile($pageName);
        if (!$templateFile) {
            echo "❌ 未找到模板文件\n";
            return false;
        }

        $html = file_get_contents($templateFile);
        
        // 分析布局结构
        $layout = $this->analyzeLayout($html);
        
        // 分析设计规范
        $designRules = $this->analyzeDesignRules();
        
        // 生成样式指南
        $this->generateStyleGuide($pageName, $layout, $designRules);
        
        return [
            'layout' => $layout,
            'design_rules' => $designRules
        ];
    }

    /**
     * 检查新功能样式兼容性
     */
    public function checkStyleCompatibility($newHtml, $newCSS = '')
    {
        echo "=== 样式兼容性检查 ===\n";
        
        $issues = [];
        
        // 检查间距规范
        $spacingIssues = $this->checkSpacing($newHtml, $newCSS);
        $issues = array_merge($issues, $spacingIssues);
        
        // 检查颜色规范
        $colorIssues = $this->checkColors($newCSS);
        $issues = array_merge($issues, $colorIssues);
        
        // 检查字体规范
        $fontIssues = $this->checkFonts($newCSS);
        $issues = array_merge($issues, $fontIssues);
        
        // 检查布局规范
        $layoutIssues = $this->checkLayout($newHtml);
        $issues = array_merge($issues, $layoutIssues);
        
        if (empty($issues)) {
            echo "✅ 样式兼容性检查通过\n";
        } else {
            echo "❌ 发现 " . count($issues) . " 个样式问题:\n";
            foreach ($issues as $issue) {
                echo "  - {$issue}\n";
            }
        }
        
        return $issues;
    }

    /**
     * 检查代码完整性
     */
    public function checkCodeCompleteness($code, $functionality)
    {
        echo "=== 代码完整性检查 ===\n";
        echo "功能: {$functionality}\n\n";
        
        $issues = [];
        
        // 检查数据统计逻辑
        if (strpos($functionality, '统计') !== false || strpos($functionality, '数量') !== false) {
            if (!preg_match('/count\(\)|->count\(\)|COUNT\(/i', $code)) {
                $issues[] = "缺少数据统计逻辑";
            }
        }
        
        // 检查循环逻辑
        if (strpos($code, 'volist') !== false || strpos($code, 'foreach') !== false) {
            if (!preg_match('/\{volist.*?\{\/volist\}|\{foreach.*?\{\/foreach\}/s', $code)) {
                $issues[] = "循环逻辑不完整";
            }
        }
        
        // 检查条件判断
        if (strpos($code, '{if') !== false) {
            $ifCount = substr_count($code, '{if');
            $endifCount = substr_count($code, '{/if}');
            if ($ifCount !== $endifCount) {
                $issues[] = "条件判断标签不匹配";
            }
        }
        
        // 检查CSS类名
        if (preg_match('/class="[^"]*"/', $code)) {
            $classes = [];
            preg_match_all('/class="([^"]*)"/', $code, $matches);
            foreach ($matches[1] as $classList) {
                $classes = array_merge($classes, explode(' ', $classList));
            }
            
            foreach ($classes as $class) {
                if (!empty($class) && !$this->cssClassExists($class)) {
                    $issues[] = "CSS类 '{$class}' 可能不存在";
                }
            }
        }
        
        if (empty($issues)) {
            echo "✅ 代码完整性检查通过\n";
        } else {
            echo "❌ 发现 " . count($issues) . " 个完整性问题:\n";
            foreach ($issues as $issue) {
                echo "  - {$issue}\n";
            }
        }
        
        return $issues;
    }

    // 私有方法
    private function loadCSSFiles()
    {
        $cssDir = $this->projectRoot . '/public/assets/css';
        if (is_dir($cssDir)) {
            $files = glob($cssDir . '/*.css');
            foreach ($files as $file) {
                $this->cssFiles[] = [
                    'path' => $file,
                    'content' => file_get_contents($file)
                ];
            }
        }
    }

    private function extractDesignTokens()
    {
        $allCSS = '';
        foreach ($this->cssFiles as $file) {
            $allCSS .= $file['content'];
        }

        // 提取颜色
        preg_match_all('/#[0-9a-fA-F]{3,6}/', $allCSS, $colors);
        $this->designTokens['colors'] = array_unique($colors[0]);

        // 提取间距
        preg_match_all('/(?:margin|padding):\s*([^;]+)/', $allCSS, $spacing);
        $this->designTokens['spacing'] = array_unique($spacing[1]);

        // 提取字体
        preg_match_all('/font-family:\s*([^;]+)/', $allCSS, $fonts);
        $this->designTokens['fonts'] = array_unique($fonts[1]);
    }

    private function findTemplateFile($pageName)
    {
        $possiblePaths = [
            "app/view/{$pageName}.html",
            "app/view/index/{$pageName}.html",
            "app/view/{$pageName}/index.html"
        ];

        foreach ($possiblePaths as $path) {
            $fullPath = $this->projectRoot . '/' . $path;
            if (file_exists($fullPath)) {
                return $fullPath;
            }
        }

        return null;
    }

    private function analyzeLayout($html)
    {
        $layout = [
            'containers' => substr_count($html, 'container'),
            'rows' => substr_count($html, 'row'),
            'columns' => substr_count($html, 'col-'),
            'sections' => substr_count($html, '<section'),
            'cards' => substr_count($html, 'card')
        ];

        return $layout;
    }

    private function analyzeDesignRules()
    {
        return [
            'primary_colors' => array_slice($this->designTokens['colors'], 0, 5),
            'common_spacing' => array_slice($this->designTokens['spacing'], 0, 5),
            'font_families' => array_slice($this->designTokens['fonts'], 0, 3)
        ];
    }

    private function checkSpacing($html, $css)
    {
        $issues = [];
        
        if (preg_match('/margin:\s*[0-9]+px/', $css)) {
            $issues[] = "建议使用标准间距值 (15px, 20px, 30px)";
        }
        
        return $issues;
    }

    private function checkColors($css)
    {
        $issues = [];
        
        $primaryColors = ['#6f42c1', '#007bff', '#28a745'];
        $hasThemeColor = false;
        
        foreach ($primaryColors as $color) {
            if (strpos($css, $color) !== false) {
                $hasThemeColor = true;
                break;
            }
        }
        
        if (!$hasThemeColor && !empty($css)) {
            $issues[] = "建议使用主题色系";
        }
        
        return $issues;
    }

    private function checkFonts($css)
    {
        $issues = [];
        
        if (strpos($css, 'font-family') !== false) {
            if (strpos($css, 'system') === false && strpos($css, 'Roboto') === false) {
                $issues[] = "建议使用系统字体或Roboto字体";
            }
        }
        
        return $issues;
    }

    private function checkLayout($html)
    {
        $issues = [];
        
        if (strpos($html, 'col-') === false && strpos($html, 'container') === false) {
            $issues[] = "建议使用Bootstrap网格系统";
        }
        
        return $issues;
    }

    private function cssClassExists($className)
    {
        foreach ($this->cssFiles as $file) {
            if (strpos($file['content'], '.' . $className) !== false) {
                return true;
            }
        }
        return false;
    }

    private function generateStyleGuide($pageName, $layout, $designRules)
    {
        $guide = "# {$pageName} 页面样式指南\n\n";
        $guide .= "## 布局结构\n";
        $guide .= "- 容器数量: {$layout['containers']}\n";
        $guide .= "- 行数量: {$layout['rows']}\n";
        $guide .= "- 列数量: {$layout['columns']}\n\n";
        
        $guide .= "## 设计规范\n";
        $guide .= "### 主要颜色\n";
        foreach ($designRules['primary_colors'] as $color) {
            $guide .= "- {$color}\n";
        }
        
        $guide .= "\n### 常用间距\n";
        foreach ($designRules['common_spacing'] as $spacing) {
            $guide .= "- {$spacing}\n";
        }
        
        $guidePath = $this->projectRoot . "/style_guide_{$pageName}.md";
        file_put_contents($guidePath, $guide);

        echo "✅ 样式指南生成: {$guidePath}\n";
    }

    /**
     * 批量样式检查
     */
    public function batchStyleCheck($directory) {
        $htmlFiles = $this->findHtmlFiles($directory);

        if (empty($htmlFiles)) {
            echo "❌ 未找到HTML文件\n";
            return;
        }

        echo "📁 发现 " . count($htmlFiles) . " 个HTML文件\n\n";

        $results = [];
        foreach ($htmlFiles as $file) {
            $relativePath = str_replace(dirname(__DIR__) . '/', '', $file);
            echo "🔍 检查文件: $relativePath\n";
            echo str_repeat("-", 40) . "\n";

            $content = file_get_contents($file);
            $result = $this->analyzeFileContent($content, $relativePath);
            $results[] = [
                'file' => $relativePath,
                'result' => $result
            ];

            echo "\n";
        }

        // 生成批量检查报告
        $this->generateBatchReport($results);
    }

    /**
     * 生成样式报告
     */
    public function generateStyleReport($directory) {
        $htmlFiles = $this->findHtmlFiles($directory);

        if (empty($htmlFiles)) {
            echo "❌ 未找到HTML文件\n";
            return;
        }

        echo "📊 生成样式报告...\n";
        echo "📁 扫描目录: $directory\n";
        echo "📄 文件数量: " . count($htmlFiles) . "\n\n";

        $allStyles = [];
        $allClasses = [];
        $styleIssues = [];

        foreach ($htmlFiles as $file) {
            $relativePath = str_replace(dirname(__DIR__) . '/', '', $file);
            $content = file_get_contents($file);

            // 提取样式信息
            $fileStyles = $this->extractStyleInfo($content);
            $allStyles[$relativePath] = $fileStyles;

            // 收集所有CSS类
            foreach ($fileStyles['classes'] as $class) {
                if (!isset($allClasses[$class])) {
                    $allClasses[$class] = [];
                }
                $allClasses[$class][] = $relativePath;
            }
        }

        // 生成报告
        $this->generateDetailedReport($allStyles, $allClasses, $directory);
    }

    /**
     * 查找HTML文件
     */
    private function findHtmlFiles($directory) {
        $files = [];

        if (!is_dir($directory)) {
            echo "❌ 目录不存在: $directory\n";
            return $files;
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'html') {
                $files[] = $file->getPathname();
            }
        }

        return $files;
    }

    /**
     * 分析文件内容
     */
    private function analyzeFileContent($content, $filePath) {
        $issues = [];
        $score = 100;

        // 检查Bootstrap类使用
        $bootstrapClasses = ['container', 'row', 'col-', 'btn', 'form-', 'nav', 'card'];
        $hasBootstrap = false;

        foreach ($bootstrapClasses as $class) {
            if (strpos($content, $class) !== false) {
                $hasBootstrap = true;
                break;
            }
        }

        if (!$hasBootstrap) {
            $issues[] = "⚠️ 未使用Bootstrap框架";
            $score -= 20;
        }

        // 检查响应式设计
        $responsiveClasses = ['col-sm-', 'col-md-', 'col-lg-', 'col-xl-'];
        $hasResponsive = false;

        foreach ($responsiveClasses as $class) {
            if (strpos($content, $class) !== false) {
                $hasResponsive = true;
                break;
            }
        }

        if (!$hasResponsive) {
            $issues[] = "⚠️ 缺少响应式设计";
            $score -= 15;
        }

        // 检查内联样式
        $inlineStyleCount = substr_count($content, 'style=');
        if ($inlineStyleCount > 5) {
            $issues[] = "⚠️ 内联样式过多 ({$inlineStyleCount}个)";
            $score -= 10;
        }

        // 检查CSS类数量
        preg_match_all('/class="([^"]*)"/', $content, $matches);
        $classCount = 0;
        foreach ($matches[1] as $classList) {
            $classCount += count(explode(' ', $classList));
        }

        if ($classCount > 50) {
            $issues[] = "⚠️ CSS类过多 ({$classCount}个)";
            $score -= 5;
        }

        // 输出结果
        if (empty($issues)) {
            echo "✅ 样式规范良好\n";
        } else {
            foreach ($issues as $issue) {
                echo "$issue\n";
            }
        }

        echo "📊 样式评分: $score/100\n";

        return [
            'score' => $score,
            'issues' => $issues,
            'class_count' => $classCount,
            'inline_styles' => $inlineStyleCount,
            'has_bootstrap' => $hasBootstrap,
            'has_responsive' => $hasResponsive
        ];
    }

    /**
     * 生成批量检查报告
     */
    private function generateBatchReport($results) {
        echo str_repeat("=", 60) . "\n";
        echo "📊 批量样式检查报告\n";
        echo str_repeat("=", 60) . "\n";

        $totalFiles = count($results);
        $totalScore = 0;
        $issueFiles = 0;

        // 统计信息
        foreach ($results as $result) {
            $totalScore += $result['result']['score'];
            if (!empty($result['result']['issues'])) {
                $issueFiles++;
            }
        }

        $avgScore = $totalFiles > 0 ? round($totalScore / $totalFiles, 1) : 0;

        echo "📈 总体统计:\n";
        echo "  • 检查文件数: $totalFiles\n";
        echo "  • 平均评分: $avgScore/100\n";
        echo "  • 有问题文件: $issueFiles\n\n";

        // 文件评分排行
        usort($results, function($a, $b) {
            return $b['result']['score'] - $a['result']['score'];
        });

        echo "📋 文件评分排行:\n";
        foreach ($results as $result) {
            $score = $result['result']['score'];
            $issueCount = count($result['result']['issues']);
            $status = $score >= 90 ? '✅' : ($score >= 70 ? '⚠️' : '❌');
            echo "  $status " . str_pad($result['file'], 30) . " 评分: $score 问题: $issueCount\n";
        }

        echo "\n🎯 改进建议:\n";
        if ($issueFiles > 0) {
            echo "  • $issueFiles 个文件存在样式问题，需要优化\n";
        }
        if ($avgScore < 80) {
            echo "  • 平均评分较低，建议建立样式规范\n";
        }
        echo "  • 定期运行样式检查，保持代码质量\n";
    }

    /**
     * 提取样式信息
     */
    private function extractStyleInfo($content) {
        $info = [
            'classes' => [],
            'inline_styles' => 0,
            'bootstrap_usage' => false,
            'responsive_usage' => false
        ];

        // 提取CSS类
        preg_match_all('/class="([^"]*)"/', $content, $matches);
        foreach ($matches[1] as $classList) {
            $classes = explode(' ', $classList);
            foreach ($classes as $class) {
                $class = trim($class);
                if (!empty($class)) {
                    $info['classes'][] = $class;
                }
            }
        }

        // 统计内联样式
        $info['inline_styles'] = substr_count($content, 'style=');

        // 检查Bootstrap使用
        $bootstrapClasses = ['container', 'row', 'col-', 'btn', 'form-'];
        foreach ($bootstrapClasses as $class) {
            if (strpos($content, $class) !== false) {
                $info['bootstrap_usage'] = true;
                break;
            }
        }

        // 检查响应式使用
        $responsiveClasses = ['col-sm-', 'col-md-', 'col-lg-', 'col-xl-'];
        foreach ($responsiveClasses as $class) {
            if (strpos($content, $class) !== false) {
                $info['responsive_usage'] = true;
                break;
            }
        }

        return $info;
    }

    /**
     * 生成详细报告
     */
    private function generateDetailedReport($allStyles, $allClasses, $directory) {
        echo "📊 样式使用统计:\n";
        echo "  • 总CSS类数: " . count($allClasses) . "\n";

        // 统计最常用的类
        $classUsage = [];
        foreach ($allClasses as $class => $files) {
            $classUsage[$class] = count($files);
        }
        arsort($classUsage);

        echo "  • 最常用类 (前10个):\n";
        $count = 0;
        foreach ($classUsage as $class => $usage) {
            if ($count >= 10) break;
            echo "    - $class: $usage 次\n";
            $count++;
        }

        // 统计Bootstrap使用率
        $bootstrapFiles = 0;
        $responsiveFiles = 0;

        foreach ($allStyles as $file => $styles) {
            if ($styles['bootstrap_usage']) $bootstrapFiles++;
            if ($styles['responsive_usage']) $responsiveFiles++;
        }

        $totalFiles = count($allStyles);
        $bootstrapRate = $totalFiles > 0 ? round(($bootstrapFiles / $totalFiles) * 100, 1) : 0;
        $responsiveRate = $totalFiles > 0 ? round(($responsiveFiles / $totalFiles) * 100, 1) : 0;

        echo "\n📱 框架使用统计:\n";
        echo "  • Bootstrap使用率: $bootstrapRate% ($bootstrapFiles/$totalFiles)\n";
        echo "  • 响应式设计率: $responsiveRate% ($responsiveFiles/$totalFiles)\n";

        // 保存详细报告
        $reportPath = dirname(__DIR__) . '/tools/style_report.txt';
        $reportContent = "# 样式检查报告 - " . date('Y-m-d H:i:s') . "\n\n";
        $reportContent .= "## 扫描目录: $directory\n\n";
        $reportContent .= "## 统计信息\n";
        $reportContent .= "- 总文件数: $totalFiles\n";
        $reportContent .= "- 总CSS类数: " . count($allClasses) . "\n";
        $reportContent .= "- Bootstrap使用率: $bootstrapRate%\n";
        $reportContent .= "- 响应式设计率: $responsiveRate%\n\n";

        $reportContent .= "## 详细文件信息\n";
        foreach ($allStyles as $file => $styles) {
            $reportContent .= "### $file\n";
            $reportContent .= "- CSS类数量: " . count($styles['classes']) . "\n";
            $reportContent .= "- 内联样式: " . $styles['inline_styles'] . "\n";
            $reportContent .= "- Bootstrap: " . ($styles['bootstrap_usage'] ? '是' : '否') . "\n";
            $reportContent .= "- 响应式: " . ($styles['responsive_usage'] ? '是' : '否') . "\n\n";
        }

        file_put_contents($reportPath, $reportContent);
        echo "\n📝 详细报告已保存: tools/style_report.txt\n";
    }
}

// 命令行使用
if (php_sapi_name() === 'cli') {
    $checker = new StyleChecker();

    $command = $argv[1] ?? 'help';

    switch ($command) {
        case 'analyze':
            $page = $argv[2] ?? 'index';
            echo "🎨 分析页面样式: $page\n";
            echo str_repeat("=", 50) . "\n";
            $checker->analyzePageStyle($page);
            break;

        case 'check':
            $html = $argv[2] ?? '';
            $css = $argv[3] ?? '';
            if (empty($html) || empty($css)) {
                echo "❌ 用法: php tools/style_checker.php check [HTML代码] [CSS样式]\n";
                echo "💡 示例: php tools/style_checker.php check 'col-lg-6' 'Bootstrap'\n";
                break;
            }
            echo "🔍 检查样式兼容性\n";
            echo str_repeat("=", 50) . "\n";
            $checker->checkStyleCompatibility($html, $css);
            break;

        case 'complete':
            $code = $argv[2] ?? '';
            $func = $argv[3] ?? '';
            if (empty($code) || empty($func)) {
                echo "❌ 用法: php tools/style_checker.php complete [代码] [功能描述]\n";
                echo "💡 示例: php tools/style_checker.php complete 'div class=\"row\"' '网格布局'\n";
                break;
            }
            echo "✅ 检查代码完整性\n";
            echo str_repeat("=", 50) . "\n";
            $checker->checkCodeCompleteness($code, $func);
            break;

        case 'batch':
            $directory = $argv[2] ?? 'app/view';
            echo "📁 批量样式检查: $directory\n";
            echo str_repeat("=", 50) . "\n";
            $checker->batchStyleCheck($directory);
            break;

        case 'report':
            $directory = $argv[2] ?? 'app/view';
            echo "📊 生成样式报告: $directory\n";
            echo str_repeat("=", 50) . "\n";
            $checker->generateStyleReport($directory);
            break;

        case 'help':
        default:
            echo "🎨 样式检查工具 - 三只鱼网络科技\n\n";
            echo "📋 可用命令:\n";
            echo "  analyze [页面]        - 分析指定页面样式规范\n";
            echo "  check [HTML] [CSS]    - 检查HTML和CSS兼容性\n";
            echo "  complete [代码] [功能] - 检查代码完整性\n";
            echo "  batch [目录]          - 批量检查目录下所有页面\n";
            echo "  report [目录]         - 生成详细样式报告\n";
            echo "  help                  - 显示此帮助信息\n\n";
            echo "💡 示例:\n";
            echo "  php tools/style_checker.php analyze index\n";
            echo "  php tools/style_checker.php check 'col-lg-6' 'Bootstrap'\n";
            echo "  php tools/style_checker.php complete 'div class=\"row\"' '网格布局'\n";
            echo "  php tools/style_checker.php batch app/view\n";
            echo "  php tools/style_checker.php report public/diy\n\n";
            echo "🎯 功能特性:\n";
            echo "  • 页面样式规范分析\n";
            echo "  • CSS兼容性检测\n";
            echo "  • 代码完整性验证\n";
            echo "  • 批量样式检查\n";
            echo "  • 详细样式报告生成\n";
            echo "  • 设计规范一致性检查\n";
    }
}
