// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 2025/6/14 13:07:31
import "@nuxtjs/tailwindcss/config-ctx"
import configMerger from "@nuxtjs/tailwindcss/merger";

import cfg2 from "./../../tailwind.config.js";
import cfg3 from "./../../tailwind.config.js";
const config = [
{"content":{"files":["D:/EServer/core/www/qiyediy/frontend/components/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/EServer/core/www/qiyediy/frontend/components/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/EServer/core/www/qiyediy/frontend/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/EServer/core/www/qiyediy/frontend/plugins/**/*.{js,ts,mjs}","D:/EServer/core/www/qiyediy/frontend/composables/**/*.{js,ts,mjs}","D:/EServer/core/www/qiyediy/frontend/utils/**/*.{js,ts,mjs}","D:/EServer/core/www/qiyediy/frontend/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/EServer/core/www/qiyediy/frontend/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","D:/EServer/core/www/qiyediy/frontend/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","D:/EServer/core/www/qiyediy/frontend/app.config.{js,ts,mjs}"]}},
{},
cfg2,
cfg3
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = config;

export default resolvedConfig;