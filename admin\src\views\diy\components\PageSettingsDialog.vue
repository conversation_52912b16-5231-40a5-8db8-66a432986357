<template>
  <el-dialog
    v-model="visible"
    title="页面设置"
    width="600px"
    :before-close="handleClose"
  >
    <el-form 
      ref="formRef"
      :model="formData" 
      :rules="formRules"
      label-width="100px"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h4 class="section-title">基本信息</h4>
        <el-form-item label="页面标题" prop="title">
          <el-input 
            v-model="formData.title" 
            placeholder="请输入页面标题"
          />
        </el-form-item>
        
        <el-form-item label="页面别名" prop="slug">
          <el-input 
            v-model="formData.slug" 
            placeholder="请输入页面别名，用于URL"
          />
          <div class="form-tip">
            页面访问地址：{{ siteUrl }}/{{ formData.slug || 'page-slug' }}
          </div>
        </el-form-item>
        
        <el-form-item label="页面描述">
          <el-input 
            v-model="formData.description" 
            type="textarea"
            :rows="3"
            placeholder="请输入页面描述"
          />
        </el-form-item>
        
        <el-form-item label="页面状态">
          <el-radio-group v-model="formData.status">
            <el-radio :label="0">草稿</el-radio>
            <el-radio :label="1">已发布</el-radio>
            <el-radio :label="2">已归档</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>

      <!-- SEO设置 -->
      <div class="form-section">
        <h4 class="section-title">SEO设置</h4>
        <el-form-item label="SEO标题">
          <el-input 
            v-model="formData.seo_title" 
            placeholder="请输入SEO标题"
            maxlength="60"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="SEO描述">
          <el-input 
            v-model="formData.seo_description" 
            type="textarea"
            :rows="3"
            placeholder="请输入SEO描述"
            maxlength="160"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="SEO关键词">
          <el-input 
            v-model="formData.seo_keywords" 
            placeholder="请输入SEO关键词，多个用逗号分隔"
          />
        </el-form-item>
      </div>

      <!-- 访问设置 -->
      <div class="form-section">
        <h4 class="section-title">访问设置</h4>
        <el-form-item label="访问权限">
          <el-radio-group v-model="formData.access_type">
            <el-radio value="public">公开访问</el-radio>
            <el-radio value="login">登录后访问</el-radio>
            <el-radio value="password">密码访问</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item 
          v-if="formData.access_type === 'password'"
          label="访问密码"
          prop="access_password"
        >
          <el-input 
            v-model="formData.access_password" 
            type="password"
            placeholder="请输入访问密码"
          />
        </el-form-item>
        
        <el-form-item label="缓存时间">
          <el-select v-model="formData.cache_time" placeholder="请选择缓存时间">
            <el-option label="不缓存" :value="0" />
            <el-option label="5分钟" :value="300" />
            <el-option label="30分钟" :value="1800" />
            <el-option label="1小时" :value="3600" />
            <el-option label="1天" :value="86400" />
          </el-select>
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { DiyPage } from '@/api/diy'

interface Props {
  modelValue: boolean
  page: DiyPage
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success', data: DiyPage): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)

// 对话框显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 站点URL（从配置获取）
const siteUrl = ref('https://your-site.com')

// 表单数据
const formData = reactive({
  title: '',
  slug: '',
  description: '',
  status: 0,
  seo_title: '',
  seo_description: '',
  seo_keywords: '',
  access_type: 'public',
  access_password: '',
  cache_time: 3600
})

// 表单验证规则
const formRules: FormRules = {
  title: [
    { required: true, message: '请输入页面标题', trigger: 'blur' }
  ],
  slug: [
    { required: true, message: '请输入页面别名', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9-_]+$/, message: '页面别名只能包含字母、数字、横线和下划线', trigger: 'blur' }
  ],
  access_password: [
    { required: true, message: '请输入访问密码', trigger: 'blur' }
  ]
}

// 监听页面数据变化
watch(() => props.page, (newPage) => {
  if (newPage && visible.value) {
    Object.assign(formData, {
      title: newPage.title || '',
      slug: newPage.slug || '',
      description: newPage.description || '',
      status: newPage.status || 0,
      seo_title: newPage.seo_title || '',
      seo_description: newPage.seo_description || '',
      seo_keywords: newPage.seo_keywords || '',
      access_type: newPage.access_type || 'public',
      access_password: newPage.access_password || '',
      cache_time: newPage.cache_time || 3600
    })
  }
}, { immediate: true, deep: true })

/**
 * 处理关闭
 */
const handleClose = () => {
  visible.value = false
}

/**
 * 处理提交
 */
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 构造更新数据
    const updateData = {
      ...props.page,
      ...formData
    }
    
    emit('success', updateData)
    visible.value = false
    
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.form-section {
  margin-bottom: 24px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.form-tip {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-size: 12px;
  color: #606266;
}
</style>
