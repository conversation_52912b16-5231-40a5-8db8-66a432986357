@echo off
REM 三只鱼网络科技 | 韩总 | 2024-12-20
REM QiyeDIY企业建站系统 - Stagewise开发环境启动脚本

echo ========================================
echo   QiyeDIY + Stagewise 开发环境启动
echo ========================================
echo.

echo [检查] 验证VS Code Stagewise扩展...
echo.
echo ⚠️  重要提醒：
echo    请确保已安装VS Code Stagewise扩展！
echo    扩展地址: https://marketplace.visualstudio.com/items?itemName=stagewise.stagewise-vscode-extension
echo.
echo    如未安装，请：
echo    1. 打开VS Code
echo    2. 按 Ctrl+Shift+X 打开扩展市场
echo    3. 搜索 "stagewise" 并安装
echo    4. 重启VS Code
echo.

pause

echo [1/4] 启动后端API服务...
cd backend
start "QiyeDIY-Backend" cmd /k "php think run -p 8000"
cd ..
echo ✅ 后端API服务启动中 (端口: 8000)
echo.

echo [2/4] 启动管理后台 (含Stagewise工具栏)...
cd admin
start "QiyeDIY-Admin-Stagewise" cmd /k "npm run dev"
cd ..
echo ✅ 管理后台启动中 (端口: 3001)
echo.

echo [3/4] 启动前端展示 (含Stagewise工具栏)...
cd frontend
start "QiyeDIY-Frontend-Stagewise" cmd /k "npm run dev"
cd ..
echo ✅ 前端展示启动中 (端口: 3000)
echo.

echo [4/4] 等待服务启动完成...
timeout /t 10 /nobreak >nul
echo.

echo ========================================
echo   🚀 Stagewise开发环境启动完成！
echo ========================================
echo.
echo 📱 访问地址:
echo    管理后台: http://localhost:3001
echo    前端展示: http://localhost:3000
echo    后端API:  http://localhost:8000
echo.
echo 🎯 Stagewise使用方法:
echo    1. 在浏览器中打开上述地址
echo    2. 在页面右下角找到Stagewise工具栏
echo    3. 点击选择工具，选择页面元素
echo    4. 用自然语言描述修改需求
echo    5. AI会自动生成并应用代码修改
echo.
echo 💡 提示:
echo    - 确保VS Code Stagewise扩展已安装
echo    - 同时只能在一个VS Code窗口中使用
echo    - 工具栏仅在开发环境中显示
echo.
echo 📚 详细文档: docs/Stagewise集成指南.md
echo.

echo 按任意键打开浏览器访问管理后台...
pause >nul

start http://localhost:3001

echo.
echo 🎉 开发环境已就绪，开始您的可视化编程之旅！
echo.
pause
