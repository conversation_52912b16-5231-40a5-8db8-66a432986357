# 🚀 Stagewise工具栏集成指南

**三只鱼网络科技 | 韩总 | 2024-12-20**  
**QiyeDIY企业建站系统 - Stagewise可视化编程工具栏集成**

---

## 📋 概述

Stagewise是一个强大的可视化编程工具栏，可以让开发者在浏览器中直接选择UI元素，并通过AI代理进行代码修改。本指南详细说明了如何在QiyeDIY系统中使用Stagewise工具栏。

### ✨ 主要功能
- 🎯 **元素选择** - 在浏览器中直接选择UI元素
- 💬 **AI交互** - 通过自然语言描述修改需求
- 🤖 **智能编程** - AI代理自动生成和修改代码
- 🔄 **实时预览** - 修改后立即看到效果
- 🎨 **可视化编辑** - 无需手动编写代码

---

## 🛠️ 安装配置

### 1. VS Code扩展安装

**必须先安装VS Code扩展才能使用Stagewise工具栏！**

1. 打开VS Code
2. 进入扩展市场 (Ctrl+Shift+X)
3. 搜索 "stagewise"
4. 安装 "Stagewise" 扩展
5. 重启VS Code

**扩展地址**: https://marketplace.visualstudio.com/items?itemName=stagewise.stagewise-vscode-extension

### 2. 项目依赖

项目已经自动安装了必要的依赖：

```bash
# 管理后台
cd admin
npm install @stagewise/toolbar-vue --save-dev

# 前端展示
cd frontend  
npm install @stagewise/toolbar-vue --save-dev
```

### 3. 自动初始化 (推荐)

在Cursor中使用自动初始化命令：

1. 按 `Ctrl+Shift+P` (Windows) 或 `Cmd+Shift+P` (Mac)
2. 输入 `setupToolbar`
3. 执行命令，工具栏将自动初始化

---

## 🎯 使用方法

### 启动开发服务器

确保在开发环境中启动项目：

```bash
# 启动管理后台 (端口3001)
cd admin
npm run dev

# 启动前端展示 (端口3000)  
cd frontend
npm run dev
```

### 使用Stagewise工具栏

1. **打开浏览器** - 访问 http://localhost:3001 (管理后台) 或 http://localhost:3000 (前端)
2. **查看工具栏** - 在页面右下角会出现Stagewise工具栏
3. **选择元素** - 点击工具栏上的选择工具，然后点击页面上的任意元素
4. **描述需求** - 在弹出的对话框中用自然语言描述你想要的修改
5. **AI处理** - AI代理会自动分析并生成相应的代码修改
6. **查看结果** - 修改会自动应用到代码中，页面会实时更新

### 工作流程示例

```
1. 选择一个按钮元素
   ↓
2. 输入: "把这个按钮改成红色，并增加圆角"
   ↓  
3. AI自动修改CSS样式
   ↓
4. 页面实时更新显示新样式
```

---

## ⚙️ 配置选项

### 管理后台配置

文件位置: `admin/src/App.vue`

```typescript
const stagewiseConfig: ToolbarConfig = {
  plugins: [
    // 自定义插件配置
  ],
  theme: 'light',           // 主题: 'light' | 'dark'
  position: 'bottom-right', // 位置: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  autoHide: false,          // 是否自动隐藏
}
```

### 前端展示配置

文件位置: `frontend/app.vue`

```typescript
const stagewiseConfig: ToolbarConfig = {
  plugins: [
    // 自定义插件配置
  ],
  theme: 'light',           // 主题颜色
  position: 'bottom-right', // 工具栏位置
  autoHide: false,          // 自动隐藏设置
}
```

### 环境检测

工具栏仅在开发环境中显示，生产环境自动隐藏：

```typescript
const isDevelopment = computed(() => {
  return process.env.NODE_ENV === 'development' || import.meta.env.DEV
})
```

---

## 🔧 自定义插件

### 创建自定义插件

```typescript
const customPlugin = {
  name: 'custom-plugin',
  init: (toolbar) => {
    // 插件初始化逻辑
  },
  onElementSelect: (element) => {
    // 元素选择时的处理逻辑
  },
  onPromptSubmit: (prompt, element) => {
    // 提示提交时的处理逻辑
  }
}

const stagewiseConfig: ToolbarConfig = {
  plugins: [customPlugin],
  // 其他配置...
}
```

### 内置插件示例

```typescript
const stagewiseConfig: ToolbarConfig = {
  plugins: [
    {
      name: 'element-inspector',
      showElementInfo: true,
      highlightOnHover: true
    },
    {
      name: 'code-generator', 
      framework: 'vue',
      outputFormat: 'typescript'
    }
  ]
}
```

---

## 🚨 注意事项

### 重要提醒

1. **VS Code扩展必需** - 必须安装VS Code扩展才能使用
2. **单窗口使用** - 同时只能在一个Cursor/VS Code窗口中使用
3. **开发环境限制** - 工具栏仅在开发环境中显示
4. **网络连接** - 需要稳定的网络连接与AI服务通信

### 常见问题

**Q: 工具栏不显示怎么办？**
A: 检查是否在开发环境，是否安装了VS Code扩展

**Q: 点击元素没有反应？**  
A: 确保只有一个VS Code窗口打开，检查扩展是否正常运行

**Q: AI修改没有生效？**
A: 检查网络连接，确保VS Code扩展与工具栏正常通信

### 性能优化

- 工具栏使用 `ClientOnly` 包装，避免SSR问题
- 仅在开发环境加载，不影响生产性能
- 使用懒加载，减少初始加载时间

---

## 📚 更多资源

- **官方文档**: https://stagewise.io/docs
- **GitHub仓库**: https://github.com/stagewise-io/stagewise
- **Discord社区**: https://discord.gg/gkdGsDYaKA
- **VS Code扩展**: https://marketplace.visualstudio.com/items?itemName=stagewise.stagewise-vscode-extension

---

## 🤝 技术支持

如有问题，请联系：
- **邮箱**: <EMAIL>
- **QQ群**: 123456789
- **技术支持**: 韩总

---

**© 2024 三只鱼网络科技 | 韩总 - QiyeDIY企业建站系统**
