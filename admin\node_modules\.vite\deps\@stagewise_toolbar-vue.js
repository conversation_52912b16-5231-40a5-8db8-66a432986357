import {
  initToolbar
} from "./chunk-XBYHZ3P6.js";
import {
  defineComponent,
  onMounted
} from "./chunk-HX5C5AN6.js";
import "./chunk-G3PMV62Z.js";

// node_modules/@stagewise/toolbar-vue/dist/index.js
var _sfc_main = defineComponent({
  __name: "StagewiseToolbar",
  props: {
    config: {},
    enabled: { type: Boolean, default: () => true }
  },
  setup(__props) {
    const props = __props;
    onMounted(() => {
      if (props.enabled) {
        initToolbar(props.config);
      }
    });
    return (_ctx, _cache) => {
      return null;
    };
  }
});
export {
  _sfc_main as StagewiseToolbar
};
//# sourceMappingURL=@stagewise_toolbar-vue.js.map
