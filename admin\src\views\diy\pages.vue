<template>
  <div class="diy-pages">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">页面管理</h1>
        <p class="page-description">管理您的DIY页面，创建和编辑自定义页面</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="handleCreate">
          新建页面
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="page-filters">
      <el-form :model="searchForm" inline>
        <el-form-item>
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索页面标题或别名"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="searchForm.status"
            placeholder="页面状态"
            clearable
            @change="handleSearch"
          >
            <el-option label="草稿" :value="0" />
            <el-option label="已发布" :value="1" />
            <el-option label="已归档" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 页面列表 -->
    <div class="page-content">
      <el-table
        v-loading="loading"
        :data="pageList"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="页面信息" min-width="300">
          <template #default="{ row }">
            <div class="page-info">
              <div class="page-title">
                <span class="title">{{ row.title }}</span>
                <el-tag
                  :type="getStatusType(row.status)"
                  size="small"
                  class="status-tag"
                >
                  {{ getStatusText(row.status) }}
                </el-tag>
              </div>
              <div class="page-meta">
                <span class="slug">{{ row.slug }}</span>
                <span class="divider">|</span>
                <span class="views">{{ row.views || 0 }} 次访问</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.updated_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              :icon="Edit"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-dropdown @command="(command) => handleAction(command, row)">
              <el-button size="small" :icon="More" />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="preview" :icon="View">
                    预览
                  </el-dropdown-item>
                  <el-dropdown-item command="copy" :icon="CopyDocument">
                    复制
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="row.status === 0"
                    command="publish"
                    :icon="Upload"
                  >
                    发布
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-else
                    command="unpublish"
                    :icon="Download"
                  >
                    取消发布
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="delete"
                    :icon="Delete"
                    divided
                  >
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedPages.length > 0" class="batch-actions">
      <div class="batch-info">
        已选择 {{ selectedPages.length }} 个页面
      </div>
      <div class="batch-buttons">
        <el-button @click="handleBatchPublish">批量发布</el-button>
        <el-button @click="handleBatchUnpublish">批量取消发布</el-button>
        <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { diyPageApi } from '@/api/diy'
import type { DiyPage } from '@/api/diy'
import {
  Plus, Search, Edit, More, View, CopyDocument, Upload, Download, Delete
} from '@element-plus/icons-vue'

const router = useRouter()

// 数据状态
const loading = ref(false)
const pageList = ref<DiyPage[]>([])
const selectedPages = ref<DiyPage[]>([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: undefined as number | undefined
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

/**
 * 获取页面列表
 */
const getPageList = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      per_page: pagination.size,
      keyword: searchForm.keyword,
      status: searchForm.status
    }

    const response = await diyPageApi.getList(params)
    pageList.value = response.data.data || response.data.list || []
    pagination.total = response.data.total || 0

  } catch (error: any) {
    ElMessage.error(error.message || '获取页面列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  pagination.page = 1
  getPageList()
}

/**
 * 处理重置
 */
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.status = undefined
  pagination.page = 1
  getPageList()
}

/**
 * 处理选择变化
 */
const handleSelectionChange = (selection: DiyPage[]) => {
  selectedPages.value = selection
}

/**
 * 处理分页大小变化
 */
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  getPageList()
}

/**
 * 处理页码变化
 */
const handlePageChange = (page: number) => {
  pagination.page = page
  getPageList()
}

/**
 * 处理新建
 */
const handleCreate = () => {
  router.push('/diy/editor/new')
}

/**
 * 处理编辑
 */
const handleEdit = (page: DiyPage) => {
  router.push(`/diy/editor/${page.id}`)
}

/**
 * 处理操作
 */
const handleAction = async (command: string, page: DiyPage) => {
  switch (command) {
    case 'preview':
      handlePreview(page)
      break
    case 'copy':
      handleCopy(page)
      break
    case 'publish':
      handlePublish(page)
      break
    case 'unpublish':
      handleUnpublish(page)
      break
    case 'delete':
      handleDelete(page)
      break
  }
}

/**
 * 处理预览
 */
const handlePreview = (page: DiyPage) => {
  const url = `${window.location.origin}/${page.slug}`
  window.open(url, '_blank')
}

/**
 * 处理复制
 */
const handleCopy = async (page: DiyPage) => {
  try {
    await diyPageApi.copy(page.id)
    ElMessage.success('页面复制成功')
    getPageList()
  } catch (error: any) {
    ElMessage.error(error.message || '页面复制失败')
  }
}

/**
 * 处理发布
 */
const handlePublish = async (page: DiyPage) => {
  try {
    await diyPageApi.publish(page.id)
    ElMessage.success('页面发布成功')
    getPageList()
  } catch (error: any) {
    ElMessage.error(error.message || '页面发布失败')
  }
}

/**
 * 处理取消发布
 */
const handleUnpublish = async (page: DiyPage) => {
  try {
    await diyPageApi.unpublish(page.id)
    ElMessage.success('页面已取消发布')
    getPageList()
  } catch (error: any) {
    ElMessage.error(error.message || '取消发布失败')
  }
}

/**
 * 处理删除
 */
const handleDelete = async (page: DiyPage) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除页面"${page.title}"吗？此操作不可恢复。`,
      '确认删除',
      {
        type: 'warning'
      }
    )
    
    await diyPageApi.delete(page.id)
    ElMessage.success('页面删除成功')
    getPageList()
    
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '页面删除失败')
    }
  }
}

/**
 * 批量发布
 */
const handleBatchPublish = async () => {
  try {
    const ids = selectedPages.value.map(page => page.id)
    await diyPageApi.batchPublish(ids)
    ElMessage.success('批量发布成功')
    getPageList()
  } catch (error: any) {
    ElMessage.error(error.message || '批量发布失败')
  }
}

/**
 * 批量取消发布
 */
const handleBatchUnpublish = async () => {
  try {
    const ids = selectedPages.value.map(page => page.id)
    await diyPageApi.batchUnpublish(ids)
    ElMessage.success('批量取消发布成功')
    getPageList()
  } catch (error: any) {
    ElMessage.error(error.message || '批量取消发布失败')
  }
}

/**
 * 批量删除
 */
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedPages.value.length} 个页面吗？此操作不可恢复。`,
      '确认删除',
      {
        type: 'warning'
      }
    )
    
    const ids = selectedPages.value.map(page => page.id)
    await diyPageApi.batchDelete(ids)
    ElMessage.success('批量删除成功')
    getPageList()
    
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '批量删除失败')
    }
  }
}

/**
 * 获取状态类型
 */
const getStatusType = (status: number) => {
  const types = {
    0: 'info',    // 草稿
    1: 'success', // 已发布
    2: 'warning'  // 已归档
  }
  return types[status] || 'info'
}

/**
 * 获取状态文本
 */
const getStatusText = (status: number) => {
  const texts = {
    0: '草稿',
    1: '已发布',
    2: '已归档'
  }
  return texts[status] || '未知'
}

/**
 * 格式化日期
 */
const formatDate = (date: string) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}

// 初始化
onMounted(() => {
  getPageList()
})
</script>

<style scoped>
.diy-pages {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.page-filters {
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.page-info .page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.page-info .title {
  font-weight: 500;
  color: #303133;
}

.page-info .page-meta {
  font-size: 12px;
  color: #909399;
}

.page-info .divider {
  margin: 0 8px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.batch-actions {
  position: fixed;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 24px;
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.batch-info {
  font-size: 14px;
  color: #606266;
}

.batch-buttons {
  display: flex;
  gap: 8px;
}
</style>
