<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 列设置对话框
-->

<template>
  <el-dialog
    v-model="visible"
    title="列设置"
    width="500px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="column-settings">
      <div class="settings-header">
        <el-checkbox
          v-model="checkAll"
          :indeterminate="isIndeterminate"
          @change="handleCheckAllChange"
        >
          全选
        </el-checkbox>
        <el-button type="text" @click="handleReset">重置</el-button>
      </div>

      <el-divider />

      <div class="column-list">
        <div v-for="element in columnList" :key="element.prop" class="column-item">
          <div class="column-info">
            <el-icon class="drag-handle">
              <Rank />
            </el-icon>
            <el-checkbox
              v-model="element.visible"
              @change="handleColumnChange"
            >
              {{ element.label }}
            </el-checkbox>
          </div>
          <div class="column-actions">
            <el-tooltip content="固定到左侧" placement="top">
              <el-button
                type="text"
                :class="{ active: element.fixed === 'left' }"
                @click="handleFixedChange(element, 'left')"
              >
                <el-icon><Back /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="取消固定" placement="top">
              <el-button
                type="text"
                :class="{ active: !element.fixed }"
                @click="handleFixedChange(element, false)"
              >
                <el-icon><Minus /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="固定到右侧" placement="top">
              <el-button
                type="text"
                :class="{ active: element.fixed === 'right' }"
                @click="handleFixedChange(element, 'right')"
              >
                <el-icon><Right /></el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Rank, Back, Right, Minus } from '@element-plus/icons-vue'
// import draggable from 'vuedraggable'

interface ColumnConfig {
  prop: string
  label: string
  visible: boolean
  fixed?: string | boolean
  width?: number
  sortable?: boolean
}

interface Props {
  modelValue: boolean
  columns: ColumnConfig[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'change', columns: ColumnConfig[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const columnList = ref<ColumnConfig[]>([])

// 全选状态
const checkAll = computed({
  get: () => columnList.value.every(item => item.visible),
  set: (val: boolean) => {
    columnList.value.forEach(item => {
      item.visible = val
    })
  }
})

// 半选状态
const isIndeterminate = computed(() => {
  const visibleCount = columnList.value.filter(item => item.visible).length
  return visibleCount > 0 && visibleCount < columnList.value.length
})

// 控制对话框显示
const visible = ref(false)

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val
    if (val) {
      loadColumns()
    }
  },
  { immediate: true }
)

watch(visible, (val) => {
  emit('update:modelValue', val)
})

/**
 * 加载列配置
 */
const loadColumns = () => {
  columnList.value = JSON.parse(JSON.stringify(props.columns))
}

/**
 * 处理全选变化
 */
const handleCheckAllChange = (val: boolean) => {
  columnList.value.forEach(item => {
    item.visible = val
  })
}

/**
 * 处理列变化
 */
const handleColumnChange = () => {
  // 触发计算属性更新
}

/**
 * 处理固定变化
 */
const handleFixedChange = (column: ColumnConfig, fixed: string | boolean) => {
  column.fixed = fixed
}

/**
 * 处理拖拽结束
 */
const handleDragEnd = () => {
  // 拖拽结束后的处理
}

/**
 * 重置设置
 */
const handleReset = () => {
  loadColumns()
  ElMessage.info('已重置为默认设置')
}

/**
 * 保存设置
 */
const handleSave = () => {
  // 至少要显示一列
  const visibleColumns = columnList.value.filter(item => item.visible)
  if (visibleColumns.length === 0) {
    ElMessage.warning('至少要显示一列')
    return
  }

  emit('change', columnList.value)
  ElMessage.success('列设置已保存')
  handleClose()
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.column-settings {
  max-height: 400px;
  overflow-y: auto;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.column-list {
  .column-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);

    &:last-child {
      border-bottom: none;
    }

    .column-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .drag-handle {
        cursor: move;
        color: var(--el-text-color-placeholder);
        
        &:hover {
          color: var(--el-text-color-regular);
        }
      }
    }

    .column-actions {
      display: flex;
      gap: 4px;

      .el-button {
        padding: 4px;
        
        &.active {
          color: var(--el-color-primary);
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-divider) {
  margin: 12px 0;
}
</style>
