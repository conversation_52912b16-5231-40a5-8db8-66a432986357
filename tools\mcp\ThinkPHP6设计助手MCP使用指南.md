# ThinkPHP6设计助手MCP使用指南

**三只鱼网络科技 | 韩总 | 2025-06-11**  
**专业前端设计解决方案 - 基于您的项目定制**

## 🎯 概述

ThinkPHP6设计助手MCP服务器是专门为您的项目定制的前端设计工具，整合了您现有的优秀工具链，提供智能化的设计分析和建议。

## ✅ 已安装完成

- ✅ **MCP服务器**: `mcp-thinkphp6-design.js`
- ✅ **依赖包**: `@modelcontextprotocol/sdk@^0.6.0`
- ✅ **配置文件**: `augment-mcp-config.json`
- ✅ **测试验证**: 服务器启动正常

## 🛠️ 功能特性

### 1. **布局质量分析** 📐
- 集成您的 `layout_analyzer.php` 工具
- 自动分析HTML页面布局质量
- 提供响应式设计评分

### 2. **性能优化分析** ⚡
- 集成您的 `performance_analyzer.php` 工具
- 全面的性能指标分析
- 优化建议和改进方案

### 3. **DIY组件生成** 🎨
- 基于您的DIY页面构建器
- 支持6种组件类型：card、hero、stats、team、contact、textblock
- 多种样式风格：modern、classic、minimal、gradient

### 4. **响应式设计检查** 📱
- 专门的响应式设计质量检查
- 移动端适配验证
- Bootstrap网格系统分析

### 5. **CSS/JS优化** 🎨
- 集成您的 `css_js_optimizer.php` 工具
- 代码优化和压缩建议
- 资源加载性能分析

### 6. **智能设计建议** 💡
- 基于项目上下文的设计建议
- 结合您的技术栈特点
- 个性化的改进方案

## 🚀 使用方法

### 在Augment中配置

1. **打开Augment设置**
2. **导航到MCP服务器配置**
3. **添加新的MCP服务器**：
   - **名称**: `ThinkPHP6设计助手`
   - **命令**: `node`
   - **参数**: `["D:\\EServer\\core\\www\\san.com\\mcp-thinkphp6-design.js"]`

### 使用示例

#### 1. 分析页面布局
```
请分析 app/view/admin/products.html 的布局质量
```

#### 2. 生成DIY组件
```
帮我生成一个现代风格的卡片组件，标题是"产品特色"，描述是"我们的产品具有以下优势"
```

#### 3. 检查响应式设计
```
检查首页的响应式设计质量
```

#### 4. 性能优化建议
```
分析整个项目的性能并给出优化建议
```

## 📋 可用工具

| 工具名称 | 功能描述 | 参数 |
|---------|---------|------|
| `analyze_layout` | 分析HTML页面布局质量 | `filePath`: 文件路径 |
| `optimize_performance` | 性能分析和优化建议 | `target`: 分析目标 |
| `generate_diy_component` | 生成DIY组件 | `componentType`, `style`, `properties` |
| `check_responsive_design` | 响应式设计检查 | `filePath`: 文件路径 |
| `optimize_css_js` | CSS/JS优化 | `mode`: 优化模式 |
| `generate_design_suggestions` | 生成设计建议 | `context`: 设计上下文 |

## 📚 可用资源

| 资源名称 | 描述 | URI |
|---------|------|-----|
| 设计规范 | ThinkPHP6设计规范和最佳实践 | `thinkphp6://design-guidelines` |
| 组件库 | DIY页面构建器组件库 | `thinkphp6://component-library` |
| 性能指标 | 当前项目性能指标 | `thinkphp6://performance-metrics` |

## 🎨 组件类型说明

### 支持的组件类型
- **card**: 卡片组件 - 适合展示产品、服务、特性
- **hero**: 英雄区组件 - 适合页面顶部横幅
- **stats**: 统计数字组件 - 适合数据展示
- **team**: 团队介绍组件 - 适合团队成员展示
- **contact**: 联系信息组件 - 适合联系方式展示
- **textblock**: 文本块组件 - 适合内容展示

### 支持的样式风格
- **modern**: 现代风格 - 简洁、时尚
- **classic**: 经典风格 - 传统、稳重
- **minimal**: 极简风格 - 简约、清爽
- **gradient**: 渐变风格 - 动感、活力

## 🔧 高级配置

### 环境变量
```bash
NODE_ENV=production  # 生产环境模式
DEBUG=mcp:*         # 调试模式（可选）
```

### 自定义配置
您可以修改 `mcp-thinkphp6-design.js` 文件来：
- 添加新的工具功能
- 自定义组件模板
- 调整分析参数
- 扩展设计建议

## 🎯 与您项目的完美集成

### 1. **无缝集成现有工具**
- 直接调用您的PHP工具脚本
- 保持原有的分析逻辑
- 增强用户体验

### 2. **基于项目特点定制**
- 针对ThinkPHP6框架优化
- 结合您的DIY页面构建器
- 符合您的开发规范

### 3. **智能化设计助手**
- 理解您的技术栈
- 提供专业建议
- 提升开发效率

## 🚀 下一步计划

1. **扩展组件库** - 添加更多DIY组件类型
2. **增强分析能力** - 集成更多分析维度
3. **自动化优化** - 实现自动代码优化
4. **设计模板** - 提供完整的页面模板

## 💡 使用技巧

1. **结合Sequential thinking** - 用于复杂设计决策分析
2. **配合Desktop Commander** - 用于文件管理和批量操作
3. **定期性能检查** - 建议每周运行一次性能分析
4. **响应式测试** - 新页面开发后必须进行响应式检查

## 🎉 总结

ThinkPHP6设计助手MCP服务器为您提供了：
- ✅ **专业的设计分析工具**
- ✅ **智能的组件生成功能**
- ✅ **完整的性能优化建议**
- ✅ **无缝的项目集成体验**

现在您可以在Augment中享受专业的前端设计助手服务了！🎨✨
