## [v0.2.0] - May 13, 2015

 * [#93] - Remove jQuery dependency in component ([@slang800])
 * [#120] - Fix the Readme showing a wrong example for `ease` ([@teeceepee])

Minor changes:

 * [#92] - Fix typo in Readme ([@FND])
 * [#90] - Fix typo in Readme ([@mdxs])
 * [#119] - Fix typo in Readme ([@infertux])
 * [#53] - Use `===` instead of `==` ([@drodil])

## [v0.1.6] - June 25, 2014

 * [#86] - Add support for specifying a different parent container. ([@jonjaques], [#14], [#33], [#39])
 * [#64] - Fix Require.js support ([#75], [#85])
 * [#54] - Fix component support

## [v0.1.5] - June 21, 2014

 * [#82] - Release properly to the npm registry

## [v0.1.4] - June 21, 2014

 * [#65] - Bower: don't download package.json. Fixes browserify + jquery problem. ([@amelon])
 * [#75] - Fix compatibility with Karma. ([@shaqq])
 
Internal changes:

 * [#77] - Use SVG to display Travis-CI badge. ([@Mithgol])
 * [#34] - Readme: update .inc() and .status docs ([@lacivert])
 * [#73] - Readme: update year ([@rwholmes])

## [v0.1.3] - March 26, 2014

 * [#7] - Remove jQuery dependency ([#28], [#17], [@rurjur])
 * [#73] - Update Readme to change year to 2014. ([@rwholmes])

## [v0.1.2] - August 21, 2013

Minor update for proper [Bower] and [Component] support.

 * Add Bower support.
 * Fix Component support and use `component/jquery` as a dependency.

## [v0.1.1] - August 21, 2013

Minor fixes.

 * Removed the busy cursor that occurs when loading.
 * Added support for IE7 to IE9. ([#3], [@markbao])
 * Implement `trickleRate` and `trickleSpeed` options.
 * [#5] - Implement the `showSpinner` option to allow removing the spinner. ([#9], [@rahulcs])
 * Registered as a Component in Component.io.
 * [#8] - Updated the Readme with better Turbolinks instructions.

## v0.1.0 - August 20, 2013

Initial release.

[Bower]: http://bower.io
[Component]: http://component.io
[#119]: https://github.com/rstacruz/nprogress/issues/119
[#120]: https://github.com/rstacruz/nprogress/issues/120
[#14]: https://github.com/rstacruz/nprogress/issues/14
[#17]: https://github.com/rstacruz/nprogress/issues/17
[#28]: https://github.com/rstacruz/nprogress/issues/28
[#33]: https://github.com/rstacruz/nprogress/issues/33
[#34]: https://github.com/rstacruz/nprogress/issues/34
[#39]: https://github.com/rstacruz/nprogress/issues/39
[#3]: https://github.com/rstacruz/nprogress/issues/3
[#54]: https://github.com/rstacruz/nprogress/issues/84
[#5]: https://github.com/rstacruz/nprogress/issues/5
[#64]: https://github.com/rstacruz/nprogress/issues/64
[#65]: https://github.com/rstacruz/nprogress/issues/65
[#73]: https://github.com/rstacruz/nprogress/issues/73
[#75]: https://github.com/rstacruz/nprogress/issues/75
[#77]: https://github.com/rstacruz/nprogress/issues/77
[#7]: https://github.com/rstacruz/nprogress/issues/7
[#82]: https://github.com/rstacruz/nprogress/issues/82
[#84]: https://github.com/rstacruz/nprogress/issues/84
[#85]: https://github.com/rstacruz/nprogress/issues/85
[#86]: https://github.com/rstacruz/nprogress/issues/86
[#8]: https://github.com/rstacruz/nprogress/issues/8
[#90]: https://github.com/rstacruz/nprogress/issues/90
[#92]: https://github.com/rstacruz/nprogress/issues/92
[#93]: https://github.com/rstacruz/nprogress/issues/93
[#9]: https://github.com/rstacruz/nprogress/issues/9
[@slang800]: https://github.com/slang800
[@teeceepee]: https://github.com/teeceepee
[@FND]: https://github.com/FND
[@mdxs]: https://github.com/mdxs
[@infertux]: https://github.com/infertux
[@jonjaques]: https://github.com/jonjaques
[@amelon]: https://github.com/amelon
[@shaqq]: https://github.com/shaqq
[@Mithgol]: https://github.com/Mithgol
[@lacivert]: https://github.com/lacivert
[@rwholmes]: https://github.com/rwholmes
[@rurjur]: https://github.com/rurjur
[@markbao]: https://github.com/markbao
[@rahulcs]: https://github.com/rahulcs
[v0.1.6]: https://github.com/rstacruz/nprogress/compare/v0.1.5...v0.1.6
[v0.1.5]: https://github.com/rstacruz/nprogress/compare/v0.1.4...v0.1.5
[v0.1.4]: https://github.com/rstacruz/nprogress/compare/v0.1.3...v0.1.4
[v0.1.3]: https://github.com/rstacruz/nprogress/compare/v0.1.2...v0.1.3
[v0.1.2]: https://github.com/rstacruz/nprogress/compare/v0.1.1...v0.1.2
[v0.1.1]: https://github.com/rstacruz/nprogress/compare/v0.1.0...v0.1.1
[#53]: https://github.com/rstacruz/nprogress/issues/53
[v0.2.0]: https://github.com/rstacruz/nprogress/compare/v0.1.6...v0.2.0
