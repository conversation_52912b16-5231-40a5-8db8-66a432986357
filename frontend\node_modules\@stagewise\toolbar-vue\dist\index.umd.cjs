(function(global, factory) {
  typeof exports === "object" && typeof module !== "undefined" ? factory(exports, require("vue"), require("@stagewise/toolbar")) : typeof define === "function" && define.amd ? define(["exports", "vue", "@stagewise/toolbar"], factory) : (global = typeof globalThis !== "undefined" ? globalThis : global || self, factory(global.StagewiseToolbarVue = {}, global.vue, global["@stagewise/toolbar"]));
})(this, function(exports2, vue, toolbar) {
  "use strict";
  const _sfc_main = /* @__PURE__ */ vue.defineComponent({
    __name: "StagewiseToolbar",
    props: {
      config: {},
      enabled: { type: Boolean, default: () => process.env.NODE_ENV === "development" }
    },
    setup(__props) {
      const props = __props;
      vue.onMounted(() => {
        if (props.enabled) {
          toolbar.initToolbar(props.config);
        }
      });
      return (_ctx, _cache) => {
        return null;
      };
    }
  });
  exports2.StagewiseToolbar = _sfc_main;
  Object.defineProperty(exports2, Symbol.toStringTag, { value: "Module" });
});
