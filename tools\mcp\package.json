{"name": "thinkphp6-design-mcp", "version": "1.0.0", "description": "ThinkPHP6专业设计助手MCP服务器", "main": "mcp-thinkphp6-design.js", "type": "module", "bin": {"thinkphp6-design-mcp": "./mcp-thinkphp6-design.js"}, "scripts": {"start": "node mcp-thinkphp6-design.js", "dev": "node --inspect mcp-thinkphp6-design.js"}, "keywords": ["mcp", "thinkphp6", "design", "frontend", "ui", "responsive"], "author": "三只鱼网络科技 | 韩总", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.6.0"}, "engines": {"node": ">=18.0.0"}}