<?php
/**
 * 三只鱼网络科技 | 韩总 | 2025-06-08
 * 代码复刻工具 - 精确提取页面区域代码和样式
 */

class CodeExtractor
{
    private $projectRoot;
    private $extractedPath;

    public function __construct()
    {
        $this->projectRoot = dirname(__DIR__);
        $this->extractedPath = $this->projectRoot . '/extracted_code';
        
        if (!is_dir($this->extractedPath)) {
            mkdir($this->extractedPath, 0755, true);
        }
    }

    /**
     * 提取指定页面区域的完整代码
     */
    public function extractPageSection($pageName, $sectionName, $selector = '')
    {
        echo "=== 代码区域提取 ===\n";
        echo "页面: {$pageName}\n";
        echo "区域: {$sectionName}\n";
        echo "选择器: {$selector}\n\n";

        $result = [
            'html' => '',
            'css' => [],
            'js' => [],
            'images' => [],
            'dependencies' => []
        ];

        // 1. 查找HTML模板文件
        $templateFile = $this->findTemplateFile($pageName);
        if ($templateFile) {
            $result['html'] = $this->extractHtmlSection($templateFile, $sectionName, $selector);
            echo "✅ HTML提取完成: {$templateFile}\n";
        }

        // 2. 分析并提取相关CSS
        $result['css'] = $this->extractRelatedCSS($result['html'], $sectionName);
        echo "✅ CSS提取完成: " . count($result['css']) . " 个文件\n";

        // 3. 分析并提取相关JS
        $result['js'] = $this->extractRelatedJS($result['html'], $sectionName);
        echo "✅ JS提取完成: " . count($result['js']) . " 个文件\n";

        // 4. 提取图片资源
        $result['images'] = $this->extractImages($result['html']);
        echo "✅ 图片提取完成: " . count($result['images']) . " 个文件\n";

        // 5. 生成复刻代码包
        $packagePath = $this->generateCodePackage($sectionName, $result);
        echo "✅ 代码包生成: {$packagePath}\n";

        return $result;
    }

    /**
     * 根据描述智能匹配页面区域
     */
    public function smartExtract($description, $pageName = 'index')
    {
        echo "=== 智能区域匹配 ===\n";
        echo "描述: {$description}\n";
        echo "页面: {$pageName}\n\n";

        // 关键词映射
        $keywords = [
            '轮播' => ['banner', 'carousel', 'slider', 'swiper'],
            '导航' => ['nav', 'menu', 'header'],
            '统计' => ['stats', 'counter', 'number'],
            '团队' => ['team', 'member', 'staff'],
            '案例' => ['case', 'portfolio', 'project'],
            '新闻' => ['news', 'article', 'blog'],
            '联系' => ['contact', 'form'],
            '底部' => ['footer', 'bottom'],
            '关于' => ['about', 'intro']
        ];

        $matchedSelectors = [];
        foreach ($keywords as $key => $selectors) {
            if (strpos($description, $key) !== false) {
                $matchedSelectors = array_merge($matchedSelectors, $selectors);
            }
        }

        if (empty($matchedSelectors)) {
            echo "❌ 无法匹配描述，请提供更具体的关键词\n";
            return false;
        }

        echo "匹配到的选择器: " . implode(', ', $matchedSelectors) . "\n";

        // 尝试提取匹配的区域
        foreach ($matchedSelectors as $selector) {
            $result = $this->extractPageSection($pageName, $description, $selector);
            if (!empty($result['html'])) {
                echo "✅ 成功提取区域: {$selector}\n";
                return $result;
            }
        }

        echo "❌ 未找到匹配的区域\n";
        return false;
    }

    // 私有方法实现
    private function findTemplateFile($pageName)
    {
        $possiblePaths = [
            "app/view/{$pageName}.html",
            "app/view/index/{$pageName}.html",
            "app/view/{$pageName}/index.html",
            "public/{$pageName}.html"
        ];

        foreach ($possiblePaths as $path) {
            $fullPath = $this->projectRoot . '/' . $path;
            if (file_exists($fullPath)) {
                return $fullPath;
            }
        }

        return null;
    }

    private function extractHtmlSection($templateFile, $sectionName, $selector)
    {
        $content = file_get_contents($templateFile);
        
        if (empty($selector)) {
            // 基于注释或标签查找
            $patterns = [
                "/<section[^>]*class=\"[^\"]*{$sectionName}[^\"]*\"[^>]*>.*?<\/section>/s",
                "/<!--\s*{$sectionName}\s*start\s*-->.*?<!--\s*{$sectionName}\s*end\s*-->/s",
                "/<div[^>]*class=\"[^\"]*{$sectionName}[^\"]*\"[^>]*>.*?<\/div>/s"
            ];
        } else {
            // 使用指定选择器
            $patterns = [
                "/<[^>]*class=\"[^\"]*{$selector}[^\"]*\"[^>]*>.*?<\/[^>]*>/s",
                "/<[^>]*id=\"{$selector}\"[^>]*>.*?<\/[^>]*>/s"
            ];
        }

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                return $matches[0];
            }
        }

        return '';
    }

    private function extractRelatedCSS($html, $sectionName)
    {
        $cssFiles = [];
        
        // 查找CSS文件
        $cssPattern = '/public\/assets\/css\/[^"\']+\.css/';
        if (preg_match_all($cssPattern, $html, $matches)) {
            foreach ($matches[0] as $cssFile) {
                $fullPath = $this->projectRoot . '/' . $cssFile;
                if (file_exists($fullPath)) {
                    $cssFiles[] = [
                        'file' => $cssFile,
                        'content' => file_get_contents($fullPath)
                    ];
                }
            }
        }

        return $cssFiles;
    }

    private function extractRelatedJS($html, $sectionName)
    {
        $jsFiles = [];
        
        // 查找JS文件
        $jsPattern = '/public\/assets\/js\/[^"\']+\.js/';
        if (preg_match_all($jsPattern, $html, $matches)) {
            foreach ($matches[0] as $jsFile) {
                $fullPath = $this->projectRoot . '/' . $jsFile;
                if (file_exists($fullPath)) {
                    $jsFiles[] = [
                        'file' => $jsFile,
                        'content' => file_get_contents($fullPath)
                    ];
                }
            }
        }

        return $jsFiles;
    }

    private function extractImages($html)
    {
        $images = [];
        
        // 提取图片路径
        $imgPattern = '/src=["\']([^"\']*\.(jpg|jpeg|png|gif|svg|webp))["\'"]/i';
        if (preg_match_all($imgPattern, $html, $matches)) {
            foreach ($matches[1] as $imgSrc) {
                if (strpos($imgSrc, 'http') !== 0) {
                    $fullPath = $this->projectRoot . '/public/' . ltrim($imgSrc, '/');
                    if (file_exists($fullPath)) {
                        $images[] = $imgSrc;
                    }
                }
            }
        }

        return $images;
    }

    private function generateCodePackage($sectionName, $result)
    {
        $packageDir = $this->extractedPath . '/' . $sectionName . '_' . date('Ymd_His');
        if (!is_dir($packageDir)) {
            mkdir($packageDir, 0755, true);
        }

        // 生成HTML文件
        file_put_contents($packageDir . '/section.html', $result['html']);

        // 生成CSS文件
        $allCSS = '';
        foreach ($result['css'] as $css) {
            $allCSS .= "/* {$css['file']} */\n" . $css['content'] . "\n\n";
        }
        file_put_contents($packageDir . '/styles.css', $allCSS);

        // 生成JS文件
        $allJS = '';
        foreach ($result['js'] as $js) {
            $allJS .= "/* {$js['file']} */\n" . $js['content'] . "\n\n";
        }
        file_put_contents($packageDir . '/scripts.js', $allJS);

        // 生成使用说明
        $readme = "# {$sectionName} 代码包\n\n";
        $readme .= "## 文件说明\n";
        $readme .= "- section.html: HTML结构\n";
        $readme .= "- styles.css: 样式文件\n";
        $readme .= "- scripts.js: 脚本文件\n\n";
        $readme .= "## 图片资源\n";
        foreach ($result['images'] as $img) {
            $readme .= "- {$img}\n";
        }
        file_put_contents($packageDir . '/README.md', $readme);

        return $packageDir;
    }

    /**
     * 批量提取代码
     */
    public function batchExtract($directory) {
        $htmlFiles = $this->findHtmlFilesInDirectory($directory);

        if (empty($htmlFiles)) {
            echo "❌ 未找到HTML文件\n";
            return;
        }

        echo "📁 发现 " . count($htmlFiles) . " 个HTML文件\n\n";

        $results = [];
        foreach ($htmlFiles as $file) {
            $relativePath = str_replace($this->projectRoot . DIRECTORY_SEPARATOR, '', $file);
            $fileName = pathinfo($file, PATHINFO_FILENAME);

            echo "🔍 处理文件: $relativePath\n";
            echo str_repeat("-", 40) . "\n";

            // 分析文件中的主要区域
            $sections = $this->analyzeFileSections($file);

            if (!empty($sections)) {
                echo "发现 " . count($sections) . " 个区域:\n";
                foreach ($sections as $section) {
                    echo "  • {$section['name']} ({$section['type']})\n";

                    // 提取每个区域
                    $result = $this->extractPageSection($fileName, $section['name'], $section['selector']);
                    if (!empty($result['html'])) {
                        $results[] = [
                            'file' => $relativePath,
                            'section' => $section['name'],
                            'result' => $result
                        ];
                    }
                }
            } else {
                echo "  ⚠️ 未识别到明确的区域结构\n";
            }

            echo "\n";
        }

        // 生成批量提取报告
        $this->generateBatchReport($results);
    }

    /**
     * 生成代码模板
     */
    public function generateTemplate($templateName, $outputDir) {
        $templates = [
            'Bootstrap卡片' => $this->generateBootstrapCard(),
            'Hero区域' => $this->generateHeroSection(),
            '统计数字' => $this->generateStatsSection(),
            '团队介绍' => $this->generateTeamSection(),
            '联系表单' => $this->generateContactForm(),
            '导航菜单' => $this->generateNavigation(),
            '页脚' => $this->generateFooter()
        ];

        if (!isset($templates[$templateName])) {
            echo "❌ 未找到模板: $templateName\n";
            echo "📋 可用模板:\n";
            foreach (array_keys($templates) as $name) {
                echo "  • $name\n";
            }
            return;
        }

        $template = $templates[$templateName];
        $templateDir = $this->projectRoot . '/' . $outputDir . '/' . str_replace(' ', '_', $templateName) . '_' . date('Ymd_His');

        if (!is_dir($templateDir)) {
            mkdir($templateDir, 0755, true);
        }

        // 生成模板文件
        file_put_contents($templateDir . '/template.html', $template['html']);
        file_put_contents($templateDir . '/template.css', $template['css']);
        file_put_contents($templateDir . '/template.js', $template['js']);

        // 生成使用说明
        $readme = "# {$templateName} 模板\n\n";
        $readme .= "## 文件说明\n";
        $readme .= "- template.html: HTML结构\n";
        $readme .= "- template.css: 样式文件\n";
        $readme .= "- template.js: 脚本文件\n\n";
        $readme .= "## 使用方法\n";
        $readme .= $template['usage'] . "\n\n";
        $readme .= "## 自定义选项\n";
        $readme .= $template['customization'] . "\n";

        file_put_contents($templateDir . '/README.md', $readme);

        echo "✅ 模板生成完成: $templateDir\n";
        echo "📋 包含文件:\n";
        echo "  • template.html - HTML结构\n";
        echo "  • template.css - 样式文件\n";
        echo "  • template.js - 脚本文件\n";
        echo "  • README.md - 使用说明\n";
    }

    /**
     * 分析代码结构
     */
    public function analyzeCodeStructure($directory) {
        $htmlFiles = $this->findHtmlFilesInDirectory($directory);

        if (empty($htmlFiles)) {
            echo "❌ 未找到HTML文件\n";
            return;
        }

        echo "📊 代码结构分析报告\n";
        echo "📁 扫描目录: $directory\n";
        echo "📄 文件数量: " . count($htmlFiles) . "\n\n";

        $allComponents = [];
        $allClasses = [];
        $allIds = [];
        $frameworks = [];

        foreach ($htmlFiles as $file) {
            $relativePath = str_replace($this->projectRoot . DIRECTORY_SEPARATOR, '', $file);
            $content = file_get_contents($file);

            echo "🔍 分析文件: $relativePath\n";

            // 分析组件
            $components = $this->analyzeComponents($content);
            $allComponents = array_merge($allComponents, $components);

            // 分析CSS类
            preg_match_all('/class="([^"]*)"/', $content, $matches);
            foreach ($matches[1] as $classList) {
                $classes = explode(' ', $classList);
                $allClasses = array_merge($allClasses, $classes);
            }

            // 分析ID
            preg_match_all('/id="([^"]*)"/', $content, $idMatches);
            $allIds = array_merge($allIds, $idMatches[1]);

            // 检测框架
            if (strpos($content, 'bootstrap') !== false || strpos($content, 'col-') !== false) {
                $frameworks['Bootstrap'] = true;
            }
            if (strpos($content, 'layui') !== false) {
                $frameworks['LayUI'] = true;
            }
            if (strpos($content, 'element-ui') !== false) {
                $frameworks['Element UI'] = true;
            }

            echo "  • 组件数: " . count($components) . "\n";
            echo "  • CSS类数: " . count(explode(' ', implode(' ', $matches[1]))) . "\n";
            echo "  • ID数: " . count($idMatches[1]) . "\n\n";
        }

        // 生成统计报告
        $this->generateStructureReport($allComponents, $allClasses, $allIds, $frameworks, $directory);
    }

    /**
     * 在指定目录查找HTML文件
     */
    private function findHtmlFilesInDirectory($directory) {
        $files = [];
        $fullDir = $this->projectRoot . '/' . $directory;

        if (!is_dir($fullDir)) {
            echo "❌ 目录不存在: $directory\n";
            return $files;
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($fullDir, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'html') {
                $files[] = $file->getPathname();
            }
        }

        return $files;
    }

    /**
     * 分析文件中的区域
     */
    private function analyzeFileSections($file) {
        $content = file_get_contents($file);
        $sections = [];

        // 查找section标签
        if (preg_match_all('/<section[^>]*class="([^"]*)"[^>]*>/i', $content, $matches)) {
            foreach ($matches[1] as $class) {
                $sections[] = [
                    'name' => $class,
                    'type' => 'section',
                    'selector' => $class
                ];
            }
        }

        // 查找div容器
        if (preg_match_all('/<div[^>]*class="([^"]*(?:container|wrapper|section)[^"]*)"[^>]*>/i', $content, $matches)) {
            foreach ($matches[1] as $class) {
                $sections[] = [
                    'name' => $class,
                    'type' => 'container',
                    'selector' => $class
                ];
            }
        }

        // 查找注释标记
        if (preg_match_all('/<!--\s*([^-]+)\s*start\s*-->/i', $content, $matches)) {
            foreach ($matches[1] as $name) {
                $sections[] = [
                    'name' => trim($name),
                    'type' => 'comment',
                    'selector' => trim($name)
                ];
            }
        }

        return array_unique($sections, SORT_REGULAR);
    }

    /**
     * 生成批量提取报告
     */
    private function generateBatchReport($results) {
        echo str_repeat("=", 60) . "\n";
        echo "📊 批量代码提取报告\n";
        echo str_repeat("=", 60) . "\n";

        $totalExtractions = count($results);
        $fileCount = count(array_unique(array_column($results, 'file')));

        echo "📈 提取统计:\n";
        echo "  • 处理文件数: $fileCount\n";
        echo "  • 提取区域数: $totalExtractions\n\n";

        if (!empty($results)) {
            echo "📋 提取详情:\n";
            $currentFile = '';
            foreach ($results as $result) {
                if ($currentFile !== $result['file']) {
                    $currentFile = $result['file'];
                    echo "\n📄 {$result['file']}:\n";
                }
                echo "  ✅ {$result['section']}\n";
            }
        }

        echo "\n💡 提取的代码包保存在 extracted_code/ 目录下\n";
        echo "🎯 每个代码包包含 HTML、CSS、JS 和使用说明\n";
    }

    /**
     * 分析组件
     */
    private function analyzeComponents($content) {
        $components = [];

        // 常见组件模式
        $patterns = [
            'navbar' => '/<nav[^>]*>.*?<\/nav>/s',
            'carousel' => '/<div[^>]*carousel[^>]*>.*?<\/div>/s',
            'card' => '/<div[^>]*card[^>]*>.*?<\/div>/s',
            'modal' => '/<div[^>]*modal[^>]*>.*?<\/div>/s',
            'form' => '/<form[^>]*>.*?<\/form>/s',
            'table' => '/<table[^>]*>.*?<\/table>/s',
            'button' => '/<button[^>]*>.*?<\/button>/s'
        ];

        foreach ($patterns as $type => $pattern) {
            if (preg_match_all($pattern, $content, $matches)) {
                $components[$type] = count($matches[0]);
            }
        }

        return $components;
    }

    /**
     * 生成结构报告
     */
    private function generateStructureReport($allComponents, $allClasses, $allIds, $frameworks, $directory) {
        echo str_repeat("=", 60) . "\n";
        echo "📊 代码结构分析报告\n";
        echo str_repeat("=", 60) . "\n";

        // 组件统计
        echo "🧩 组件使用统计:\n";
        $componentCounts = array_count_values(array_keys($allComponents));
        foreach ($componentCounts as $component => $count) {
            echo "  • $component: $count 个\n";
        }

        // CSS类统计
        $classCounts = array_count_values($allClasses);
        arsort($classCounts);
        echo "\n🎨 CSS类使用频率 (前10个):\n";
        $count = 0;
        foreach ($classCounts as $class => $frequency) {
            if ($count >= 10 || empty(trim($class))) break;
            echo "  • $class: $frequency 次\n";
            $count++;
        }

        // 框架检测
        echo "\n🔧 检测到的框架:\n";
        if (!empty($frameworks)) {
            foreach (array_keys($frameworks) as $framework) {
                echo "  ✅ $framework\n";
            }
        } else {
            echo "  ❌ 未检测到常见前端框架\n";
        }

        // 保存详细报告
        $reportPath = $this->projectRoot . '/tools/code_structure_report.txt';
        $reportContent = "# 代码结构分析报告 - " . date('Y-m-d H:i:s') . "\n\n";
        $reportContent .= "## 扫描目录: $directory\n\n";
        $reportContent .= "## 组件统计\n";
        foreach ($componentCounts as $component => $count) {
            $reportContent .= "- $component: $count 个\n";
        }
        $reportContent .= "\n## CSS类统计\n";
        foreach ($classCounts as $class => $frequency) {
            if (empty(trim($class))) continue;
            $reportContent .= "- $class: $frequency 次\n";
        }
        $reportContent .= "\n## 框架使用\n";
        foreach (array_keys($frameworks) as $framework) {
            $reportContent .= "- $framework: 是\n";
        }

        file_put_contents($reportPath, $reportContent);
        echo "\n📝 详细报告已保存: tools/code_structure_report.txt\n";
    }

    /**
     * 生成Bootstrap卡片模板
     */
    private function generateBootstrapCard() {
        return [
            'html' => '<div class="card shadow-sm">
    <img src="https://via.placeholder.com/300x200" class="card-img-top" alt="卡片图片">
    <div class="card-body">
        <h5 class="card-title">卡片标题</h5>
        <p class="card-text">这里是卡片的描述内容，可以根据需要进行修改。</p>
        <a href="#" class="btn btn-primary">了解更多</a>
    </div>
</div>',
            'css' => '.card {
    border: none;
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-img-top {
    border-radius: 10px 10px 0 0;
    height: 200px;
    object-fit: cover;
}

.card-title {
    color: #333;
    font-weight: 600;
}

.card-text {
    color: #666;
    line-height: 1.6;
}',
            'js' => '// 卡片交互效果
document.querySelectorAll(\'.card\').forEach(card => {
    card.addEventListener(\'mouseenter\', function() {
        this.style.boxShadow = \'0 10px 25px rgba(0,0,0,0.15)\';
    });

    card.addEventListener(\'mouseleave\', function() {
        this.style.boxShadow = \'0 2px 10px rgba(0,0,0,0.1)\';
    });
});',
            'usage' => '1. 复制HTML代码到页面中\n2. 引入CSS样式\n3. 添加JS交互效果\n4. 修改图片、标题和描述内容',
            'customization' => '• 修改卡片尺寸：调整CSS中的宽高\n• 更换配色：修改颜色变量\n• 添加动画：调整transition属性\n• 自定义按钮：修改btn类样式'
        ];
    }

    /**
     * 生成Hero区域模板
     */
    private function generateHeroSection() {
        return [
            'html' => '<section class="hero-section">
    <div class="container">
        <div class="row align-items-center min-vh-100">
            <div class="col-lg-6">
                <h1 class="hero-title">欢迎来到我们的网站</h1>
                <p class="hero-subtitle">这里是副标题，描述您的产品或服务的核心价值。</p>
                <div class="hero-buttons">
                    <a href="#" class="btn btn-primary btn-lg me-3">开始使用</a>
                    <a href="#" class="btn btn-outline-secondary btn-lg">了解更多</a>
                </div>
            </div>
            <div class="col-lg-6">
                <img src="https://via.placeholder.com/600x400" class="img-fluid hero-image" alt="Hero图片">
            </div>
        </div>
    </div>
</section>',
            'css' => '.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons .btn {
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 50px;
}

.hero-image {
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
}',
            'js' => '// Hero区域动画效果
window.addEventListener(\'load\', function() {
    const heroTitle = document.querySelector(\'.hero-title\');
    const heroSubtitle = document.querySelector(\'.hero-subtitle\');
    const heroButtons = document.querySelector(\'.hero-buttons\');

    // 添加动画类
    heroTitle.style.opacity = \'0\';
    heroTitle.style.transform = \'translateY(30px)\';

    setTimeout(() => {
        heroTitle.style.transition = \'all 0.8s ease\';
        heroTitle.style.opacity = \'1\';
        heroTitle.style.transform = \'translateY(0)\';
    }, 200);

    setTimeout(() => {
        heroSubtitle.style.transition = \'all 0.8s ease\';
        heroSubtitle.style.opacity = \'1\';
        heroSubtitle.style.transform = \'translateY(0)\';
    }, 400);

    setTimeout(() => {
        heroButtons.style.transition = \'all 0.8s ease\';
        heroButtons.style.opacity = \'1\';
        heroButtons.style.transform = \'translateY(0)\';
    }, 600);
});',
            'usage' => '1. 将HTML代码放在页面顶部\n2. 引入CSS样式文件\n3. 添加JS动画效果\n4. 替换标题、描述和图片',
            'customization' => '• 更换背景：修改background属性\n• 调整布局：修改Bootstrap网格\n• 自定义动画：调整JS动画参数\n• 响应式优化：调整媒体查询'
        ];
    }

    /**
     * 生成统计数字模板
     */
    private function generateStatsSection() {
        return [
            'html' => '<section class="stats-section py-5">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-3 col-6 mb-4">
                <div class="stat-item">
                    <div class="stat-number" data-target="1000">0</div>
                    <div class="stat-label">满意客户</div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-4">
                <div class="stat-item">
                    <div class="stat-number" data-target="500">0</div>
                    <div class="stat-label">完成项目</div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-4">
                <div class="stat-item">
                    <div class="stat-number" data-target="50">0</div>
                    <div class="stat-label">团队成员</div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-4">
                <div class="stat-item">
                    <div class="stat-number" data-target="5">0</div>
                    <div class="stat-label">服务年限</div>
                </div>
            </div>
        </div>
    </div>
</section>',
            'css' => '.stats-section {
    background-color: #f8f9fa;
}

.stat-item {
    padding: 2rem 1rem;
    border-radius: 10px;
    background: white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    color: #666;
    font-weight: 500;
}

@media (max-width: 768px) {
    .stat-number {
        font-size: 2.5rem;
    }
}',
            'js' => '// 数字动画效果
function animateNumbers() {
    const numbers = document.querySelectorAll(\'.stat-number\');

    numbers.forEach(number => {
        const target = parseInt(number.getAttribute(\'data-target\'));
        const duration = 2000; // 2秒
        const step = target / (duration / 16); // 60fps
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            number.textContent = Math.floor(current);
        }, 16);
    });
}

// 滚动到视图时触发动画
const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            animateNumbers();
            observer.unobserve(entry.target);
        }
    });
});

document.addEventListener(\'DOMContentLoaded\', () => {
    const statsSection = document.querySelector(\'.stats-section\');
    if (statsSection) {
        observer.observe(statsSection);
    }
});',
            'usage' => '1. 复制HTML到页面中\n2. 引入CSS样式\n3. 添加JS动画脚本\n4. 修改数字和标签内容',
            'customization' => '• 修改目标数字：更改data-target属性\n• 调整动画速度：修改duration参数\n• 更换配色：修改CSS颜色变量\n• 添加图标：在stat-item中加入图标'
        ];
    }

    /**
     * 生成团队介绍模板
     */
    private function generateTeamSection() {
        return [
            'html' => '<section class="team-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="section-title">我们的团队</h2>
                <p class="section-subtitle">专业的团队为您提供优质的服务</p>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="team-member">
                    <img src="https://via.placeholder.com/300x300" class="member-photo" alt="团队成员">
                    <div class="member-info">
                        <h5 class="member-name">张三</h5>
                        <p class="member-position">技术总监</p>
                        <p class="member-description">10年开发经验，专注于前端技术</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>',
            'css' => '.team-section {
    background-color: #fff;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #666;
}

.team-member {
    text-align: center;
    padding: 2rem;
    border-radius: 15px;
    background: white;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.team-member:hover {
    transform: translateY(-10px);
}

.member-photo {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 1.5rem;
}

.member-name {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.member-position {
    color: #667eea;
    font-weight: 500;
    margin-bottom: 1rem;
}

.member-description {
    color: #666;
    line-height: 1.6;
}',
            'js' => '// 团队成员动画效果
document.addEventListener(\'DOMContentLoaded\', function() {
    const teamMembers = document.querySelectorAll(\'.team-member\');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = \'1\';
                    entry.target.style.transform = \'translateY(0)\';
                }, index * 200);
                observer.unobserve(entry.target);
            }
        });
    });

    teamMembers.forEach(member => {
        member.style.opacity = \'0\';
        member.style.transform = \'translateY(30px)\';
        member.style.transition = \'all 0.6s ease\';
        observer.observe(member);
    });
});',
            'usage' => '1. 复制HTML结构\n2. 引入CSS样式\n3. 添加JS动画\n4. 替换成员信息和照片',
            'customization' => '• 调整成员数量：复制team-member结构\n• 更换照片：修改img src\n• 自定义样式：调整CSS变量\n• 添加社交链接：在member-info中添加图标'
        ];
    }

    /**
     * 生成联系表单模板
     */
    private function generateContactForm() {
        return [
            'html' => '<section class="contact-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="contact-form-wrapper">
                    <h2 class="form-title text-center mb-4">联系我们</h2>
                    <form class="contact-form">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <input type="text" class="form-control" placeholder="您的姓名" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <input type="email" class="form-control" placeholder="邮箱地址" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <input type="text" class="form-control" placeholder="主题">
                        </div>
                        <div class="mb-3">
                            <textarea class="form-control" rows="5" placeholder="留言内容" required></textarea>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg">发送消息</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>',
            'css' => '.contact-section {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.contact-form-wrapper {
    background: white;
    padding: 3rem;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.form-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 12px 40px;
    border-radius: 50px;
    font-weight: 600;
    transition: transform 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
}',
            'js' => '// 表单验证和提交
document.addEventListener(\'DOMContentLoaded\', function() {
    const form = document.querySelector(\'.contact-form\');

    form.addEventListener(\'submit\', function(e) {
        e.preventDefault();

        // 简单验证
        const inputs = form.querySelectorAll(\'input[required], textarea[required]\');
        let isValid = true;

        inputs.forEach(input => {
            if (!input.value.trim()) {
                input.style.borderColor = \'#dc3545\';
                isValid = false;
            } else {
                input.style.borderColor = \'#28a745\';
            }
        });

        if (isValid) {
            // 这里可以添加实际的提交逻辑
            alert(\'消息发送成功！\');
            form.reset();
        } else {
            alert(\'请填写所有必填字段\');
        }
    });

    // 输入时清除错误状态
    const inputs = form.querySelectorAll(\'input, textarea\');
    inputs.forEach(input => {
        input.addEventListener(\'input\', function() {
            this.style.borderColor = \'#e9ecef\';
        });
    });
});',
            'usage' => '1. 复制HTML表单结构\n2. 引入CSS样式\n3. 添加JS验证逻辑\n4. 配置后端处理',
            'customization' => '• 添加字段：复制input结构\n• 修改验证：调整JS验证逻辑\n• 更换样式：修改CSS颜色和布局\n• 集成后端：修改表单提交处理'
        ];
    }

    /**
     * 生成导航菜单模板
     */
    private function generateNavigation() {
        return [
            'html' => '<nav class="navbar navbar-expand-lg navbar-light fixed-top">
    <div class="container">
        <a class="navbar-brand" href="#">
            <img src="https://via.placeholder.com/120x40" alt="Logo">
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto">
                <li class="nav-item">
                    <a class="nav-link active" href="#home">首页</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#about">关于我们</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#services">服务</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#contact">联系我们</a>
                </li>
            </ul>
        </div>
    </div>
</nav>',
            'css' => '.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.navbar-brand img {
    height: 40px;
}

.nav-link {
    font-weight: 500;
    color: #333 !important;
    margin: 0 10px;
    transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    color: #667eea !important;
}

.navbar-toggler {
    border: none;
    padding: 4px 8px;
}

.navbar-toggler:focus {
    box-shadow: none;
}',
            'js' => '// 导航栏滚动效果
window.addEventListener(\'scroll\', function() {
    const navbar = document.querySelector(\'.navbar\');
    if (window.scrollY > 50) {
        navbar.style.background = \'rgba(255, 255, 255, 0.98)\';
        navbar.style.boxShadow = \'0 2px 30px rgba(0,0,0,0.15)\';
    } else {
        navbar.style.background = \'rgba(255, 255, 255, 0.95)\';
        navbar.style.boxShadow = \'0 2px 20px rgba(0,0,0,0.1)\';
    }
});

// 平滑滚动
document.querySelectorAll(\'a[href^="#"]\').forEach(anchor => {
    anchor.addEventListener(\'click\', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute(\'href\'));
        if (target) {
            target.scrollIntoView({
                behavior: \'smooth\',
                block: \'start\'
            });
        }
    });
});',
            'usage' => '1. 复制导航HTML结构\n2. 引入CSS样式\n3. 添加JS交互效果\n4. 替换Logo和菜单项',
            'customization' => '• 修改菜单项：调整nav-item结构\n• 更换Logo：修改img src\n• 调整样式：修改CSS颜色和效果\n• 添加下拉菜单：使用Bootstrap组件'
        ];
    }

    /**
     * 生成页脚模板
     */
    private function generateFooter() {
        return [
            'html' => '<footer class="footer-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-4 mb-4">
                <h5 class="footer-title">关于我们</h5>
                <p class="footer-text">我们是一家专业的技术服务公司，致力于为客户提供优质的解决方案。</p>
            </div>
            <div class="col-lg-4 mb-4">
                <h5 class="footer-title">快速链接</h5>
                <ul class="footer-links">
                    <li><a href="#home">首页</a></li>
                    <li><a href="#about">关于我们</a></li>
                    <li><a href="#services">服务</a></li>
                    <li><a href="#contact">联系我们</a></li>
                </ul>
            </div>
            <div class="col-lg-4 mb-4">
                <h5 class="footer-title">联系信息</h5>
                <div class="contact-info">
                    <p><i class="fas fa-map-marker-alt"></i> 北京市朝阳区xxx街道</p>
                    <p><i class="fas fa-phone"></i> +86 138-0000-0000</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
            </div>
        </div>
        <hr class="footer-divider">
        <div class="row">
            <div class="col-12 text-center">
                <p class="copyright">&copy; 2024 公司名称. 保留所有权利.</p>
            </div>
        </div>
    </div>
</footer>',
            'css' => '.footer-section {
    background: #2c3e50;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #ecf0f1;
}

.footer-text {
    color: #bdc3c7;
    line-height: 1.6;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #3498db;
}

.contact-info p {
    color: #bdc3c7;
    margin-bottom: 0.5rem;
}

.contact-info i {
    color: #3498db;
    margin-right: 10px;
    width: 15px;
}

.footer-divider {
    border-color: #34495e;
    margin: 2rem 0 1rem;
}

.copyright {
    color: #95a5a6;
    margin: 0;
}',
            'js' => '// 页脚动画效果
document.addEventListener(\'DOMContentLoaded\', function() {
    const footerElements = document.querySelectorAll(\'.footer-title, .footer-text, .footer-links, .contact-info\');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = \'1\';
                    entry.target.style.transform = \'translateY(0)\';
                }, index * 100);
                observer.unobserve(entry.target);
            }
        });
    });

    footerElements.forEach(element => {
        element.style.opacity = \'0\';
        element.style.transform = \'translateY(20px)\';
        element.style.transition = \'all 0.6s ease\';
        observer.observe(element);
    });
});',
            'usage' => '1. 复制页脚HTML结构\n2. 引入CSS样式\n3. 添加Font Awesome图标库\n4. 修改联系信息和链接',
            'customization' => '• 修改联系信息：更新contact-info内容\n• 添加社交媒体：在页脚添加社交图标\n• 调整布局：修改Bootstrap网格\n• 更换配色：修改CSS颜色变量'
        ];
    }
}

// 命令行使用
if (php_sapi_name() === 'cli') {
    $extractor = new CodeExtractor();
    
    $command = $argv[1] ?? 'help';
    
    switch ($command) {
        case 'extract':
            $page = $argv[2] ?? 'index';
            $section = $argv[3] ?? '';
            $selector = $argv[4] ?? '';
            $extractor->extractPageSection($page, $section, $selector);
            break;
            
        case 'smart':
            $description = $argv[2] ?? '';
            $page = $argv[3] ?? 'index';
            if (empty($description)) {
                echo "❌ 用法: php tools/code_extractor.php smart [描述] [页面]\n";
                echo "💡 示例: php tools/code_extractor.php smart '首页轮播图' index\n";
                break;
            }
            echo "🧠 智能代码提取: $description\n";
            echo str_repeat("=", 50) . "\n";
            $extractor->smartExtract($description, $page);
            break;

        case 'batch':
            $directory = $argv[2] ?? 'app/view';
            echo "📁 批量代码提取: $directory\n";
            echo str_repeat("=", 50) . "\n";
            $extractor->batchExtract($directory);
            break;

        case 'template':
            $templateName = $argv[2] ?? '';
            $outputDir = $argv[3] ?? 'extracted_templates';
            if (empty($templateName)) {
                echo "❌ 用法: php tools/code_extractor.php template [模板名] [输出目录]\n";
                echo "💡 示例: php tools/code_extractor.php template 'Bootstrap卡片' templates\n";
                break;
            }
            echo "📋 模板代码生成: $templateName\n";
            echo str_repeat("=", 50) . "\n";
            $extractor->generateTemplate($templateName, $outputDir);
            break;

        case 'analyze':
            $directory = $argv[2] ?? 'app/view';
            echo "🔍 代码结构分析: $directory\n";
            echo str_repeat("=", 50) . "\n";
            $extractor->analyzeCodeStructure($directory);
            break;

        case 'help':
        default:
            echo "🔧 代码复刻工具 - 三只鱼网络科技\n\n";
            echo "📋 可用命令:\n";
            echo "  extract [页面] [区域] [选择器] - 提取指定页面区域代码\n";
            echo "  smart [描述] [页面]            - 智能匹配并提取代码\n";
            echo "  batch [目录]                   - 批量提取目录下所有页面\n";
            echo "  template [模板名] [输出目录]    - 生成代码模板\n";
            echo "  analyze [目录]                 - 分析代码结构和组件\n";
            echo "  help                           - 显示此帮助信息\n\n";
            echo "💡 示例:\n";
            echo "  php tools/code_extractor.php extract index 轮播图 banner\n";
            echo "  php tools/code_extractor.php smart '首页轮播图' index\n";
            echo "  php tools/code_extractor.php batch app/view\n";
            echo "  php tools/code_extractor.php template 'Bootstrap卡片' templates\n";
            echo "  php tools/code_extractor.php analyze public/diy\n\n";
            echo "🎯 功能特性:\n";
            echo "  • 精确提取页面区域代码和样式\n";
            echo "  • 智能匹配关键词自动提取\n";
            echo "  • 批量处理多个页面文件\n";
            echo "  • 生成可复用的代码模板\n";
            echo "  • 分析代码结构和依赖关系\n";
            echo "  • 自动打包HTML/CSS/JS资源\n";
    }
}
