/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 仪表盘API接口
 */

import { http } from '@/utils/http'

export interface DashboardStats {
  users: {
    total: number
    trend: number
    new_today: number
    active_today: number
  }
  pages: {
    total: number
    trend: number
    published: number
    draft: number
  }
  visits: {
    today: number
    trend: number
    total: number
    unique: number
  }
  messages: {
    unread: number
    trend: number
    total: number
  }
}

export interface SystemInfo {
  phpVersion: string
  database: string
  server: string
  uptime: string
  memory_usage: string
  disk_usage: string
}

export interface RecentActivity {
  id: number
  user: string
  action: string
  time: string
  avatar?: string
  type: 'create' | 'update' | 'delete' | 'login' | 'logout'
}

export interface VisitData {
  date: string
  visits: number
  unique_visits: number
  page_views: number
}

/**
 * 仪表盘相关API
 */
export const dashboardApi = {
  /**
   * 获取仪表盘统计数据
   */
  getStats: (): Promise<DashboardStats> => {
    return http.get('/admin/dashboard/stats')
  },

  /**
   * 获取系统信息
   */
  getSystemInfo: (): Promise<SystemInfo> => {
    return http.get('/admin/system/info')
  },

  /**
   * 获取最近活动
   */
  getRecentActivities: (limit: number = 10): Promise<RecentActivity[]> => {
    return http.get('/admin/dashboard/activities', { params: { limit } })
  },

  /**
   * 获取访问趋势数据
   */
  getVisitTrend: (period: string = '7d'): Promise<VisitData[]> => {
    return http.get('/admin/dashboard/visit-trend', { params: { period } })
  },

  /**
   * 获取快速统计
   */
  getQuickStats: (): Promise<{
    online_users: number
    today_orders: number
    today_revenue: number
    system_load: number
  }> => {
    return http.get('/admin/dashboard/quick-stats')
  }
}
