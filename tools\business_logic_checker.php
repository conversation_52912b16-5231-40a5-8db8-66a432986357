<?php
/**
 * 三只鱼网络科技 | 韩总 | 2025-01-15
 * ThinkPHP6业务逻辑检查工具 - 企业级应用业务完整性验证
 */

class BusinessLogicChecker
{
    private $projectRoot;
    private $issues = [];
    private $score = 100;
    private $businessModules = [];
    
    public function __construct($projectRoot = '.')
    {
        $this->projectRoot = realpath($projectRoot);
    }
    
    /**
     * 执行业务逻辑验证
     */
    public function validateBusinessLogic()
    {
        echo "🔍 ThinkPHP6业务逻辑检查工具\n";
        echo "============================================================\n\n";
        
        $this->discoverBusinessModules();
        $this->validateControllerMethods();
        $this->validateModelRelations();
        $this->validateServiceLayer();
        $this->validateDataValidation();
        $this->validateErrorHandling();
        $this->validateSecurityMechanisms();
        $this->validateAPIConsistency();
        
        $this->generateReport();
    }
    
    /**
     * 发现业务模块
     */
    private function discoverBusinessModules()
    {
        echo "🔎 业务模块发现...\n";
        echo "----------------------------------------\n";
        
        // 扫描控制器目录
        $controllerPath = $this->projectRoot . '/app/controller';
        if (is_dir($controllerPath)) {
            $controllers = $this->scanDirectory($controllerPath, '*.php');
            foreach ($controllers as $controller) {
                $moduleName = str_replace('.php', '', $controller);
                $this->businessModules[] = [
                    'name' => $moduleName,
                    'type' => 'frontend',
                    'controller' => $controller,
                    'model' => $this->findCorrespondingModel($moduleName)
                ];
            }
        }
        
        // 扫描后台控制器
        $adminControllerPath = $this->projectRoot . '/app/admin/controller';
        if (is_dir($adminControllerPath)) {
            $adminControllers = $this->scanDirectory($adminControllerPath, '*.php');
            foreach ($adminControllers as $controller) {
                $moduleName = str_replace('.php', '', $controller);
                $this->businessModules[] = [
                    'name' => $moduleName,
                    'type' => 'admin',
                    'controller' => $controller,
                    'model' => $this->findCorrespondingModel($moduleName)
                ];
            }
        }
        
        echo "✅ 发现业务模块: " . count($this->businessModules) . "个\n";
        foreach ($this->businessModules as $module) {
            echo "  📦 {$module['name']} ({$module['type']})\n";
        }
        echo "\n";
    }
    
    /**
     * 验证控制器方法完整性
     */
    private function validateControllerMethods()
    {
        echo "🎮 控制器方法完整性检查...\n";
        echo "----------------------------------------\n";
        
        foreach ($this->businessModules as $module) {
            $controllerPath = $module['type'] === 'admin' 
                ? $this->projectRoot . '/app/admin/controller/' . $module['controller']
                : $this->projectRoot . '/app/controller/' . $module['controller'];
                
            if (!file_exists($controllerPath)) {
                $this->addIssue('critical', '控制器完整性', "控制器文件不存在: {$module['controller']}");
                continue;
            }
            
            $content = file_get_contents($controllerPath);
            
            // 检查基本CRUD方法（针对管理模块）
            if ($module['type'] === 'admin' && $module['model']) {
                $requiredMethods = ['index', 'save', 'delete'];
                foreach ($requiredMethods as $method) {
                    if (strpos($content, "function {$method}(") === false) {
                        $this->addIssue('medium', '控制器完整性', 
                            "管理控制器 {$module['name']} 缺少 {$method} 方法");
                        $this->score -= 5;
                    }
                }
            }
            
            // 检查错误处理
            if (strpos($content, 'try') === false && strpos($content, 'catch') === false) {
                $this->addIssue('medium', '错误处理', 
                    "控制器 {$module['name']} 缺少异常处理机制");
                $this->score -= 3;
            }
            
            // 检查输入验证
            if (strpos($content, 'validate') === false && strpos($content, 'Validate') === false) {
                $this->addIssue('high', '输入验证', 
                    "控制器 {$module['name']} 可能缺少输入验证");
                $this->score -= 8;
            }
        }
        
        echo "✅ 控制器方法检查完成\n\n";
    }
    
    /**
     * 验证模型关联
     */
    private function validateModelRelations()
    {
        echo "🔗 模型关联验证...\n";
        echo "----------------------------------------\n";
        
        $modelPath = $this->projectRoot . '/app/model';
        if (!is_dir($modelPath)) {
            $this->addIssue('critical', '模型关联', '模型目录不存在');
            return;
        }
        
        $models = $this->scanDirectory($modelPath, '*.php');
        $relationCount = 0;
        
        foreach ($models as $model) {
            $content = file_get_contents($modelPath . '/' . $model);
            $modelName = str_replace('.php', '', $model);
            
            // 检查表名设置
            if (strpos($content, 'protected $name') === false && 
                strpos($content, 'protected $table') === false) {
                $this->addIssue('medium', '模型配置', 
                    "模型 {$modelName} 未明确设置表名");
                $this->score -= 3;
            }
            
            // 检查字段定义
            if (strpos($content, 'protected $field') === false && 
                strpos($content, 'protected $schema') === false) {
                $this->addIssue('low', '模型配置', 
                    "模型 {$modelName} 建议定义字段信息");
                $this->score -= 2;
            }
            
            // 统计关联方法
            $relations = ['hasOne', 'hasMany', 'belongsTo', 'belongsToMany'];
            foreach ($relations as $relation) {
                if (strpos($content, $relation) !== false) {
                    $relationCount++;
                }
            }
        }
        
        echo "✅ 模型关联统计: {$relationCount}个\n";
        
        // 检查关联合理性
        if (count($models) > 3 && $relationCount === 0) {
            $this->addIssue('medium', '模型关联', '多个模型但无关联关系，可能存在数据孤岛');
            $this->score -= 10;
        }
        
        echo "\n";
    }
    
    /**
     * 验证服务层
     */
    private function validateServiceLayer()
    {
        echo "⚙️ 服务层验证...\n";
        echo "----------------------------------------\n";
        
        $servicePath = $this->projectRoot . '/app/service';
        if (!is_dir($servicePath)) {
            $this->addIssue('medium', '服务层', '建议创建服务层封装复杂业务逻辑');
            $this->score -= 8;
            echo "⚠️ 服务层目录不存在\n\n";
            return;
        }
        
        $services = $this->scanDirectory($servicePath, '*.php');
        echo "✅ 服务类: " . count($services) . "个\n";
        
        // 检查服务类质量
        foreach ($services as $service) {
            $content = file_get_contents($servicePath . '/' . $service);
            $serviceName = str_replace('.php', '', $service);
            
            // 检查是否有业务逻辑方法
            if (substr_count($content, 'public function') < 2) {
                $this->addIssue('low', '服务层', 
                    "服务类 {$serviceName} 方法较少，可能未充分封装业务逻辑");
                $this->score -= 2;
            }
        }
        
        echo "\n";
    }
    
    /**
     * 验证数据验证
     */
    private function validateDataValidation()
    {
        echo "✅ 数据验证机制检查...\n";
        echo "----------------------------------------\n";
        
        $validatePath = $this->projectRoot . '/app/validate';
        if (!is_dir($validatePath)) {
            $this->addIssue('high', '数据验证', '缺少验证器目录，建议创建验证器类');
            $this->score -= 15;
            echo "⚠️ 验证器目录不存在\n\n";
            return;
        }
        
        $validators = $this->scanDirectory($validatePath, '*.php');
        echo "✅ 验证器: " . count($validators) . "个\n";
        
        // 检查验证器与模型的对应关系
        $modelPath = $this->projectRoot . '/app/model';
        if (is_dir($modelPath)) {
            $models = $this->scanDirectory($modelPath, '*.php');
            $modelCount = count($models);
            $validatorCount = count($validators);
            
            if ($modelCount > $validatorCount && $validatorCount === 0) {
                $this->addIssue('high', '数据验证', 
                    "有 {$modelCount} 个模型但无验证器，数据验证可能不完整");
                $this->score -= 12;
            }
        }
        
        echo "\n";
    }
    
    /**
     * 验证错误处理
     */
    private function validateErrorHandling()
    {
        echo "🚨 错误处理机制检查...\n";
        echo "----------------------------------------\n";
        
        // 检查异常处理类
        $exceptionHandler = $this->projectRoot . '/app/ExceptionHandle.php';
        if (!file_exists($exceptionHandler)) {
            $this->addIssue('high', '错误处理', '缺少自定义异常处理类');
            $this->score -= 10;
        } else {
            echo "✅ 异常处理类存在\n";
        }
        
        // 检查日志配置
        $logConfig = $this->projectRoot . '/config/log.php';
        if (!file_exists($logConfig)) {
            $this->addIssue('medium', '错误处理', '缺少日志配置文件');
            $this->score -= 5;
        } else {
            echo "✅ 日志配置存在\n";
        }
        
        echo "\n";
    }
    
    /**
     * 验证安全机制
     */
    private function validateSecurityMechanisms()
    {
        echo "🛡️ 安全机制检查...\n";
        echo "----------------------------------------\n";
        
        // 检查中间件认证
        $middlewarePath = $this->projectRoot . '/app/middleware';
        $hasAuthMiddleware = false;
        
        if (is_dir($middlewarePath)) {
            $middlewares = $this->scanDirectory($middlewarePath, '*.php');
            foreach ($middlewares as $middleware) {
                if (strpos(strtolower($middleware), 'auth') !== false) {
                    $hasAuthMiddleware = true;
                    break;
                }
            }
        }
        
        if (!$hasAuthMiddleware) {
            $this->addIssue('high', '安全机制', '缺少身份认证中间件');
            $this->score -= 12;
        } else {
            echo "✅ 身份认证中间件存在\n";
        }
        
        // 检查CSRF保护
        $appConfig = $this->projectRoot . '/config/app.php';
        if (file_exists($appConfig)) {
            $content = file_get_contents($appConfig);
            if (strpos($content, 'csrf') === false) {
                $this->addIssue('medium', '安全机制', '建议启用CSRF保护');
                $this->score -= 5;
            }
        }
        
        echo "\n";
    }
    
    /**
     * 验证API一致性
     */
    private function validateAPIConsistency()
    {
        echo "🔌 API一致性检查...\n";
        echo "----------------------------------------\n";
        
        $apiPath = $this->projectRoot . '/app/api';
        if (!is_dir($apiPath)) {
            echo "ℹ️ 未发现API目录，跳过API检查\n\n";
            return;
        }
        
        $apiControllers = $this->scanDirectory($apiPath . '/controller', '*.php');
        echo "✅ API控制器: " . count($apiControllers) . "个\n";
        
        // 检查API响应格式一致性
        foreach ($apiControllers as $controller) {
            $content = file_get_contents($apiPath . '/controller/' . $controller);
            
            // 检查是否使用统一的响应格式
            if (strpos($content, 'json(') !== false) {
                if (strpos($content, 'success') === false || 
                    strpos($content, 'message') === false) {
                    $this->addIssue('medium', 'API一致性', 
                        "API控制器 {$controller} 响应格式可能不统一");
                    $this->score -= 5;
                }
            }
        }
        
        echo "\n";
    }
    
    /**
     * 生成检查报告
     */
    private function generateReport()
    {
        echo "============================================================\n";
        echo "📊 业务逻辑检查报告\n";
        echo "============================================================\n\n";
        
        // 评分
        $grade = $this->getGrade($this->score);
        echo "🎯 业务逻辑完整性评分: {$this->score}/100 ({$grade})\n\n";
        
        // 问题统计
        $criticalCount = count(array_filter($this->issues, fn($issue) => $issue['level'] === 'critical'));
        $highCount = count(array_filter($this->issues, fn($issue) => $issue['level'] === 'high'));
        $mediumCount = count(array_filter($this->issues, fn($issue) => $issue['level'] === 'medium'));
        $lowCount = count(array_filter($this->issues, fn($issue) => $issue['level'] === 'low'));
        
        echo "❗ 发现问题 (" . count($this->issues) . "个):\n";
        if ($criticalCount > 0) echo "  🔴 严重: {$criticalCount}个\n";
        if ($highCount > 0) echo "  🟠 高危: {$highCount}个\n";
        if ($mediumCount > 0) echo "  🟡 中等: {$mediumCount}个\n";
        if ($lowCount > 0) echo "  🔵 轻微: {$lowCount}个\n";
        
        if (count($this->issues) > 0) {
            echo "\n📋 问题详情:\n";
            foreach ($this->issues as $issue) {
                $icon = $this->getIssueIcon($issue['level']);
                echo "  {$icon} [{$issue['category']}] {$issue['message']}\n";
            }
        }
        
        echo "\n💡 改进建议:\n";
        $this->generateRecommendations();
        
        echo "\n✅ 业务逻辑检查完成！\n";
    }
    
    // 辅助方法
    private function scanDirectory($path, $pattern = '*')
    {
        if (!is_dir($path)) return [];
        $files = glob($path . '/' . $pattern);
        return array_map('basename', $files);
    }
    
    private function findCorrespondingModel($controllerName)
    {
        $modelPath = $this->projectRoot . '/app/model';
        $possibleModels = [
            $controllerName . '.php',
            ucfirst($controllerName) . '.php',
            rtrim($controllerName, 's') . '.php' // 复数转单数
        ];
        
        foreach ($possibleModels as $model) {
            if (file_exists($modelPath . '/' . $model)) {
                return $model;
            }
        }
        
        return null;
    }
    
    private function addIssue($level, $category, $message)
    {
        $this->issues[] = [
            'level' => $level,
            'category' => $category,
            'message' => $message
        ];
    }
    
    private function getGrade($score)
    {
        if ($score >= 90) return '优秀';
        if ($score >= 80) return '良好';
        if ($score >= 70) return '一般';
        if ($score >= 60) return '及格';
        return '需改进';
    }
    
    private function getIssueIcon($level)
    {
        return match($level) {
            'critical' => '🔴',
            'high' => '🟠',
            'medium' => '🟡',
            'low' => '🔵',
            default => 'ℹ️'
        };
    }
    
    private function generateRecommendations()
    {
        if ($this->score >= 90) {
            echo "  • 业务逻辑设计优秀，继续保持良好的开发规范\n";
        } elseif ($this->score >= 80) {
            echo "  • 业务逻辑基础良好，建议完善验证和错误处理机制\n";
        } else {
            echo "  • 建议优先完善数据验证和安全机制\n";
            echo "  • 加强错误处理和异常管理\n";
            echo "  • 考虑引入服务层封装复杂业务逻辑\n";
        }
    }
}

// 命令行执行
if (php_sapi_name() === 'cli') {
    $checker = new BusinessLogicChecker();
    
    if (isset($argv[1]) && $argv[1] === 'validate') {
        $checker->validateBusinessLogic();
    } else {
        echo "用法: php business_logic_checker.php validate\n";
    }
}
