<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 修改密码对话框
-->

<template>
  <el-dialog
    v-model="visible"
    title="修改密码"
    width="500px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="当前密码" prop="oldPassword">
        <el-input
          v-model="form.oldPassword"
          type="password"
          placeholder="请输入当前密码"
          show-password
          maxlength="50"
        />
      </el-form-item>

      <el-form-item label="新密码" prop="newPassword">
        <el-input
          v-model="form.newPassword"
          type="password"
          placeholder="请输入新密码"
          show-password
          maxlength="50"
        />
        <div class="password-tips">
          <p>密码要求：</p>
          <ul>
            <li :class="{ valid: passwordChecks.length }">长度8-50个字符</li>
            <li :class="{ valid: passwordChecks.uppercase }">包含大写字母</li>
            <li :class="{ valid: passwordChecks.lowercase }">包含小写字母</li>
            <li :class="{ valid: passwordChecks.number }">包含数字</li>
            <li :class="{ valid: passwordChecks.special }">包含特殊字符</li>
          </ul>
        </div>
      </el-form-item>

      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input
          v-model="form.confirmPassword"
          type="password"
          placeholder="请再次输入新密码"
          show-password
          maxlength="50"
        />
      </el-form-item>

      <el-form-item>
        <div class="password-strength">
          <span>密码强度：</span>
          <div class="strength-bar">
            <div 
              class="strength-fill" 
              :class="strengthClass"
              :style="{ width: strengthWidth }"
            ></div>
          </div>
          <span class="strength-text" :class="strengthClass">{{ strengthText }}</span>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确认修改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const form = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码强度检查
const passwordChecks = computed(() => ({
  length: form.newPassword.length >= 8 && form.newPassword.length <= 50,
  uppercase: /[A-Z]/.test(form.newPassword),
  lowercase: /[a-z]/.test(form.newPassword),
  number: /\d/.test(form.newPassword),
  special: /[!@#$%^&*(),.?":{}|<>]/.test(form.newPassword)
}))

// 密码强度计算
const passwordStrength = computed(() => {
  const checks = Object.values(passwordChecks.value)
  const validCount = checks.filter(Boolean).length
  return validCount
})

// 密码强度样式
const strengthClass = computed(() => {
  const strength = passwordStrength.value
  if (strength <= 2) return 'weak'
  if (strength <= 3) return 'medium'
  if (strength <= 4) return 'strong'
  return 'very-strong'
})

const strengthWidth = computed(() => {
  return `${(passwordStrength.value / 5) * 100}%`
})

const strengthText = computed(() => {
  const strength = passwordStrength.value
  if (strength <= 2) return '弱'
  if (strength <= 3) return '中等'
  if (strength <= 4) return '强'
  return '很强'
})

// 自定义验证函数
const validatePassword = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请输入新密码'))
    return
  }
  
  if (value.length < 8 || value.length > 50) {
    callback(new Error('密码长度必须在8-50个字符之间'))
    return
  }
  
  if (passwordStrength.value < 3) {
    callback(new Error('密码强度太弱，请包含大小写字母、数字和特殊字符'))
    return
  }
  
  callback()
}

const validateConfirmPassword = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请确认新密码'))
    return
  }
  
  if (value !== form.newPassword) {
    callback(new Error('两次输入的密码不一致'))
    return
  }
  
  callback()
}

// 表单验证规则
const rules: FormRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { validator: validatePassword, trigger: 'blur' }
  ],
  confirmPassword: [
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 控制对话框显示
const visible = ref(false)

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val
    if (val) {
      resetForm()
    }
  },
  { immediate: true }
)

watch(visible, (val) => {
  emit('update:modelValue', val)
})

/**
 * 重置表单
 */
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))

    ElMessage.success('密码修改成功，请重新登录')
    handleClose()
    
    // 这里可以触发退出登录
    // await logout()
  } catch (error) {
    console.error('修改密码失败:', error)
    ElMessage.error('修改密码失败，请检查当前密码是否正确')
  } finally {
    loading.value = false
  }
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.password-tips {
  margin-top: 8px;
  font-size: 12px;
  color: #666;

  p {
    margin: 0 0 4px 0;
    font-weight: 500;
  }

  ul {
    margin: 0;
    padding-left: 16px;
    list-style: none;

    li {
      position: relative;
      margin-bottom: 2px;
      color: #999;

      &::before {
        content: '✗';
        position: absolute;
        left: -16px;
        color: #f56c6c;
      }

      &.valid {
        color: #67c23a;

        &::before {
          content: '✓';
          color: #67c23a;
        }
      }
    }
  }
}

.password-strength {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;

  .strength-bar {
    flex: 1;
    height: 6px;
    background-color: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;

    .strength-fill {
      height: 100%;
      border-radius: 3px;
      transition: all 0.3s ease;

      &.weak {
        background-color: #f56c6c;
      }

      &.medium {
        background-color: #e6a23c;
      }

      &.strong {
        background-color: #409eff;
      }

      &.very-strong {
        background-color: #67c23a;
      }
    }
  }

  .strength-text {
    min-width: 30px;
    font-weight: 500;

    &.weak {
      color: #f56c6c;
    }

    &.medium {
      color: #e6a23c;
    }

    &.strong {
      color: #409eff;
    }

    &.very-strong {
      color: #67c23a;
    }
  }
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}
</style>
