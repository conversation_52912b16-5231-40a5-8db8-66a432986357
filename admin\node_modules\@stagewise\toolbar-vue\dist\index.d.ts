import { ComponentOptionsMixin } from 'vue';
import { ComponentProvideOptions } from 'vue';
import { DefineComponent } from 'vue';
import { PublicProps } from 'vue';
import { ToolbarConfig } from '@stagewise/toolbar';

declare type __VLS_Props = {
    config?: ToolbarConfig;
    enabled?: boolean;
};

export declare const StagewiseToolbar: DefineComponent<__VLS_Props, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<__VLS_Props> & Readonly<{}>, {
enabled: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, true, {}, any>;

export { ToolbarConfig }

export { }
