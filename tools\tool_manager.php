<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * 工具管理器 - ThinkPHP6企业级应用
 * 功能：统一管理所有开发工具，提供一键检测和批量操作
 */

class ToolManager {
    
    private $toolsPath;
    private $projectRoot;
    private $availableTools = [
        // 核心工具(必需) - 开发流程必备
        'layout' => [
            'name' => '布局分析工具',
            'file' => 'layout_analyzer.php',
            'description' => '检测页面布局对称性、网格系统、响应式设计',
            'priority' => 'high',
            'usage' => 'frequent'
        ],
        'db' => [
            'name' => '数据库分析工具',
            'file' => 'db_analyzer.php',
            'description' => '快速获取数据库结构和统计信息',
            'priority' => 'high',
            'usage' => 'frequent'
        ],
        'performance' => [
            'name' => '性能分析工具',
            'file' => 'performance_analyzer.php',
            'description' => '分析代码性能、数据库查询、缓存使用',
            'priority' => 'high',
            'usage' => 'frequent'
        ],
        'architecture' => [
            'name' => '架构分析工具',
            'file' => 'architecture_analyzer.php',
            'description' => 'ThinkPHP6架构完整性检测、MVC分层验证',
            'priority' => 'high',
            'usage' => 'frequent'
        ],
        'business' => [
            'name' => '业务逻辑检查工具',
            'file' => 'business_logic_checker.php',
            'description' => '业务逻辑完整性验证、控制器方法检查',
            'priority' => 'high',
            'usage' => 'frequent'
        ],
        'security' => [
            'name' => '安全审计工具',
            'file' => 'security_auditor.php',
            'description' => '安全漏洞扫描、SQL注入检测、XSS防护',
            'priority' => 'high',
            'usage' => 'frequent'
        ],

        // 辅助工具(按需) - 特定场景使用
        'cssjs' => [
            'name' => 'CSS/JS优化工具',
            'file' => 'css_js_optimizer.php',
            'description' => '检测重复CSS、未使用样式、JS冗余清理',
            'priority' => 'medium',
            'usage' => 'periodic'
        ],
        'style' => [
            'name' => '样式检查工具',
            'file' => 'style_checker.php',
            'description' => '检查样式兼容性、设计规范、代码完整性',
            'priority' => 'medium',
            'usage' => 'occasional'
        ],
        'code' => [
            'name' => '代码提取工具',
            'file' => 'code_extractor.php',
            'description' => '智能提取页面区域代码和样式、模板生成',
            'priority' => 'low',
            'usage' => 'occasional'
        ],
        'file' => [
            'name' => '文件分析工具',
            'file' => 'file_analyzer.php',
            'description' => '批量文件查看、内容搜索、质量检查',
            'priority' => 'low',
            'usage' => 'rare'
        ]
    ];
    
    public function __construct() {
        $this->projectRoot = dirname(__DIR__);
        $this->toolsPath = $this->projectRoot . '/tools';
    }
    
    /**
     * 显示所有可用工具
     */
    public function listTools() {
        echo "🔧 开发工具集 - 三只鱼网络科技\n";
        echo str_repeat("=", 60) . "\n";

        // 按优先级分组显示
        $priorities = ['high' => '🔥 核心工具(必需)', 'medium' => '⚡ 辅助工具(按需)', 'low' => '🛠️ 扩展工具(可选)'];

        foreach ($priorities as $priority => $title) {
            echo "\n$title:\n";
            foreach ($this->availableTools as $tool) {
                if (($tool['priority'] ?? 'medium') === $priority) {
                    $status = file_exists($this->toolsPath . '/' . $tool['file']) ? '✅' : '❌';
                    $usage = $tool['usage'] ?? 'occasional';
                    echo sprintf("  %s %-18s %s [%s]\n", $status, $tool['name'], $tool['description'], $usage);
                }
            }
        }

        echo "\n💡 推荐使用流程:\n";
        echo "  1. 项目检查: php tools/tool_manager.php batch . (完整检查)\n";
        echo "  2. 架构验证: php tools/architecture_analyzer.php full\n";
        echo "  3. 业务检查: php tools/business_logic_checker.php validate\n";
        echo "  4. 安全审计: php tools/security_auditor.php scan\n";
        echo "  5. 布局检测: php tools/layout_analyzer.php check 文件\n";
        echo "  6. 性能验证: php tools/performance_analyzer.php full\n";

        echo "\n🎯 快捷命令:\n";
        echo "  check [文件] - 一键全面检测\n";
        echo "  batch [目录] - 批量分析\n";
        echo "  run [工具] [参数] - 运行指定工具\n";
    }
    
    /**
     * 运行指定工具
     */
    public function runTool($toolName, $args = []) {
        if (!isset($this->availableTools[$toolName])) {
            echo "❌ 未知工具: $toolName\n";
            echo "💡 使用 'php tools/tool_manager.php list' 查看可用工具\n";
            return false;
        }
        
        $tool = $this->availableTools[$toolName];
        $toolFile = $this->toolsPath . '/' . $tool['file'];
        
        if (!file_exists($toolFile)) {
            echo "❌ 工具文件不存在: {$tool['file']}\n";
            return false;
        }
        
        echo "🚀 启动工具: {$tool['name']}\n";
        echo str_repeat("-", 40) . "\n";
        
        // 构建命令
        $command = "php $toolFile " . implode(' ', array_map('escapeshellarg', $args));
        
        // 执行工具
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        foreach ($output as $line) {
            echo $line . "\n";
        }
        
        return $returnCode === 0;
    }
    
    /**
     * 一键全面检测
     */
    public function quickCheck($target) {
        echo "🔍 一键全面检测\n";
        echo "目标: $target\n";
        echo str_repeat("=", 60) . "\n";
        
        $results = [];
        
        // 1. 布局检测
        if ($this->isHtmlFile($target)) {
            echo "\n📐 布局质量检测...\n";
            $layoutResult = $this->runLayoutCheck($target);
            $results['layout'] = $layoutResult;
        }
        
        // 2. 样式检测
        echo "\n🎨 样式规范检测...\n";
        $styleResult = $this->runStyleCheck($target);
        $results['style'] = $styleResult;
        
        // 3. CSS/JS优化检测
        echo "\n🎨 CSS/JS优化检测...\n";
        $cssJsResult = $this->runCssJsCheck();
        $results['cssjs'] = $cssJsResult;

        // 4. 代码质量检测
        echo "\n📝 代码质量检测...\n";
        $qualityResult = $this->runQualityCheck($target);
        $results['quality'] = $qualityResult;
        
        // 生成综合报告
        $this->generateQuickReport($results);
        
        return $results;
    }
    
    /**
     * 批量分析目录
     */
    public function batchAnalyze($directory) {
        echo "📁 批量分析目录: $directory\n";
        echo str_repeat("=", 60) . "\n";

        if (!is_dir($directory)) {
            echo "❌ 目录不存在: $directory\n";
            return false;
        }

        // 1. 文件结构分析
        echo "\n📊 文件结构分析...\n";
        $this->runTool('file', ['structure', $directory]);

        // 2. 架构完整性检测
        echo "\n🏗️ 架构完整性检测...\n";
        $this->runTool('architecture', ['full']);

        // 3. 业务逻辑验证
        echo "\n🔍 业务逻辑验证...\n";
        $this->runTool('business', ['validate']);

        // 4. 安全审计扫描
        echo "\n🛡️ 安全审计扫描...\n";
        $this->runTool('security', ['scan']);

        // 5. 批量布局检测
        echo "\n📐 批量布局检测...\n";
        $this->runTool('layout', ['batch', $directory]);

        // 6. 代码质量检查
        echo "\n📝 代码质量检查...\n";
        $this->runTool('file', ['quality', '*.php']);

        echo "\n✅ 批量分析完成！\n";
        return true;
    }
    
    // 私有方法
    private function isHtmlFile($file) {
        return is_file($file) && pathinfo($file, PATHINFO_EXTENSION) === 'html';
    }
    
    private function runLayoutCheck($file) {
        $command = "php {$this->toolsPath}/layout_analyzer.php check " . escapeshellarg($file);
        $output = [];
        exec($command, $output);
        
        // 解析评分
        $score = 0;
        foreach ($output as $line) {
            if (preg_match('/布局质量评分:\s*(\d+)/', $line, $matches)) {
                $score = intval($matches[1]);
                break;
            }
        }
        
        return ['score' => $score, 'output' => $output];
    }
    
    private function runStyleCheck($target) {
        $command = "php {$this->toolsPath}/style_checker.php analyze " . escapeshellarg($target);
        $output = [];
        exec($command, $output);
        
        return ['output' => $output];
    }
    
    private function runCssJsCheck() {
        $command = "php {$this->toolsPath}/css_js_optimizer.php full";
        $output = [];
        exec($command, $output);

        // 解析优化统计
        $duplicateRules = 0;
        $unusedRules = 0;

        foreach ($output as $line) {
            if (preg_match('/CSS重复规则:\s*(\d+)/', $line, $matches)) {
                $duplicateRules = intval($matches[1]);
            }
            if (preg_match('/未使用CSS规则:\s*(\d+)/', $line, $matches)) {
                $unusedRules = intval($matches[1]);
            }
        }

        return [
            'duplicate_rules' => $duplicateRules,
            'unused_rules' => $unusedRules,
            'output' => $output
        ];
    }

    private function runQualityCheck($target) {
        $command = "php {$this->toolsPath}/file_analyzer.php quality *.php";
        $output = [];
        exec($command, $output);

        return ['output' => $output];
    }
    
    private function generateQuickReport($results) {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📊 综合检测报告\n";
        echo str_repeat("=", 60) . "\n";
        
        if (isset($results['layout'])) {
            $score = $results['layout']['score'];
            $status = $score >= 80 ? '✅ 优秀' : ($score >= 60 ? '⚠️ 良好' : '❌ 需改进');
            echo "📐 布局质量: $score/100 $status\n";
        }
        
        echo "🎨 样式规范: 已检测\n";

        if (isset($results['cssjs'])) {
            $duplicates = $results['cssjs']['duplicate_rules'];
            $unused = $results['cssjs']['unused_rules'];
            $status = ($duplicates + $unused) < 100 ? '✅ 良好' : '⚠️ 需优化';
            echo "🔧 CSS/JS优化: 重复{$duplicates}个, 未使用{$unused}个 $status\n";
        }

        echo "📝 代码质量: 已检测\n";

        echo "\n💡 建议:\n";
        if (isset($results['layout']) && $results['layout']['score'] < 80) {
            echo "  • 优先修复布局问题，提升页面质量\n";
        }
        if (isset($results['cssjs']) && ($results['cssjs']['duplicate_rules'] + $results['cssjs']['unused_rules']) > 100) {
            echo "  • CSS/JS存在大量冗余，建议清理优化\n";
        }
        echo "  • 定期运行检测，保持代码质量\n";
        echo "  • 遵循开发规范，减少问题产生\n";
    }
}

// 命令行使用
if (php_sapi_name() === 'cli') {
    $manager = new ToolManager();
    
    $command = $argv[1] ?? 'help';
    
    switch ($command) {
        case 'list':
            $manager->listTools();
            break;
            
        case 'run':
            $toolName = $argv[2] ?? '';
            $args = array_slice($argv, 3);
            if (empty($toolName)) {
                echo "❌ 请指定工具名称\n";
            } else {
                $manager->runTool($toolName, $args);
            }
            break;
            
        case 'check':
            $target = $argv[2] ?? '';
            if (empty($target)) {
                echo "❌ 请指定检测目标\n";
            } else {
                $manager->quickCheck($target);
            }
            break;
            
        case 'batch':
            $directory = $argv[2] ?? '';
            if (empty($directory)) {
                echo "❌ 请指定目录路径\n";
            } else {
                $manager->batchAnalyze($directory);
            }
            break;
            
        default:
            echo "🔧 工具管理器 - 三只鱼网络科技\n\n";
            echo "📋 可用命令:\n";
            echo "  list                    - 显示所有可用工具\n";
            echo "  run [工具] [参数...]     - 运行指定工具\n";
            echo "  check [文件/目录]       - 一键全面检测\n";
            echo "  batch [目录]           - 批量分析目录\n\n";
            echo "💡 示例:\n";
            echo "  php tools/tool_manager.php list\n";
            echo "  php tools/tool_manager.php run layout check app/view/index/index.html\n";
            echo "  php tools/tool_manager.php check app/view/index/index.html\n";
            echo "  php tools/tool_manager.php batch app/view\n";
    }
}
